"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Slider } from "@/components/ui/slider"
import { Textarea } from "@/components/ui/textarea"
import { User, Palette, Cpu, Brain, Users, Shield, Bell, Monitor, Smartphone, Save, RefreshCw, DollarSign } from "lucide-react"
import TokenUsagePanel from "@/components/token/TokenUsagePanel"

export default function SettingsPage() {
  const [theme, setTheme] = useState("light")
  const [language, setLanguage] = useState("zh-CN")
  const [renderQuality, setRenderQuality] = useState([75])
  const [memoryLimit, setMemoryLimit] = useState([2048])
  const [aiModel, setAiModel] = useState("gpt-4")
  const [notifications, setNotifications] = useState({
    generation: true,
    completion: true,
    errors: true,
    updates: false,
  })

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold mb-2">设置</h1>
            <p className="text-gray-600">配置您的Ignis工作环境</p>
          </div>

          <Tabs defaultValue="interface" className="space-y-6">
            <TabsList className="grid w-full grid-cols-6">
              <TabsTrigger value="interface" className="flex items-center gap-2">
                <Palette className="w-4 h-4" />
                界面
              </TabsTrigger>
              <TabsTrigger value="performance" className="flex items-center gap-2">
                <Cpu className="w-4 h-4" />
                性能
              </TabsTrigger>
              <TabsTrigger value="ai" className="flex items-center gap-2">
                <Brain className="w-4 h-4" />
                AI配置
              </TabsTrigger>
              <TabsTrigger value="account" className="flex items-center gap-2">
                <User className="w-4 h-4" />
                账户
              </TabsTrigger>
              <TabsTrigger value="team" className="flex items-center gap-2">
                <Users className="w-4 h-4" />
                团队
              </TabsTrigger>
              <TabsTrigger value="billing" className="flex items-center gap-2">
                <DollarSign className="w-4 h-4" />
                计费统计
              </TabsTrigger>
            </TabsList>

            {/* Interface Settings */}
            <TabsContent value="interface" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>外观设置</CardTitle>
                  <CardDescription>自定义界面外观和布局偏好</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label>主题</Label>
                      <Select value={theme} onValueChange={setTheme}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="light">浅色主题</SelectItem>
                          <SelectItem value="dark">深色主题</SelectItem>
                          <SelectItem value="auto">跟随系统</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label>语言</Label>
                      <Select value={language} onValueChange={setLanguage}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="zh-CN">简体中文</SelectItem>
                          <SelectItem value="en-US">English</SelectItem>
                          <SelectItem value="ja-JP">日本語</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <Label>布局偏好</Label>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center gap-3">
                          <Monitor className="w-5 h-5 text-gray-600" />
                          <div>
                            <div className="font-medium">紧凑布局</div>
                            <div className="text-sm text-gray-600">适合大屏幕</div>
                          </div>
                        </div>
                        <Switch />
                      </div>

                      <div className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center gap-3">
                          <Smartphone className="w-5 h-5 text-gray-600" />
                          <div>
                            <div className="font-medium">移动优化</div>
                            <div className="text-sm text-gray-600">触屏友好</div>
                          </div>
                        </div>
                        <Switch />
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>通知设置</CardTitle>
                  <CardDescription>管理系统通知和提醒</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <Bell className="w-5 h-5 text-gray-600" />
                      <div>
                        <div className="font-medium">生成进度通知</div>
                        <div className="text-sm text-gray-600">游戏生成过程中的进度更新</div>
                      </div>
                    </div>
                    <Switch
                      checked={notifications.generation}
                      onCheckedChange={(checked) => setNotifications((prev) => ({ ...prev, generation: checked }))}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <Bell className="w-5 h-5 text-gray-600" />
                      <div>
                        <div className="font-medium">完成通知</div>
                        <div className="text-sm text-gray-600">游戏生成完成时通知</div>
                      </div>
                    </div>
                    <Switch
                      checked={notifications.completion}
                      onCheckedChange={(checked) => setNotifications((prev) => ({ ...prev, completion: checked }))}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <Bell className="w-5 h-5 text-gray-600" />
                      <div>
                        <div className="font-medium">错误警告</div>
                        <div className="text-sm text-gray-600">生成过程中的错误提醒</div>
                      </div>
                    </div>
                    <Switch
                      checked={notifications.errors}
                      onCheckedChange={(checked) => setNotifications((prev) => ({ ...prev, errors: checked }))}
                    />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Performance Settings */}
            <TabsContent value="performance" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>渲染设置</CardTitle>
                  <CardDescription>调整3D渲染质量和性能</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <Label>渲染质量</Label>
                      <span className="text-sm text-gray-600">{renderQuality[0]}%</span>
                    </div>
                    <Slider
                      value={renderQuality}
                      onValueChange={setRenderQuality}
                      max={100}
                      min={25}
                      step={25}
                      className="w-full"
                    />
                    <div className="flex justify-between text-xs text-gray-500">
                      <span>低质量</span>
                      <span>中等</span>
                      <span>高质量</span>
                      <span>超高</span>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <Label>内存限制</Label>
                      <span className="text-sm text-gray-600">{memoryLimit[0]}MB</span>
                    </div>
                    <Slider
                      value={memoryLimit}
                      onValueChange={setMemoryLimit}
                      max={8192}
                      min={512}
                      step={512}
                      className="w-full"
                    />
                    <div className="flex justify-between text-xs text-gray-500">
                      <span>512MB</span>
                      <span>2GB</span>
                      <span>4GB</span>
                      <span>8GB</span>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <div className="font-medium">硬件加速</div>
                        <div className="text-sm text-gray-600">使用GPU加速</div>
                      </div>
                      <Switch defaultChecked />
                    </div>

                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <div className="font-medium">缓存优化</div>
                        <div className="text-sm text-gray-600">智能资源缓存</div>
                      </div>
                      <Switch defaultChecked />
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>系统监控</CardTitle>
                  <CardDescription>实时性能监控和资源使用</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>CPU使用率</span>
                        <span>45%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div className="bg-blue-500 h-2 rounded-full" style={{ width: "45%" }}></div>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>内存使用</span>
                        <span>2.1GB</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div className="bg-green-500 h-2 rounded-full" style={{ width: "60%" }}></div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* AI Configuration */}
            <TabsContent value="ai" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>AI模型设置</CardTitle>
                  <CardDescription>配置AI生成参数和模型选择</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label>主要模型</Label>
                      <Select value={aiModel} onValueChange={setAiModel}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="gpt-4">GPT-4 (推荐)</SelectItem>
                          <SelectItem value="gpt-3.5">GPT-3.5 Turbo</SelectItem>
                          <SelectItem value="claude">Claude 3</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label>生成速度</Label>
                      <Select defaultValue="balanced">
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="fast">快速生成</SelectItem>
                          <SelectItem value="balanced">平衡模式</SelectItem>
                          <SelectItem value="quality">质量优先</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <Label>创意度设置</Label>
                    <Slider defaultValue={[70]} max={100} min={0} step={10} className="w-full" />
                    <div className="flex justify-between text-xs text-gray-500">
                      <span>保守</span>
                      <span>平衡</span>
                      <span>创新</span>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>API密钥</Label>
                    <Input type="password" placeholder="输入您的API密钥（可选）" className="font-mono" />
                    <p className="text-xs text-gray-600">使用自己的API密钥可以获得更高的使用限额</p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>生成偏好</CardTitle>
                  <CardDescription>自定义AI生成的默认偏好</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <div className="font-medium">优先3D效果</div>
                        <div className="text-sm text-gray-600">默认生成3D游戏</div>
                      </div>
                      <Switch defaultChecked />
                    </div>

                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <div className="font-medium">包含音效</div>
                        <div className="text-sm text-gray-600">自动生成音效</div>
                      </div>
                      <Switch defaultChecked />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>默认游戏风格</Label>
                    <Textarea
                      placeholder="描述您偏好的游戏风格，AI会在生成时参考这些偏好..."
                      className="min-h-[80px]"
                    />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Account Settings */}
            <TabsContent value="account" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>个人信息</CardTitle>
                  <CardDescription>管理您的账户信息</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label>用户名</Label>
                      <Input defaultValue="游戏创作者" />
                    </div>
                    <div className="space-y-2">
                      <Label>邮箱</Label>
                      <Input defaultValue="<EMAIL>" />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>个人简介</Label>
                    <Textarea placeholder="介绍一下您自己..." defaultValue="热爱游戏创作的设计师" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>安全设置</CardTitle>
                  <CardDescription>保护您的账户安全</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center gap-3">
                      <Shield className="w-5 h-5 text-gray-600" />
                      <div>
                        <div className="font-medium">两步验证</div>
                        <div className="text-sm text-gray-600">增强账户安全性</div>
                      </div>
                    </div>
                    <Switch />
                  </div>

                  <Button variant="outline" className="w-full bg-transparent">
                    修改密码
                  </Button>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>使用统计</CardTitle>
                  <CardDescription>查看您的使用情况</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-6">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-blue-600">15</div>
                      <div className="text-sm text-gray-600">创建的项目</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-600">8.2K</div>
                      <div className="text-sm text-gray-600">总播放次数</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Team Settings */}
            <TabsContent value="team" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>团队协作</CardTitle>
                  <CardDescription>管理团队成员和权限</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium">邀请团队成员</h4>
                      <p className="text-sm text-gray-600">通过邮箱邀请其他用户加入团队</p>
                    </div>
                    <Button>发送邀请</Button>
                  </div>

                  <div className="border rounded-lg p-4">
                    <h5 className="font-medium mb-3">当前成员</h5>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                            <User className="w-4 h-4 text-blue-600" />
                          </div>
                          <div>
                            <div className="font-medium">您</div>
                            <div className="text-sm text-gray-600">管理员</div>
                          </div>
                        </div>
                        <span className="text-sm text-gray-500">所有者</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>权限设置</CardTitle>
                  <CardDescription>配置团队成员的访问权限</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <div className="font-medium">项目创建权限</div>
                      <div className="text-sm text-gray-600">允许成员创建新项目</div>
                    </div>
                    <Switch defaultChecked />
                  </div>

                  <div className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <div className="font-medium">项目编辑权限</div>
                      <div className="text-sm text-gray-600">允许成员编辑现有项目</div>
                    </div>
                    <Switch defaultChecked />
                  </div>

                  <div className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <div className="font-medium">导出权限</div>
                      <div className="text-sm text-gray-600">允许成员导出项目文件</div>
                    </div>
                    <Switch />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Billing & Token Usage Statistics */}
            <TabsContent value="billing" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Token使用与计费统计</CardTitle>
                  <CardDescription>监控AI模型调用的token使用情况和成本</CardDescription>
                </CardHeader>
                <CardContent>
                  <TokenUsagePanel />
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          {/* Save Button */}
          <div className="flex justify-end gap-4 pt-6">
            <Button variant="outline">
              <RefreshCw className="w-4 h-4 mr-2" />
              重置
            </Button>
            <Button>
              <Save className="w-4 h-4 mr-2" />
              保存设置
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
