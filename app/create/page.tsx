"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import {
  Upload,
  Mic,
  Sparkles,
  ImageIcon,
  Video,
  Box,
  Gamepad2,
  Smartphone,
  Monitor,
  Lightbulb,
  Wand2,
} from "lucide-react"

export default function CreatePage() {
  const [description, setDescription] = useState("")
  const [selectedType, setSelectedType] = useState("")
  const [selectedPlatform, setSelectedPlatform] = useState("")
  const [isRecording, setIsRecording] = useState(false)
  const [isGenerating, setIsGenerating] = useState(false)
  const [projectName, setProjectName] = useState("")

  const gameTypes = [
    { id: "runner", name: "跑酷游戏", icon: "🏃", description: "经典跑酷玩法" },
    { id: "puzzle", name: "消除游戏", icon: "🧩", description: "三消益智类" },
    { id: "tower", name: "塔防游戏", icon: "🏰", description: "策略防守类" },
    { id: "racing", name: "竞速游戏", icon: "🏎️", description: "赛车竞速类" },
    { id: "rpg", name: "角色扮演", icon: "⚔️", description: "RPG冒险类" },
    { id: "casual", name: "休闲游戏", icon: "🎯", description: "轻松休闲类" },
  ]

  const platforms = [
    { id: "mobile", name: "移动端", icon: Smartphone, description: "手机/平板" },
    { id: "web", name: "网页端", icon: Monitor, description: "浏览器" },
    { id: "both", name: "全平台", icon: Gamepad2, description: "移动+网页" },
  ]

  const templates = [
    {
      id: 1,
      title: "超级跑酷",
      description: "一个角色在城市中跑酷，需要跳跃障碍物，收集金币，有加速道具和特殊技能",
      type: "跑酷游戏",
      tags: ["3D", "动作", "收集"],
    },
    {
      id: 2,
      title: "魔法消除",
      description: "玩家需要消除相同颜色的魔法宝石，达到目标分数，有特殊道具和连击效果",
      type: "消除游戏",
      tags: ["2D", "益智", "策略"],
    },
    {
      id: 3,
      title: "守护城堡",
      description: "玩家建造防御塔来阻止敌人攻击城堡，有多种塔类型和升级系统",
      type: "塔防游戏",
      tags: ["策略", "建造", "升级"],
    },
  ]

  const handleVoiceInput = () => {
    setIsRecording(!isRecording)
    // 这里会集成语音识别功能
  }

  const handleTemplateInsert = (template: (typeof templates)[0]) => {
    setDescription(template.description)
    setSelectedType(template.type)
  }

  const handleGenerate = async () => {
    if (!description.trim()) return

    setIsGenerating(true)

    try {
      // 创建项目
      const projectResponse = await fetch('/api/projects', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          name: projectName || `游戏项目 ${new Date().toLocaleString()}`,
          description: description.trim(),
          gameType: selectedType,
          platform: selectedPlatform,
          config: {
            enableOptimization: true,
            qualityThreshold: 75,
            maxIterations: 3
          }
        })
      })

      if (!projectResponse.ok) {
        throw new Error('创建项目失败')
      }

      const project = await projectResponse.json()
      console.log('项目创建成功:', project)

      // 跳转到canvas页面并传递项目ID
      window.location.href = `/canvas?projectId=${project.id}`

    } catch (error) {
      console.error('创建项目失败:', error)
      alert('创建项目失败，请重试')
      setIsGenerating(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <div className="text-center mb-8">
            <h1 className="text-3xl md:text-4xl font-bold mb-4">创建新项目</h1>
            <p className="text-lg text-gray-600">用自然语言描述您的游戏创意，AI将为您生成完整的可玩广告</p>
          </div>

          <div className="grid lg:grid-cols-3 gap-8">
            {/* Main Input Area */}
            <div className="lg:col-span-2 space-y-6">
              {/* Description Input */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Lightbulb className="w-5 h-5 text-yellow-500" />
                    游戏创意描述
                  </CardTitle>
                  <CardDescription>详细描述您想要的游戏玩法、视觉风格和交互方式</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* 项目名称 */}
                  <div className="space-y-2">
                    <Label htmlFor="projectName">项目名称（可选）</Label>
                    <Input
                      id="projectName"
                      placeholder="为您的游戏项目起个名字..."
                      value={projectName}
                      onChange={(e) => setProjectName(e.target.value)}
                    />
                  </div>

                  <div className="relative">
                    <Textarea
                      placeholder="例如：我想要一个3D跑酷游戏，角色是一只可爱的小猫，在彩虹城市中奔跑，需要跳跃障碍物，收集鱼干，有飞行道具和磁铁效果..."
                      value={description}
                      onChange={(e) => setDescription(e.target.value)}
                      className="min-h-[120px] text-base resize-none"
                    />
                    <div className="absolute bottom-3 right-3 flex gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={handleVoiceInput}
                        className={isRecording ? "bg-red-50 border-red-200" : ""}
                      >
                        <Mic className={`w-4 h-4 ${isRecording ? "text-red-500" : ""}`} />
                      </Button>
                      <Button size="sm" variant="outline">
                        <Wand2 className="w-4 h-4" />
                        AI优化
                      </Button>
                    </div>
                  </div>

                  {description && (
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                      <p className="text-sm text-blue-700 font-medium mb-1">AI分析建议：</p>
                      <p className="text-sm text-blue-600">
                        检测到跑酷游戏类型，建议添加更多关于角色技能和关卡设计的描述
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Reference Materials */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Upload className="w-5 h-5 text-blue-500" />
                    参考资料
                  </CardTitle>
                  <CardDescription>上传图片、视频或3D模型作为参考（可选）</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-3 gap-4">
                    <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-400 transition-colors cursor-pointer">
                      <ImageIcon className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                      <p className="text-sm text-gray-600">上传图片</p>
                    </div>
                    <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-400 transition-colors cursor-pointer">
                      <Video className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                      <p className="text-sm text-gray-600">上传视频</p>
                    </div>
                    <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-400 transition-colors cursor-pointer">
                      <Box className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                      <p className="text-sm text-gray-600">3D模型</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Quick Configuration */}
              <Card>
                <CardHeader>
                  <CardTitle>快速配置</CardTitle>
                  <CardDescription>选择游戏类型和目标平台</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Game Type */}
                  <div>
                    <Label className="text-base font-medium mb-3 block">游戏类型</Label>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                      {gameTypes.map((type) => (
                        <div
                          key={type.id}
                          onClick={() => setSelectedType(type.name)}
                          className={`p-3 border rounded-lg cursor-pointer transition-all hover:shadow-md ${
                            selectedType === type.name
                              ? "border-blue-500 bg-blue-50"
                              : "border-gray-200 hover:border-gray-300"
                          }`}
                        >
                          <div className="text-2xl mb-1">{type.icon}</div>
                          <div className="font-medium text-sm">{type.name}</div>
                          <div className="text-xs text-gray-500">{type.description}</div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Platform */}
                  <div>
                    <Label className="text-base font-medium mb-3 block">目标平台</Label>
                    <div className="grid grid-cols-3 gap-3">
                      {platforms.map((platform) => {
                        const Icon = platform.icon
                        return (
                          <div
                            key={platform.id}
                            onClick={() => setSelectedPlatform(platform.name)}
                            className={`p-4 border rounded-lg cursor-pointer transition-all hover:shadow-md text-center ${
                              selectedPlatform === platform.name
                                ? "border-blue-500 bg-blue-50"
                                : "border-gray-200 hover:border-gray-300"
                            }`}
                          >
                            <Icon className="w-6 h-6 mx-auto mb-2 text-gray-600" />
                            <div className="font-medium text-sm">{platform.name}</div>
                            <div className="text-xs text-gray-500">{platform.description}</div>
                          </div>
                        )
                      })}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Generate Button */}
              <div className="flex justify-center">
                <Button
                  size="lg"
                  onClick={handleGenerate}
                  disabled={!description.trim() || isGenerating}
                  className="px-8 py-3 text-lg"
                >
                  {isGenerating ? (
                    <>
                      <div className="w-5 h-5 mr-2 animate-spin rounded-full border-2 border-white border-t-transparent" />
                      创建项目中...
                    </>
                  ) : (
                    <>
                      <Sparkles className="w-5 h-5 mr-2" />
                      开始生成游戏
                    </>
                  )}
                </Button>
              </div>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Templates */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">模板快速插入</CardTitle>
                  <CardDescription>点击模板快速填入描述</CardDescription>
                </CardHeader>
                <CardContent className="space-y-3">
                  {templates.map((template) => (
                    <div
                      key={template.id}
                      onClick={() => handleTemplateInsert(template)}
                      className="p-3 border rounded-lg cursor-pointer hover:shadow-md transition-shadow"
                    >
                      <div className="font-medium text-sm mb-1">{template.title}</div>
                      <div className="text-xs text-gray-600 mb-2 line-clamp-2">{template.description}</div>
                      <div className="flex flex-wrap gap-1">
                        {template.tags.map((tag) => (
                          <Badge key={tag} variant="secondary" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>

              {/* Tips */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">创作提示</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3 text-sm">
                  <div className="flex gap-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                    <p>详细描述游戏玩法和交互方式</p>
                  </div>
                  <div className="flex gap-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                    <p>说明视觉风格和美术需求</p>
                  </div>
                  <div className="flex gap-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                    <p>提及特殊效果和音效需求</p>
                  </div>
                  <div className="flex gap-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                    <p>上传参考图片会提高生成质量</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
