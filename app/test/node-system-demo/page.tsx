/**
 * NodeSystemDemo - 重构后的节点系统演示页面
 * 使用Three.js替代Babylon.js，模块化组件设计
 */

'use client';

import React from 'react';

// 组件导入
import { NodeSystemProvider } from './contexts/NodeSystemContext';
import AgentChatPanel from './components/AgentChatPanel';
import ThreeSceneViewer from './components/ThreeSceneViewer';
import NodePropertiesPanel from './components/NodePropertiesPanel';
import ScriptManager from './components/ScriptManager';

/**
 * 节点系统演示主页面
 * 展示Three.js 3D场景、AI Agent对话、节点属性编辑和脚本管理功能
 */
export default function NodeSystemDemo(): React.JSX.Element {
  return (
    <NodeSystemProvider>
      <div className="page-height bg-gray-50 text-gray-900 flex">
        {/* 左侧：Agent对话区域 */}
        <AgentChatPanel />

        {/* 中间：3D视图和脚本区域 */}
        <div className="flex-1 flex flex-col">
          {/* Three.js 3D场景 */}
          <ThreeSceneViewer />
          
          {/* 脚本管理区域 */}
          <ScriptManager />
        </div>

        {/* 右侧：节点属性面板 */}
        <NodePropertiesPanel />
      </div>
    </NodeSystemProvider>
  );
}
