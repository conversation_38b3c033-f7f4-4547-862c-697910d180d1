/**
 * NodeSystemContext - 节点系统状态管理
 * 提供全局状态管理和共享逻辑
 */

'use client';

import React, { createContext, useContext, useState, useRef, useCallback, ReactNode } from 'react';
import * as THREE from 'three';
import { ThreeCanvasContext } from '../../../../src/three/components/ThreeCanvas';
import { FBXModel } from '../../../../src/three/loaders/FBXModelLoader';
import { GameNodeProperties, GameNodeType, AnimationInfo, ModelInfo, MaterialInfo } from '../../../../src/types/NodeTypes';
import { NodeCommunicationService } from '../../../../src/utils/NodeCommunicationService';
import { Checkpoint, CheckpointOperationResult, CreateCheckpointOptions, RollbackCheckpointOptions } from '../../../../src/types/CheckpointTypes';

// 类型定义
export interface AgentMessage {
  id: string;
  type: 'user' | 'agent' | 'system';
  content: string;
  timestamp: Date;
  isStreaming?: boolean;
}

export interface ScriptFile {
  id: string;
  name: string;
  content: string;
  nodeId?: string;
  createdAt: Date;
  lastModified: Date;
  isActive: boolean;
  isPreview?: boolean;
  metadata: {
    description: string;
    targetNodeTypes: string[];
    dependencies: string[];
    functionType?: string;
  };
}

export interface NodeSystemState {
  // 节点系统状态
  nodes: GameNodeProperties[];
  selectedNode: GameNodeProperties | null;
  nodeStats: { total: number; byType: Record<string, number> };
  
  // Agent对话状态
  agentMessages: AgentMessage[];
  agentInput: string;
  isAgentProcessing: boolean;
  isLoadingHistory: boolean;
  currentSessionId: string;
  
  // 脚本管理状态
  scriptFiles: ScriptFile[];
  selectedScript: ScriptFile | null;
  editingScriptId: string | null;
  editingScriptName: string;
  
  // 模型上传状态
  isUploadingModel: boolean;
  uploadError: string | null;
  isDragOver: boolean;
  
  // Three.js相关状态
  threeContext: ThreeCanvasContext | null;
  loadedModels: Map<string, FBXModel>;

  // 动画管理状态
  uploadedAnimations: Map<string, AnimationInfo[]>; // nodeId -> animations
  currentPlayingAnimation: Map<string, string>; // nodeId -> animationId
  previewingAnimation: Map<string, string>; // nodeId -> animationId
  isUploadingAnimation: boolean;
  animationError: string | null;

  // 模型管理状态
  uploadedModels: Map<string, ModelInfo[]>; // nodeId -> models
  currentModel: Map<string, string>; // nodeId -> modelId

  // 材质管理状态
  uploadedMaterials: Map<string, MaterialInfo[]>; // nodeId -> materials
  currentMaterial: Map<string, string>; // nodeId -> materialId

  // 临时输入状态
  tempInputValues: {
    positionX?: string;
    positionY?: string;
    positionZ?: string;
    rotationX?: string;
    rotationY?: string;
    rotationZ?: string;
    scaleX?: string;
    scaleY?: string;
    scaleZ?: string;
    fov?: string;
    viewLocked?: string; // 视角锁定状态，存储为字符串'true'/'false'
  };

  // Checkpoint版本管理状态
  checkpoints: Checkpoint[];
  currentCheckpointId: string | null;
  isCreatingCheckpoint: boolean;
  isRollingBack: boolean;
  checkpointError: string | null;
}

export interface NodeSystemActions {
  // 节点操作
  setNodes: (nodes: GameNodeProperties[]) => void;
  setSelectedNode: (node: GameNodeProperties | null) => void;
  setNodeStats: (stats: { total: number; byType: Record<string, number> }) => void;
  
  // Agent操作
  setAgentMessages: (messages: AgentMessage[]) => void;
  setAgentInput: (input: string) => void;
  setIsAgentProcessing: (processing: boolean) => void;
  setIsLoadingHistory: (loading: boolean) => void;
  setCurrentSessionId: (sessionId: string) => void;
  addAgentMessage: (message: AgentMessage) => void;
  
  // 脚本操作
  setScriptFiles: (scripts: ScriptFile[] | ((prevFiles: ScriptFile[]) => ScriptFile[])) => void;
  setSelectedScript: (script: ScriptFile | null) => void;
  setEditingScriptId: (id: string | null) => void;
  setEditingScriptName: (name: string) => void;
  addScriptFile: (script: ScriptFile) => void;
  updateScriptFile: (id: string, updates: Partial<ScriptFile>) => void;
  removeScriptFile: (id: string) => void;
  
  // 模型上传操作
  setIsUploadingModel: (uploading: boolean) => void;
  setUploadError: (error: string | null) => void;
  setIsDragOver: (dragOver: boolean) => void;
  
  // Three.js操作
  setThreeContext: (context: ThreeCanvasContext | null) => void;
  addLoadedModel: (id: string, model: FBXModel) => void;
  removeLoadedModel: (id: string) => void;

  // 动画管理操作
  addAnimation: (nodeId: string, animation: AnimationInfo) => void;
  removeAnimation: (nodeId: string, animationId: string) => void;
  setCurrentPlayingAnimation: (nodeId: string, animationId: string | null) => void;
  setPreviewingAnimation: (nodeId: string, animationId: string | null) => void;
  setIsUploadingAnimation: (uploading: boolean) => void;
  setAnimationError: (error: string | null) => void;
  getNodeAnimations: (nodeId: string) => AnimationInfo[];

  // 模型管理操作
  addModel: (nodeId: string, model: ModelInfo) => void;
  removeModel: (nodeId: string, modelId: string) => void;
  setCurrentModel: (nodeId: string, modelId: string | null) => void;
  getNodeModels: (nodeId: string) => ModelInfo[];

  // 材质管理操作
  addMaterial: (nodeId: string, material: MaterialInfo) => void;
  removeMaterial: (nodeId: string, materialId: string) => void;
  setCurrentMaterial: (nodeId: string, materialId: string | null) => void;
  getNodeMaterials: (nodeId: string) => MaterialInfo[];

  // 临时输入操作
  setTempInputValues: (values: Partial<NodeSystemState['tempInputValues']>) => void;

  // Checkpoint操作
  setCheckpoints: (checkpoints: Checkpoint[]) => void;
  setCurrentCheckpointId: (id: string | null) => void;
  setIsCreatingCheckpoint: (creating: boolean) => void;
  setIsRollingBack: (rolling: boolean) => void;
  setCheckpointError: (error: string | null) => void;
  createCheckpoint: (messageId: string, description: string, userAction: string, generatedScripts?: string[]) => Promise<Checkpoint | null>;
  rollbackToCheckpoint: (checkpointId: string) => Promise<boolean>;
  loadCheckpoints: () => Promise<void>;
  recoverFromError: () => Promise<void>;

  // 工具方法
  generateSessionId: () => string;
  scrollToBottom: () => void;
}

const NodeSystemContext = createContext<(NodeSystemState & NodeSystemActions) | null>(null);

export const useNodeSystem = () => {
  const context = useContext(NodeSystemContext);
  if (!context) {
    throw new Error('useNodeSystem must be used within a NodeSystemProvider');
  }
  return context;
};

interface NodeSystemProviderProps {
  children: ReactNode;
}

export const NodeSystemProvider: React.FC<NodeSystemProviderProps> = ({ children }) => {
  // 状态定义
  const [nodes, setNodes] = useState<GameNodeProperties[]>([]);
  const [selectedNode, setSelectedNode] = useState<GameNodeProperties | null>(null);
  const [nodeStats, setNodeStats] = useState<{ total: number; byType: Record<string, number> }>({ 
    total: 0, 
    byType: {} 
  });
  
  const [agentMessages, setAgentMessages] = useState<AgentMessage[]>([]);
  const [agentInput, setAgentInput] = useState<string>('');
  const [isAgentProcessing, setIsAgentProcessing] = useState<boolean>(false);
  const [isLoadingHistory, setIsLoadingHistory] = useState<boolean>(true);
  const [currentSessionId, setCurrentSessionId] = useState<string>('');
  
  const [scriptFiles, setScriptFiles] = useState<ScriptFile[]>([]);
  const [selectedScript, setSelectedScript] = useState<ScriptFile | null>(null);
  const [editingScriptId, setEditingScriptId] = useState<string | null>(null);
  const [editingScriptName, setEditingScriptName] = useState<string>('');
  
  const [isUploadingModel, setIsUploadingModel] = useState<boolean>(false);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const [isDragOver, setIsDragOver] = useState<boolean>(false);
  
  const [threeContext, setThreeContext] = useState<ThreeCanvasContext | null>(null);
  const [loadedModels] = useState<Map<string, FBXModel>>(new Map());

  // 动画管理状态
  const [uploadedAnimations] = useState<Map<string, AnimationInfo[]>>(new Map());
  const [currentPlayingAnimation] = useState<Map<string, string>>(new Map());
  const [previewingAnimation] = useState<Map<string, string>>(new Map());
  const [isUploadingAnimation, setIsUploadingAnimation] = useState<boolean>(false);
  const [animationError, setAnimationError] = useState<string | null>(null);
  const [animationUpdateTrigger, setAnimationUpdateTrigger] = useState<number>(0);

  // 模型管理状态
  const [uploadedModels] = useState<Map<string, ModelInfo[]>>(new Map());
  const [currentModel] = useState<Map<string, string>>(new Map());
  const [modelUpdateTrigger, setModelUpdateTrigger] = useState<number>(0);

  // 材质管理状态
  const [uploadedMaterials] = useState<Map<string, MaterialInfo[]>>(new Map());
  const [currentMaterial] = useState<Map<string, string>>(new Map());
  const [materialUpdateTrigger, setMaterialUpdateTrigger] = useState<number>(0);

  const [tempInputValues, setTempInputValues] = useState<NodeSystemState['tempInputValues']>({});

  // Checkpoint版本管理状态
  const [checkpoints, setCheckpoints] = useState<Checkpoint[]>([]);
  const [currentCheckpointId, setCurrentCheckpointId] = useState<string | null>(null);
  const [isCreatingCheckpoint, setIsCreatingCheckpoint] = useState<boolean>(false);
  const [isRollingBack, setIsRollingBack] = useState<boolean>(false);
  const [checkpointError, setCheckpointError] = useState<string | null>(null);

  // 引用
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const commServiceRef = useRef<NodeCommunicationService | null>(null);
  
  // 工具方法
  const generateSessionId = useCallback((): string => {
    const today = new Date().toISOString().split('T')[0];
    return `session_${today}`;
  }, []);
  
  const scrollToBottom = useCallback(() => {
    if (messagesContainerRef.current) {
      messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;
    }
  }, []);
  
  // Agent消息操作
  const addAgentMessage = useCallback((message: AgentMessage) => {
    setAgentMessages(prev => [...prev, message]);
  }, []);
  
  // 脚本操作
  const addScriptFile = useCallback((script: ScriptFile) => {
    setScriptFiles(prev => [...prev, script]);
  }, []);
  
  const updateScriptFile = useCallback((id: string, updates: Partial<ScriptFile>) => {
    setScriptFiles(prev => prev.map(script => 
      script.id === id ? { ...script, ...updates } : script
    ));
  }, []);
  
  const removeScriptFile = useCallback((id: string) => {
    setScriptFiles(prev => prev.filter(script => script.id !== id));
    if (selectedScript?.id === id) {
      setSelectedScript(null);
    }
  }, [selectedScript]);
  
  // 模型操作
  const addLoadedModel = useCallback((id: string, model: FBXModel) => {
    loadedModels.set(id, model);
  }, [loadedModels]);
  
  const removeLoadedModel = useCallback((id: string) => {
    const model = loadedModels.get(id);
    if (model) {
      // 清理模型资源
      loadedModels.delete(id);
    }
  }, [loadedModels]);
  
  // 动画管理操作
  const addAnimation = useCallback((nodeId: string, animation: AnimationInfo) => {
    const nodeAnimations = uploadedAnimations.get(nodeId) || [];

    // 检查是否已存在相同ID的动画，避免重复添加
    const existingIndex = nodeAnimations.findIndex(existing => existing.id === animation.id);

    if (existingIndex >= 0) {
      // 如果已存在，更新现有动画
      const updatedAnimations = [...nodeAnimations];
      updatedAnimations[existingIndex] = animation;
      uploadedAnimations.set(nodeId, updatedAnimations);
      console.log(`[NodeSystemContext] 更新动画: ${animation.name} (${animation.id})`);
    } else {
      // 如果不存在，添加新动画
      uploadedAnimations.set(nodeId, [...nodeAnimations, animation]);
      console.log(`[NodeSystemContext] 添加动画: ${animation.name} (${animation.id})`);
    }

    // 触发重新渲染
    setAnimationUpdateTrigger(prev => prev + 1);
  }, [uploadedAnimations]);

  const removeAnimation = useCallback((nodeId: string, animationId: string) => {
    const nodeAnimations = uploadedAnimations.get(nodeId) || [];
    uploadedAnimations.set(nodeId, nodeAnimations.filter(anim => anim.id !== animationId));
    // 触发重新渲染
    setAnimationUpdateTrigger(prev => prev + 1);
  }, [uploadedAnimations]);

  const setCurrentPlayingAnimation = useCallback((nodeId: string, animationId: string | null) => {
    if (animationId) {
      currentPlayingAnimation.set(nodeId, animationId);
    } else {
      currentPlayingAnimation.delete(nodeId);
    }
  }, [currentPlayingAnimation]);

  const setPreviewingAnimation = useCallback((nodeId: string, animationId: string | null) => {
    if (animationId) {
      previewingAnimation.set(nodeId, animationId);
    } else {
      previewingAnimation.delete(nodeId);
    }
  }, [previewingAnimation]);

  const getNodeAnimations = useCallback((nodeId: string): AnimationInfo[] => {
    // 依赖animationUpdateTrigger来确保重新渲染
    return uploadedAnimations.get(nodeId) || [];
  }, [uploadedAnimations, animationUpdateTrigger]);

  // 模型管理操作
  const addModel = useCallback((nodeId: string, model: ModelInfo) => {
    const nodeModels = uploadedModels.get(nodeId) || [];

    // 检查是否已存在相同ID的模型，避免重复添加
    const existingIndex = nodeModels.findIndex(existing => existing.id === model.id);

    if (existingIndex >= 0) {
      // 如果已存在，更新现有模型
      const updatedModels = [...nodeModels];
      updatedModels[existingIndex] = model;
      uploadedModels.set(nodeId, updatedModels);
      console.log(`[NodeSystemContext] 更新模型: ${model.name} (${model.id})`);
    } else {
      // 如果不存在，添加新模型
      uploadedModels.set(nodeId, [...nodeModels, model]);
      console.log(`[NodeSystemContext] 添加模型: ${model.name} (${model.id})`);
    }

    setModelUpdateTrigger(prev => prev + 1);
  }, [uploadedModels]);

  const removeModel = useCallback((nodeId: string, modelId: string) => {
    const nodeModels = uploadedModels.get(nodeId) || [];
    uploadedModels.set(nodeId, nodeModels.filter(model => model.id !== modelId));
    setModelUpdateTrigger(prev => prev + 1);
  }, [uploadedModels]);

  const setCurrentModel = useCallback((nodeId: string, modelId: string | null) => {
    if (modelId) {
      currentModel.set(nodeId, modelId);
    } else {
      currentModel.delete(nodeId);
    }
  }, [currentModel]);

  const getNodeModels = useCallback((nodeId: string): ModelInfo[] => {
    return uploadedModels.get(nodeId) || [];
  }, [uploadedModels, modelUpdateTrigger]);

  // 材质管理操作
  const addMaterial = useCallback((nodeId: string, material: MaterialInfo) => {
    const nodeMaterials = uploadedMaterials.get(nodeId) || [];

    // 检查是否已存在相同ID的材质，避免重复添加
    const existingIndex = nodeMaterials.findIndex(existing => existing.id === material.id);

    if (existingIndex >= 0) {
      // 如果已存在，更新现有材质
      const updatedMaterials = [...nodeMaterials];
      updatedMaterials[existingIndex] = material;
      uploadedMaterials.set(nodeId, updatedMaterials);
      console.log(`[NodeSystemContext] 更新材质: ${material.name} (${material.id})`);
    } else {
      // 如果不存在，添加新材质
      uploadedMaterials.set(nodeId, [...nodeMaterials, material]);
      console.log(`[NodeSystemContext] 添加材质: ${material.name} (${material.id})`);
    }

    setMaterialUpdateTrigger(prev => prev + 1);
  }, [uploadedMaterials]);

  const removeMaterial = useCallback((nodeId: string, materialId: string) => {
    const nodeMaterials = uploadedMaterials.get(nodeId) || [];
    uploadedMaterials.set(nodeId, nodeMaterials.filter(material => material.id !== materialId));
    setMaterialUpdateTrigger(prev => prev + 1);
  }, [uploadedMaterials]);

  const setCurrentMaterial = useCallback((nodeId: string, materialId: string | null) => {
    if (materialId) {
      currentMaterial.set(nodeId, materialId);
    } else {
      currentMaterial.delete(nodeId);
    }
  }, [currentMaterial]);

  const getNodeMaterials = useCallback((nodeId: string): MaterialInfo[] => {
    return uploadedMaterials.get(nodeId) || [];
  }, [uploadedMaterials, materialUpdateTrigger]);

  // 临时输入值更新
  const updateTempInputValues = useCallback((values: Partial<NodeSystemState['tempInputValues']>) => {
    // 如果传入的是空对象，则完全重置tempInputValues
    if (Object.keys(values).length === 0) {
      setTempInputValues({});
    } else {
      setTempInputValues(prev => ({ ...prev, ...values }));
    }
  }, []);

  // Checkpoint操作方法
  const createCheckpoint = useCallback(async (
    messageId: string,
    description: string,
    userAction: string,
    generatedScripts: string[] = []
  ): Promise<Checkpoint | null> => {
    // 防止并发创建
    if (isCreatingCheckpoint) {
      console.warn('[Checkpoint] 正在创建checkpoint，跳过重复请求');
      return null;
    }

    try {
      setIsCreatingCheckpoint(true);
      setCheckpointError(null);

      // 构建当前状态快照
      const currentState = {
        scripts: [], // 将从persistent-features.json获取
        scene: {
          nodes,
          selectedNode,
          nodeStats
        },
        messages: agentMessages,
        session: {
          sessionId: currentSessionId,
          messageCount: agentMessages.length
        }
      };

      // 获取当前的persistent features
      try {
        const persistentResponse = await fetch('/api/persistent-features');
        if (persistentResponse.ok) {
          const persistentResult = await persistentResponse.json();
          if (persistentResult.success) {
            currentState.scripts = persistentResult.data.features;
          }
        }
      } catch (error) {
        console.warn('[Checkpoint] 获取persistent features失败:', error);
      }

      // 调用API创建checkpoint
      const response = await fetch('/api/checkpoints', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'create',
          messageId,
          description,
          userAction,
          generatedScripts,
          state: currentState
        })
      });

      const result = await response.json();

      if (result.success && result.data.checkpoint) {
        const newCheckpoint = result.data.checkpoint;
        setCheckpoints(prev => [...prev, newCheckpoint]);
        setCurrentCheckpointId(newCheckpoint.id);
        console.log('[Checkpoint] 创建成功:', newCheckpoint.version);
        return newCheckpoint;
      } else {
        throw new Error(result.error || 'Checkpoint创建失败');
      }
    } catch (error) {
      console.error('[Checkpoint] 创建失败:', error);
      setCheckpointError(error instanceof Error ? error.message : '创建checkpoint失败');
      return null;
    } finally {
      setIsCreatingCheckpoint(false);
    }
  }, [nodes, selectedNode, nodeStats, agentMessages, currentSessionId]);

  const rollbackToCheckpoint = useCallback(async (checkpointId: string): Promise<boolean> => {
    // 防止并发回滚
    if (isRollingBack) {
      console.warn('[Checkpoint] 正在执行回滚操作，跳过重复请求');
      return false;
    }

    // 防止在创建checkpoint时回滚
    if (isCreatingCheckpoint) {
      console.warn('[Checkpoint] 正在创建checkpoint，无法执行回滚');
      setCheckpointError('正在创建版本，请稍后再试');
      return false;
    }

    try {
      setIsRollingBack(true);
      setCheckpointError(null);

      // 调用API回滚
      const response = await fetch('/api/checkpoints', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'rollback',
          checkpointId,
          clearSubsequent: true
        })
      });

      const result = await response.json();

      if (result.success && result.data.restoredState) {
        const restoredState = result.data.restoredState;

        // 恢复状态
        setNodes(restoredState.scene.nodes);
        setSelectedNode(restoredState.scene.selectedNode);
        setNodeStats(restoredState.scene.nodeStats);

        // 恢复消息状态 - 只保留到checkpoint为止的消息，并转换timestamp为Date对象
        console.log('[Checkpoint] 恢复消息状态，从', agentMessages.length, '条消息恢复到', restoredState.messages.length, '条');
        const messagesWithDates = restoredState.messages.map((msg: Record<string, unknown>) => ({
          ...msg,
          timestamp: new Date(msg.timestamp as string)
        })) as AgentMessage[];
        setAgentMessages(messagesWithDates);
        setCurrentSessionId(restoredState.session.sessionId);
        setCurrentCheckpointId(checkpointId);

        // 恢复脚本状态
        try {
          // 1. 清除当前的persistent features
          const currentFeaturesResponse = await fetch('/api/persistent-features');
          if (currentFeaturesResponse.ok) {
            const currentResult = await currentFeaturesResponse.json();
            if (currentResult.success && currentResult.data.features) {
              // 删除所有当前的features
              for (const feature of currentResult.data.features) {
                await fetch(`/api/persistent-features?featureId=${feature.id}`, {
                  method: 'DELETE'
                });
              }
            }
          }

          // 2. 恢复checkpoint中的脚本
          for (const script of restoredState.scripts) {
            await fetch('/api/persistent-features', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify(script)
            });
          }

          // 3. 更新ScriptManager中的脚本文件状态
          const restoredScriptFiles = restoredState.scripts.map((script: Record<string, unknown>) => ({
            id: script.id as string,
            name: script.name as string,
            content: script.scriptContent as string,
            nodeId: undefined,
            createdAt: new Date(script.addedAt as string),
            lastModified: new Date(script.addedAt as string),
            isActive: script.isActive as boolean,
            isPreview: false,
            metadata: {
              description: script.description as string,
              targetNodeTypes: ['mesh'] as string[],
              dependencies: ['three'] as string[],
              functionType: 'utility' as string
            }
          }));

          setScriptFiles(restoredScriptFiles);
          setSelectedScript(null);

          // 4. 清理当前场景中的脚本效果，然后重新执行活跃的脚本
          setTimeout(() => {
            // 首先清理所有脚本效果
            const clearEvent = new CustomEvent('clearAllScriptEffects');
            window.dispatchEvent(clearEvent);

            // 然后重新执行活跃的脚本
            restoredScriptFiles.forEach((scriptFile: Record<string, unknown>) => {
              if (scriptFile.isActive) {
                const executeEvent = new CustomEvent('executeScript', {
                  detail: { scriptId: scriptFile.id }
                });
                window.dispatchEvent(executeEvent);
                console.log('[Checkpoint] 重新执行脚本:', scriptFile.name);
              }
            });
          }, 500); // 延迟执行，确保组件状态已更新

        } catch (error) {
          console.warn('[Checkpoint] 恢复脚本状态失败:', error);
        }

        // 更新checkpoint列表
        await loadCheckpoints();

        console.log('[Checkpoint] 回滚成功:', checkpointId);
        return true;
      } else {
        throw new Error(result.error || '回滚失败');
      }
    } catch (error) {
      console.error('[Checkpoint] 回滚失败:', error);
      setCheckpointError(error instanceof Error ? error.message : '回滚失败');
      return false;
    } finally {
      setIsRollingBack(false);
    }
  }, []);

  const loadCheckpoints = useCallback(async (): Promise<void> => {
    try {
      const response = await fetch('/api/checkpoints');
      const result = await response.json();

      if (result.success) {
        setCheckpoints(result.data.checkpoints);
        setCurrentCheckpointId(result.data.currentCheckpointId);
        setCheckpointError(null);
      } else {
        console.warn('[Checkpoint] 加载失败:', result.error);
        setCheckpointError(`加载版本列表失败：${result.error}`);
      }
    } catch (error) {
      console.error('[Checkpoint] 加载失败:', error);
      setCheckpointError('网络错误，无法加载版本列表');
      // 降级处理：使用本地存储的checkpoint信息
      try {
        const localCheckpoints = localStorage.getItem('fallback_checkpoints');
        if (localCheckpoints) {
          const parsed = JSON.parse(localCheckpoints);
          setCheckpoints(parsed.checkpoints || []);
          console.log('[Checkpoint] 使用本地备份数据');
        }
      } catch (localError) {
        console.warn('[Checkpoint] 本地备份数据也无法使用:', localError);
      }
    }
  }, []);

  // 错误恢复函数
  const recoverFromError = useCallback(async (): Promise<void> => {
    console.log('[Checkpoint] 尝试错误恢复...');
    setCheckpointError(null);
    setIsCreatingCheckpoint(false);
    setIsRollingBack(false);

    // 重新加载checkpoint列表
    await loadCheckpoints();

    // 保存当前状态到本地作为备份
    try {
      const backupData = {
        checkpoints,
        timestamp: new Date().toISOString()
      };
      localStorage.setItem('fallback_checkpoints', JSON.stringify(backupData));
    } catch (error) {
      console.warn('[Checkpoint] 无法保存本地备份:', error);
    }
  }, [checkpoints, loadCheckpoints]);
  
  const contextValue: NodeSystemState & NodeSystemActions = {
    // 状态
    nodes,
    selectedNode,
    nodeStats,
    agentMessages,
    agentInput,
    isAgentProcessing,
    isLoadingHistory,
    currentSessionId,
    scriptFiles,
    selectedScript,
    editingScriptId,
    editingScriptName,
    isUploadingModel,
    uploadError,
    isDragOver,
    threeContext,
    loadedModels,
    uploadedAnimations,
    currentPlayingAnimation,
    previewingAnimation,
    isUploadingAnimation,
    animationError,
    uploadedModels,
    currentModel,
    uploadedMaterials,
    currentMaterial,
    tempInputValues,
    checkpoints,
    currentCheckpointId,
    isCreatingCheckpoint,
    isRollingBack,
    checkpointError,

    // 操作
    setNodes,
    setSelectedNode,
    setNodeStats,
    setAgentMessages,
    setAgentInput,
    setIsAgentProcessing,
    setIsLoadingHistory,
    setCurrentSessionId,
    addAgentMessage,
    setScriptFiles,
    setSelectedScript,
    setEditingScriptId,
    setEditingScriptName,
    addScriptFile,
    updateScriptFile,
    removeScriptFile,
    setIsUploadingModel,
    setUploadError,
    setIsDragOver,
    setThreeContext,
    addLoadedModel,
    removeLoadedModel,
    addAnimation,
    removeAnimation,
    setCurrentPlayingAnimation,
    setPreviewingAnimation,
    setIsUploadingAnimation,
    setAnimationError,
    getNodeAnimations,
    addModel,
    removeModel,
    setCurrentModel,
    getNodeModels,
    addMaterial,
    removeMaterial,
    setCurrentMaterial,
    getNodeMaterials,
    setTempInputValues: updateTempInputValues,
    setCheckpoints,
    setCurrentCheckpointId,
    setIsCreatingCheckpoint,
    setIsRollingBack,
    setCheckpointError,
    createCheckpoint,
    rollbackToCheckpoint,
    loadCheckpoints,
    recoverFromError,
    generateSessionId,
    scrollToBottom
  };
  
  return (
    <NodeSystemContext.Provider value={contextValue}>
      <div ref={messagesContainerRef}>
        {children}
      </div>
    </NodeSystemContext.Provider>
  );
};

export default NodeSystemContext;
