/**
 * BasicPropertiesTab - 基础属性标签页组件
 * 提供节点基础属性的编辑功能
 */

'use client';

import React, { useState } from 'react';
import { GameNodeProperties, GameNodeType, CameraNodeProperties } from '../../../../src/types/NodeTypes';

interface BasicPropertiesTabProps {
  selectedNode: GameNodeProperties;
  tempInputValues: Record<string, string>;
  onUpdateProperty: (property: string, value: string) => void;
}

export const BasicPropertiesTab: React.FC<BasicPropertiesTabProps> = ({
  selectedNode,
  tempInputValues,
  onUpdateProperty
}) => {
  const [editingNodeName, setEditingNodeName] = useState<string>(selectedNode.name);
  const [isEditing, setIsEditing] = useState<boolean>(false);

  // 当选中的节点改变时，更新编辑状态
  React.useEffect(() => {
    if (!isEditing) {
      setEditingNodeName(selectedNode.name);
    }
  }, [selectedNode.name, isEditing]);

  return (
    <div className="space-y-4">
      {/* 节点名称 */}
      <div>
        <label className="block text-sm font-medium mb-1 text-gray-900">名称</label>
        <input
          type="text"
          value={editingNodeName}
          onChange={(e) => setEditingNodeName(e.target.value)}
          onFocus={() => setIsEditing(true)}
          onBlur={() => {
            setIsEditing(false);
            // 只要名称发生了变化就保存，包括空字符串
            if (editingNodeName !== selectedNode.name) {
              onUpdateProperty('name', editingNodeName);
            }
          }}
          onKeyDown={(e) => {
            if (e.key === 'Enter') {
              e.currentTarget.blur();
            }
          }}
          className="w-full border border-gray-300 rounded px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        />
      </div>

      {/* 位置属性 */}
      <div>
        <label className="block text-sm font-medium mb-2 text-gray-900">位置 (Position)</label>
        <div className="grid grid-cols-3 gap-2">
          {(['x', 'y', 'z'] as const).map((axis) => (
            <div key={axis}>
              <label className="block text-xs text-gray-600 mb-1">{axis.toUpperCase()}</label>
              <input
                type="text"
                value={tempInputValues[`position${axis.toUpperCase()}`] ??
                       selectedNode.position?.[axis]?.toFixed(2) ?? '0'}
                onChange={(e) => onUpdateProperty(`position.${axis}`, e.target.value)}
                className="w-full border border-gray-300 rounded px-2 py-1 text-xs focus:outline-none focus:ring-1 focus:ring-blue-500"
              />
            </div>
          ))}
        </div>
      </div>

      {/* 旋转属性 */}
      <div>
        <label className="block text-sm font-medium mb-2 text-gray-900">旋转 (Rotation)</label>
        <div className="grid grid-cols-3 gap-2">
          {(['x', 'y', 'z'] as const).map((axis) => (
            <div key={axis}>
              <label className="block text-xs text-gray-600 mb-1">{axis.toUpperCase()}°</label>
              <input
                type="text"
                value={tempInputValues[`rotation${axis.toUpperCase()}`] ??
                       ((selectedNode.rotation?.[axis] * 180) / Math.PI)?.toFixed(1) ?? '0'}
                onChange={(e) => onUpdateProperty(`rotation.${axis}`, e.target.value)}
                className="w-full border border-gray-300 rounded px-2 py-1 text-xs focus:outline-none focus:ring-1 focus:ring-blue-500"
              />
            </div>
          ))}
        </div>
      </div>

      {/* 缩放属性 */}
      <div>
        <label className="block text-sm font-medium mb-2 text-gray-900">缩放 (Scale)</label>
        <div className="grid grid-cols-3 gap-2">
          {(['x', 'y', 'z'] as const).map((axis) => (
            <div key={axis}>
              <label className="block text-xs text-gray-600 mb-1">{axis.toUpperCase()}</label>
              <input
                type="text"
                value={tempInputValues[`scale${axis.toUpperCase()}`] ??
                       selectedNode.scaling?.[axis]?.toFixed(2) ?? '1'}
                onChange={(e) => onUpdateProperty(`scale.${axis}`, e.target.value)}
                className="w-full border border-gray-300 rounded px-2 py-1 text-xs focus:outline-none focus:ring-1 focus:ring-blue-500"
              />
            </div>
          ))}
        </div>
      </div>

      {/* 摄像机特有属性 */}
      {selectedNode.type === GameNodeType.CAMERA && (
        <div className="space-y-3 pt-4 border-t border-gray-200">
          <h4 className="text-sm font-medium text-gray-900">摄像机属性</h4>
          
          {/* FOV */}
          <div>
            <label className="block text-sm font-medium mb-1 text-gray-900">视野角度 (FOV)</label>
            <input
              type="text"
              value={tempInputValues['fov'] ?? (selectedNode as CameraNodeProperties).fov?.toString() ?? '75'}
              onChange={(e) => onUpdateProperty('fov', e.target.value)}
              className="w-full border border-gray-300 rounded px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          {/* 视角锁定 */}
          <div>
            <label className="flex items-center space-x-2 text-sm font-medium text-gray-900">
              <input
                type="checkbox"
                checked={(() => {
                  if (tempInputValues['viewLocked'] !== undefined) {
                    return tempInputValues['viewLocked'] === 'true';
                  }
                  return (selectedNode as CameraNodeProperties).viewLocked ?? true;
                })()}
                onChange={(e) => {
                  console.log('[BasicPropertiesTab] 视角锁定状态变更:', e.target.checked);
                  onUpdateProperty('viewLocked', e.target.checked.toString());
                }}
                className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
              />
              <span>视角锁定</span>
            </label>
            <p className="text-xs text-gray-500 mt-1">勾选后用户无法手动缩放、移动摄像机视角</p>
          </div>


        </div>
      )}

      {/* 节点信息 */}
      <div className="space-y-3 pt-4 border-t border-gray-200">
        <div>
          <label className="block text-sm font-medium mb-1 text-gray-900">类型</label>
          <p className="text-sm text-gray-700 bg-gray-100 rounded px-3 py-2">
            {selectedNode.type}
          </p>
        </div>

        <div>
          <label className="block text-sm font-medium mb-1 text-gray-900">ID</label>
          <p className="text-xs text-gray-600 bg-gray-100 rounded px-3 py-2 font-mono break-all">
            {selectedNode.id}
          </p>
        </div>
      </div>
    </div>
  );
};

export default BasicPropertiesTab;
