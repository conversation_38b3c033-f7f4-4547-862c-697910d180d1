/**
 * HTML导出工具栏组件
 * 提供HTML5广告导出功能，支持广告平台部署
 */

'use client';

import React, { useState, useContext } from 'react';
import { NodeSystemContext } from '../contexts/NodeSystemContext';
import { HTMLExporter, ExportOptions } from '../../../../src/services/HTMLExporter';

interface ExportStatus {
  isExporting: boolean;
  progress: number;
  message: string;
  error?: string;
}

/**
 * HTML导出工具栏组件
 */
export default function HTMLExportToolbar(): React.JSX.Element {
  const context = useContext(NodeSystemContext);
  const [exportStatus, setExportStatus] = useState<ExportStatus>({
    isExporting: false,
    progress: 0,
    message: ''
  });
  const [showOptions, setShowOptions] = useState(false);
  const [exportOptions, setExportOptions] = useState<ExportOptions>({
    includeModels: true,
    includeAnimations: true,
    includeScripts: true,
    compressOutput: true,
    maxFileSize: 5
  });

  if (!context) {
    return <div>Loading...</div>;
  }

  const { 
    nodes, 
    persistentFeatures, 
    models,
    threeScene,
    threeCamera 
  } = context;

  /**
   * 处理HTML导出
   */
  const handleExportHTML = async (): Promise<void> => {
    if (!threeScene || !threeCamera) {
      setExportStatus({
        isExporting: false,
        progress: 0,
        message: '',
        error: 'Three.js场景未初始化，请等待场景加载完成'
      });
      return;
    }

    setExportStatus({
      isExporting: true,
      progress: 0,
      message: '开始导出HTML5广告文件...'
    });

    try {
      // 更新进度
      setExportStatus(prev => ({
        ...prev,
        progress: 20,
        message: '收集场景数据...'
      }));

      // 准备场景数据
      const sceneData = {
        nodes: Object.fromEntries(
          Object.entries(nodes).map(([nodeId, nodeData]) => [
            nodeId,
            {
              position: nodeData.position || { x: 0, y: 0, z: 0 },
              rotation: nodeData.rotation || { x: 0, y: 0, z: 0 },
              scale: nodeData.scale || { x: 1, y: 1, z: 1 },
              color: nodeData.color || '#00ff00',
              model: nodeData.model,
              material: nodeData.material,
              animation: nodeData.animation
            }
          ])
        ),
        scripts: persistentFeatures.map(feature => ({
          id: feature.id || Math.random().toString(36).substring(2, 11),
          name: feature.name || 'Unnamed Script',
          content: feature.content || '',
          isActive: feature.isActive || false
        })),
        camera: {
          position: [threeCamera.position.x, threeCamera.position.y, threeCamera.position.z] as [number, number, number],
          target: [0, 0, 0] as [number, number, number]
        },
        lights: [] as Array<{
          type: string;
          color: number;
          intensity: number;
          position: [number, number, number];
          target?: [number, number, number];
        }>,
        background: threeScene.background ? '#000000' : '#000000'
      };

      // 收集光照数据
      threeScene.traverse((object) => {
        if ((object as any).isLight) {
          const light = object as any;
          sceneData.lights.push({
            type: light.type || 'AmbientLight',
            color: light.color?.getHex() || 0xffffff,
            intensity: light.intensity || 1,
            position: [light.position.x, light.position.y, light.position.z]
          });
        }
      });

      // 更新进度
      setExportStatus(prev => ({
        ...prev,
        progress: 50,
        message: '发送导出请求...'
      }));

      // 调用API导出
      const response = await fetch('/api/export-html', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sceneData,
          options: exportOptions
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || '导出请求失败');
      }

      // 更新进度
      setExportStatus(prev => ({
        ...prev,
        progress: 80,
        message: '准备下载文件...'
      }));

      // 获取HTML内容
      const htmlContent = await response.text();
      const fileSizeKB = response.headers.get('X-File-Size-KB');

      // 下载文件
      const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
      const filename = `playable-game-${timestamp}.html`;

      const blob = new Blob([htmlContent], { type: 'text/html;charset=utf-8' });
      const url = URL.createObjectURL(blob);

      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      link.style.display = 'none';

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      URL.revokeObjectURL(url);

      // 完成
      setExportStatus({
        isExporting: false,
        progress: 100,
        message: `HTML5广告文件已成功导出: ${filename}${fileSizeKB ? ` (${fileSizeKB}KB)` : ''}`
      });

      // 3秒后清除消息
      setTimeout(() => {
        setExportStatus({
          isExporting: false,
          progress: 0,
          message: ''
        });
      }, 3000);

    } catch (error) {
      console.error('HTML导出失败:', error);
      setExportStatus({
        isExporting: false,
        progress: 0,
        message: '',
        error: error instanceof Error ? error.message : '导出失败，请重试'
      });

      // 5秒后清除错误消息
      setTimeout(() => {
        setExportStatus({
          isExporting: false,
          progress: 0,
          message: ''
        });
      }, 5000);
    }
  };

  /**
   * 渲染导出选项面板
   */
  const renderExportOptions = (): React.JSX.Element => {
    if (!showOptions) return <></>;

    return (
      <div className="absolute top-full left-0 mt-2 bg-white border border-gray-200 rounded-lg shadow-lg p-4 z-50 min-w-80">
        <h3 className="text-sm font-semibold text-gray-800 mb-3">导出选项</h3>
        
        <div className="space-y-3">
          {/* 包含模型 */}
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={exportOptions.includeModels}
              onChange={(e) => setExportOptions(prev => ({
                ...prev,
                includeModels: e.target.checked
              }))}
              className="rounded border-gray-300"
            />
            <span className="text-sm text-gray-700">包含3D模型</span>
          </label>

          {/* 包含动画 */}
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={exportOptions.includeAnimations}
              onChange={(e) => setExportOptions(prev => ({
                ...prev,
                includeAnimations: e.target.checked
              }))}
              className="rounded border-gray-300"
            />
            <span className="text-sm text-gray-700">包含动画</span>
          </label>

          {/* 包含脚本 */}
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={exportOptions.includeScripts}
              onChange={(e) => setExportOptions(prev => ({
                ...prev,
                includeScripts: e.target.checked
              }))}
              className="rounded border-gray-300"
            />
            <span className="text-sm text-gray-700">包含交互脚本</span>
          </label>

          {/* 压缩输出 */}
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={exportOptions.compressOutput}
              onChange={(e) => setExportOptions(prev => ({
                ...prev,
                compressOutput: e.target.checked
              }))}
              className="rounded border-gray-300"
            />
            <span className="text-sm text-gray-700">压缩输出</span>
          </label>

          {/* 最大文件大小 */}
          <div className="flex items-center space-x-2">
            <label className="text-sm text-gray-700">最大文件大小:</label>
            <select
              value={exportOptions.maxFileSize}
              onChange={(e) => setExportOptions(prev => ({
                ...prev,
                maxFileSize: parseInt(e.target.value)
              }))}
              className="text-sm border border-gray-300 rounded px-2 py-1"
            >
              <option value={2}>2MB (Facebook)</option>
              <option value={5}>5MB (通用)</option>
              <option value={10}>10MB (测试)</option>
            </select>
          </div>
        </div>

        <div className="flex justify-end space-x-2 mt-4 pt-3 border-t border-gray-200">
          <button
            onClick={() => setShowOptions(false)}
            className="px-3 py-1 text-sm text-gray-600 hover:text-gray-800"
          >
            取消
          </button>
          <button
            onClick={() => setShowOptions(false)}
            className="px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            确定
          </button>
        </div>
      </div>
    );
  };

  return (
    <div className="bg-white border-b border-gray-200 px-4 py-2">
      <div className="flex items-center justify-between">
        {/* 左侧：标题 */}
        <div className="flex items-center space-x-4">
          <h1 className="text-lg font-semibold text-gray-800">PlayableAgent 演示</h1>
          <div className="text-sm text-gray-500">
            Three.js 3D场景 + AI Agent + 脚本系统
          </div>
        </div>

        {/* 右侧：导出工具 */}
        <div className="flex items-center space-x-3">
          {/* 导出状态显示 */}
          {exportStatus.message && (
            <div className="flex items-center space-x-2">
              {exportStatus.isExporting && (
                <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
              )}
              <span className={`text-sm ${
                exportStatus.error ? 'text-red-600' : 'text-green-600'
              }`}>
                {exportStatus.error || exportStatus.message}
              </span>
              {exportStatus.isExporting && (
                <span className="text-sm text-gray-500">
                  ({exportStatus.progress}%)
                </span>
              )}
            </div>
          )}

          {/* 导出选项按钮 */}
          <div className="relative">
            <button
              onClick={() => setShowOptions(!showOptions)}
              disabled={exportStatus.isExporting}
              className="px-3 py-1 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50"
            >
              导出选项
            </button>
            {renderExportOptions()}
          </div>

          {/* 导出HTML按钮 */}
          <button
            onClick={handleExportHTML}
            disabled={exportStatus.isExporting || !threeScene}
            className="px-4 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
          >
            {exportStatus.isExporting ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                <span>导出中...</span>
              </>
            ) : (
              <>
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                <span>导出HTML5广告</span>
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
}
