/**
 * NodePropertiesUtils - 节点属性相关的工具函数和类型定义
 */

import * as THREE from 'three';
import { GameNodeType, AnimationClipInfo } from '../../../../../src/types/NodeTypes';

/**
 * 支持的文件格式常量
 */
export const FILE_FORMATS = {
  MODEL: ['.fbx', '.glb', '.gltf'],
  ANIMATION: ['.fbx'],
  MATERIAL: ['.jpg', '.jpeg', '.png', '.bmp', '.tga', '.exr', '.hdr']
} as const;

/**
 * 验证文件扩展名
 */
export const validateFileExtension = (fileName: string, allowedExtensions: readonly string[]): string => {
  const fileExtension = '.' + fileName.split('.').pop()?.toLowerCase();
  
  if (!allowedExtensions.includes(fileExtension)) {
    throw new Error(`文件格式不支持。支持的格式: ${allowedExtensions.join(', ')}`);
  }
  
  return fileExtension;
};

/**
 * 生成唯一ID
 */
export const generateUniqueId = (prefix: string): string => {
  return `${prefix}_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
};

/**
 * 格式化文件大小
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * 上传文件到服务器的通用函数
 */
export const uploadFileToServer = async (
  file: File, 
  nodeId: string, 
  itemId: string, 
  type: 'model' | 'animation' | 'material'
): Promise<{ filePath: string }> => {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('nodeId', nodeId);
  formData.append(`${type}Id`, itemId);
  formData.append('type', type);

  const response = await fetch('/api/upload-model', {
    method: 'POST',
    body: formData
  });

  const result = await response.json();

  if (!result.success) {
    throw new Error(result.error || `${type}文件上传失败`);
  }

  return result.data;
};

/**
 * 从文件名移除扩展名
 */
export const removeFileExtension = (fileName: string): string => {
  return fileName.replace(/\.[^/.]+$/, '');
};

/**
 * 更新场景对象的变换属性
 */
export const updateSceneObjectTransform = (
  sceneObject: THREE.Object3D,
  property: 'position' | 'rotation' | 'scale',
  axis: 'x' | 'y' | 'z',
  value: number
): void => {
  console.log(`[NodePropertiesUtils] 更新变换属性: ${property}.${axis} = ${value}`);

  // 对于非摄像机对象，直接设置属性
  if (!(sceneObject instanceof THREE.Camera)) {
    sceneObject[property][axis] = value;
    return;
  }

  // 摄像机特殊处理
  const camera = sceneObject as THREE.Camera;
  const controls = (window as unknown as { 
    orbitControls?: { 
      update: () => void;
      object: THREE.Camera;
      target: THREE.Vector3;
      enabled: boolean;
      getAzimuthalAngle?: () => number;
      getPolarAngle?: () => number;
      getDistance?: () => number;
    } 
  }).orbitControls;

  if (property === 'rotation' && controls && controls.object === camera) {
    // 对于摄像机旋转，通过调整OrbitControls的target来实现
    console.log(`[NodePropertiesUtils] 处理摄像机旋转: ${axis} = ${value}`);
    
    const currentPosition = camera.position.clone();
    
    // 使用固定的参考距离和当前摄像机的rotation状态来计算目标
    const defaultDistance = 10; // 固定参考距离
    
    // 获取摄像机当前的完整旋转状态，然后更新指定轴
    const currentRotation = {
      x: camera.rotation.x,
      y: camera.rotation.y,
      z: camera.rotation.z
    };
    
    // 更新指定轴的旋转值
    currentRotation[axis] = value;
    
    console.log(`[NodePropertiesUtils] 摄像机旋转状态:`, currentRotation);
    
    // 直接根据旋转值创建球坐标
    const spherical = new THREE.Spherical();
    spherical.radius = defaultDistance;
    
    if (axis === 'x') {
      // X轴旋转（俯仰）- 更新phi
      spherical.phi = Math.PI / 2 - currentRotation.x;
      spherical.theta = currentRotation.y; // 保持Y轴旋转
    } else if (axis === 'y') {
      // Y轴旋转（偏航）- 更新theta  
      spherical.theta = currentRotation.y;
      spherical.phi = Math.PI / 2 - currentRotation.x; // 保持X轴旋转
    }
    
    // 确保角度在有效范围内
    spherical.phi = Math.max(0.1, Math.min(Math.PI - 0.1, spherical.phi));
    
    // 基于球坐标计算新的目标位置
    const targetDirection = new THREE.Vector3();
    targetDirection.setFromSpherical(spherical);
    const newTarget = currentPosition.clone().add(targetDirection);
    
    console.log(`[NodePropertiesUtils] 球坐标计算:`, {
      axis,
      value,
      phi: spherical.phi,
      theta: spherical.theta,
      distance: spherical.radius,
      newTarget: newTarget.toArray()
    });
    
    // 更新OrbitControls的目标
    controls.target.copy(newTarget);
    controls.update();
    
    console.log(`[NodePropertiesUtils] 摄像机旋转已应用`);
    
  } else {
    // 对于位置和缩放，直接设置
    camera[property][axis] = value;
    camera.updateMatrixWorld(true);
    
    if (controls && controls.object === camera) {
      controls.update();
    }
  }
};

/**
 * 复制对象的变换属性
 */
export const copyTransform = (source: THREE.Object3D, target: THREE.Object3D): void => {
  target.position.copy(source.position);
  target.rotation.copy(source.rotation);
  target.scale.copy(source.scale);
  target.name = source.name;
};

/**
 * 计算动画的总时长
 */
export const calculateTotalDuration = (clips: AnimationClipInfo[]): number => {
  return clips.length > 0 ? Math.max(...clips.map(clip => clip.duration)) : 0;
};

/**
 * 检查节点是否为Mesh类型
 */
export const isMeshNode = (nodeType: GameNodeType): boolean => {
  return nodeType === GameNodeType.MESH;
};

/**
 * 错误处理工具函数
 */
export const handleError = (error: unknown, context: string): string => {
  const message = error instanceof Error ? error.message : '未知错误';
  console.error(`[${context}] 错误:`, error);
  return message;
};

/**
 * 创建加载Promise的工具函数
 */
export const createLoaderPromise = <T>(
  loader: { load: (path: string, onLoad: (result: T) => void, onProgress?: (progress: ProgressEvent) => void, onError?: (error: unknown) => void) => void },
  path: string,
  onProgress?: (progress: ProgressEvent) => void
): Promise<T> => {
  return new Promise<T>((resolve, reject) => {
    loader.load(
      path,
      (result: T) => resolve(result),
      onProgress,
      (error: unknown) => reject(error)
    );
  });
}; 