/**
 * useNodePropertyManagement - 节点属性管理Hook
 * 负责节点属性的更新和保存
 */

import { useCallback, useState } from 'react';
import * as THREE from 'three';
import { useNodeSystem } from '../../contexts/NodeSystemContext';
import { GameNodeType, GameNodeProperties, MeshNodeProperties, CameraNodeProperties } from '../../../../../src/types/NodeTypes';
import { updateSceneObjectTransform } from '../utils/NodePropertiesUtils';

export interface UseNodePropertyManagementReturn {
  isSaving: boolean;
  saveMessage: string | null;
  updateNodeProperty: (property: string, value: string) => void;
  saveNodeConfiguration: () => Promise<void>;
}

export const useNodePropertyManagement = (): UseNodePropertyManagementReturn => {
  const {
    nodes,
    selectedNode,
    setNodes,
    setSelectedNode,
    setTempInputValues,
    threeContext,
    getNodeModels,
    getNodeMaterials,
    getNodeAnimations
  } = useNodeSystem();

  const [isSaving, setIsSaving] = useState<boolean>(false);
  const [saveMessage, setSaveMessage] = useState<string | null>(null);

  // 更新节点属性
  const updateNodeProperty = useCallback((property: string, value: string) => {
    if (!selectedNode || !threeContext) return;

    const { scene } = threeContext;
    const sceneObject = scene.getObjectByName(selectedNode.id);

    // 添加调试日志
    console.log('[节点属性] 正在更新属性:', {
      nodeId: selectedNode.id,
      property,
      value,
      sceneObject: sceneObject ? {
        name: sceneObject.name,
        type: sceneObject.type,
        isCamera: sceneObject instanceof THREE.Camera,
        constructor: sceneObject.constructor.name
      } : null
    });

    // 处理嵌套属性（如 position.x）
    const keys = property.split('.');
    const updatedNode = { ...selectedNode };

    if (keys.length === 2) {
      const [parentKey, childKey] = keys;
      if (parentKey === 'position' || parentKey === 'rotation' || parentKey === 'scale') {
        const numValue = parseFloat(value) || 0;
        const finalValue = parentKey === 'rotation' ? (numValue * Math.PI) / 180 : numValue;

        // 处理scaling属性的映射
        const nodePropertyKey = parentKey === 'scale' ? 'scaling' : parentKey;

        if (!updatedNode[nodePropertyKey as keyof GameNodeProperties]) {
          (updatedNode as unknown as Record<string, THREE.Vector3>)[nodePropertyKey] = new THREE.Vector3();
        }
        ((updatedNode as unknown as Record<string, THREE.Vector3>)[nodePropertyKey] as THREE.Vector3)[childKey as 'x' | 'y' | 'z'] = finalValue;

        // 同步更新Three.js场景中的对象
        if (sceneObject) {
          updateSceneObjectTransform(sceneObject, parentKey, childKey as 'x' | 'y' | 'z', finalValue);
        }

        // 更新临时输入值（单个属性更新，保持其他值不变）
        setTempInputValues({
          [`${parentKey}${childKey.toUpperCase()}`]: value
        });
      }
    } else {
      // 处理不同类型的属性值转换
      if (property === 'viewLocked') {
        (updatedNode as CameraNodeProperties).viewLocked = value === 'true';
      } else if (property === 'fov') {
        (updatedNode as CameraNodeProperties).fov = parseFloat(value) || 75;
      } else {
        (updatedNode as unknown as Record<string, string | boolean>)[property] = value;
      }

      // 更新临时输入值
      setTempInputValues({ [property]: value });

      // 同步更新Three.js场景中的对象的其他属性
      // 注意：不要更新sceneObject.name，因为它用于通过ID查找对象
      if (sceneObject && property === 'enabled') {
        // enabled属性可能需要特殊处理
        sceneObject.userData.enabled = value === 'true';
      } else if (sceneObject instanceof THREE.Camera) {
        // 处理摄像机特有属性
        const camera = sceneObject as THREE.PerspectiveCamera;
        if (property === 'fov') {
          camera.fov = parseFloat(value) || 75;
          camera.updateProjectionMatrix();
          console.log('[节点属性] 摄像机FOV已更新:', camera.fov);
        } else if (property === 'viewLocked') {
          // 处理视角锁定
          const isLocked = value === 'true';
          const controls = (window as unknown as { 
            orbitControls?: { 
              enabled: boolean;
            } 
          }).orbitControls;
          
          if (controls) {
            controls.enabled = !isLocked;
            console.log('[节点属性] 摄像机视角锁定已更新:', isLocked ? '已锁定' : '已解锁');
          }
        }
      }

      // 对于name属性，我们只更新React状态，不更新Three.js对象的name
      // 因为Three.js对象的name用于通过ID查找，应该保持为节点ID
    }

    // 更新节点列表
    const updatedNodes = nodes.map(node =>
      node.id === selectedNode.id ? updatedNode : node
    );
    setNodes(updatedNodes);

    // 同时更新selectedNode状态，确保UI显示最新的值
    setSelectedNode(updatedNode);

    console.log('[节点属性] 属性已更新:', property, value, '场景对象:', sceneObject);
  }, [selectedNode, nodes, setNodes, setSelectedNode, setTempInputValues, threeContext]);

  // 保存节点配置
  const saveNodeConfiguration = useCallback(async () => {
    if (!selectedNode) {
      console.warn('[节点保存] 没有选中的节点');
      return;
    }

    setIsSaving(true);
    setSaveMessage(null);

    try {
      console.log('[节点保存] 开始保存节点配置:', selectedNode.id);

      // 等待一小段时间，确保Context更新完成
      await new Promise(resolve => setTimeout(resolve, 100));

      // 准备保存的节点数据，包含模型和材质信息
      const nodeToSave = {
        id: selectedNode.id,
        name: selectedNode.name,
        type: selectedNode.type,
        position: {
          x: selectedNode.position.x,
          y: selectedNode.position.y,
          z: selectedNode.position.z
        },
        rotation: {
          x: selectedNode.rotation.x,
          y: selectedNode.rotation.y,
          z: selectedNode.rotation.z
        },
        scaling: {
          x: selectedNode.scaling.x,
          y: selectedNode.scaling.y,
          z: selectedNode.scaling.z
        },
        // 从Context获取模型信息
        models: selectedNode.type === GameNodeType.MESH ?
          getNodeModels(selectedNode.id) : undefined,
        // 从Context获取材质信息
        materials: selectedNode.type === GameNodeType.MESH ?
          getNodeMaterials(selectedNode.id) : undefined,
        // 从Context获取动画信息
        animations: selectedNode.type === GameNodeType.MESH ?
          getNodeAnimations(selectedNode.id) : undefined,
        // 保留颜色信息（如果存在）
        color: selectedNode.type === GameNodeType.MESH &&
               (selectedNode as MeshNodeProperties).material?.diffuseColor ? {
          r: (selectedNode as MeshNodeProperties).material!.diffuseColor!.r,
          g: (selectedNode as MeshNodeProperties).material!.diffuseColor!.g,
          b: (selectedNode as MeshNodeProperties).material!.diffuseColor!.b
        } : undefined,
        // 保存摄像机特有属性
        ...(selectedNode.type === GameNodeType.CAMERA && {
          fov: (selectedNode as CameraNodeProperties).fov,
          viewLocked: (selectedNode as CameraNodeProperties).viewLocked
        })
      };

      // 添加调试日志，查看保存的数据
      console.log('[节点保存] 准备保存的数据:', nodeToSave);
      if (nodeToSave.materials) {
        console.log('[节点保存] 材质数据:', nodeToSave.materials.map(m => ({ name: m.name, isDefault: m.isDefault })));
      }
      if (nodeToSave.animations) {
        console.log('[节点保存] 动画数据:', nodeToSave.animations.map(a => ({ name: a.name, isDefault: a.isDefault })));
      }

      // 调用保存API
      const response = await fetch('/api/node-properties', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          nodeProperty: nodeToSave
        })
      });

      const result = await response.json();

      if (result.success) {
        console.log('[节点保存] 节点配置保存成功:', result.message);
        setSaveMessage('保存成功！');
        // 3秒后清除消息
        setTimeout(() => setSaveMessage(null), 3000);
      } else {
        console.error('[节点保存] 节点配置保存失败:', result.error);
        setSaveMessage('保存失败: ' + result.error);
      }

    } catch (error) {
      console.error('[节点保存] 保存过程中发生错误:', error);
      setSaveMessage('保存失败: ' + (error instanceof Error ? error.message : '未知错误'));
    } finally {
      setIsSaving(false);
    }
  }, [selectedNode, getNodeModels, getNodeMaterials, getNodeAnimations]);

  return {
    isSaving,
    saveMessage,
    updateNodeProperty,
    saveNodeConfiguration
  };
}; 