/**
 * useAnimationManagement - 动画管理Hook
 * 负责动画的上传、预览、播放和删除
 */

import { useCallback, useState } from 'react';
import * as THREE from 'three';
import { FBXLoader } from 'three/examples/jsm/loaders/FBXLoader.js';
import { useNodeSystem } from '../../contexts/NodeSystemContext';
import { AnimationInfo, AnimationClipInfo, GameNodeType, MeshNodeProperties } from '../../../../../src/types/NodeTypes';
import { 
  FILE_FORMATS, 
  validateFileExtension, 
  generateUniqueId, 
  uploadFileToServer, 
  removeFileExtension, 
  calculateTotalDuration,
  handleError,
  createLoaderPromise
} from '../utils/NodePropertiesUtils';

export interface UseAnimationManagementReturn {
  isUploadingAnimation: boolean;
  animationError: string | null;
  isDragOverAnimation: boolean;
  setIsDragOverAnimation: (isDragOver: boolean) => void;
  handleAnimationUpload: (file: File, nodeId: string) => Promise<void>;
  previewAnimation: (nodeId: string, animationInfo: AnimationInfo) => Promise<void>;
  stopPreviewAnimation: (nodeId: string) => void;
  setDefaultAnimation: (nodeId: string, animationInfo: AnimationInfo) => Promise<void>;
}

export const useAnimationManagement = (): UseAnimationManagementReturn => {
  const {
    nodes,
    setNodes,
    threeContext,
    addAnimation,
    setCurrentPlayingAnimation,
    setPreviewingAnimation,
    getNodeAnimations
  } = useNodeSystem();

  const [isUploadingAnimation, setIsUploadingAnimation] = useState<boolean>(false);
  const [animationError, setAnimationError] = useState<string | null>(null);
  const [isDragOverAnimation, setIsDragOverAnimation] = useState<boolean>(false);
  const [, setAnimationMixer] = useState<THREE.AnimationMixer | null>(null);

  // 加载动画元数据
  const loadAnimationMetadata = useCallback(async (animationPath: string): Promise<{ duration: number; clips: AnimationClipInfo[] }> => {
    try {
      const fbxLoader = new FBXLoader();
      const animationFBX = await createLoaderPromise<THREE.Group>(fbxLoader, animationPath);

      if (animationFBX.animations.length === 0) {
        return { duration: 0, clips: [] };
      }

      const clips: AnimationClipInfo[] = animationFBX.animations.map(clip => ({
        name: clip.name || 'Unnamed',
        duration: clip.duration,
        tracks: clip.tracks.length
      }));

      const totalDuration = calculateTotalDuration(clips);

      return { duration: totalDuration, clips };
    } catch (error) {
      console.error('[动画元数据] 加载失败:', error);
      return { duration: 0, clips: [] };
    }
  }, []);

  // 处理动画文件上传
  const handleAnimationUpload = useCallback(async (file: File, nodeId: string) => {
    setIsUploadingAnimation(true);
    setAnimationError(null);

    try {
      // 验证文件格式
      validateFileExtension(file.name, FILE_FORMATS.ANIMATION);

      console.log('[动画上传] 开始上传动画文件:', file.name, 'to node:', nodeId);

      // 生成动画ID
      const animationId = generateUniqueId('anim');

      // 1. 上传文件到服务器
      const uploadResult = await uploadFileToServer(file, nodeId, animationId, 'animation');

      console.log('[动画上传] 动画文件上传成功:', uploadResult.filePath);

      // 2. 加载动画文件获取元数据
      const animationMetadata = await loadAnimationMetadata(uploadResult.filePath);

      // 3. 创建动画信息对象
      const animationInfo: AnimationInfo = {
        id: animationId,
        name: removeFileExtension(file.name),
        filePath: uploadResult.filePath,
        duration: animationMetadata.duration,
        isDefault: false, // 新上传的动画不设为默认
        uploadedAt: new Date(),
        clips: animationMetadata.clips
      };

      // 4. 添加到动画管理系统
      addAnimation(nodeId, animationInfo);

      // 5. 更新节点属性
      const updatedNodes = nodes.map(node => {
        if (node.id === nodeId && node.type === GameNodeType.MESH) {
          const meshNode = node as MeshNodeProperties;
          const existingAnimations = meshNode.animations || [];
          return {
            ...meshNode,
            animations: [...existingAnimations, animationInfo]
          };
        }
        return node;
      });
      setNodes(updatedNodes);

      console.log('[动画上传] 动画信息已保存到节点属性');

    } catch (error) {
      const errorMessage = handleError(error, '动画上传');
      setAnimationError(errorMessage);
    } finally {
      setIsUploadingAnimation(false);
    }
  }, [addAnimation, nodes, setNodes, loadAnimationMetadata]);

  // 预览动画
  const previewAnimation = useCallback(async (nodeId: string, animationInfo: AnimationInfo) => {
    if (!threeContext) return;

    const { scene } = threeContext;
    const existingObject = scene.getObjectByName(nodeId);

    if (!existingObject) {
      console.warn('[动画预览] 未找到目标节点:', nodeId);
      return;
    }

    try {
      console.log('[动画预览] 开始预览动画:', animationInfo.name);

      // 设置预览状态
      setPreviewingAnimation(nodeId, animationInfo.id);

      // 使用FBXLoader加载动画文件
      const fbxLoader = new FBXLoader();
      const animationFBX = await createLoaderPromise<THREE.Group>(fbxLoader, animationInfo.filePath);

      if (animationFBX.animations.length === 0) {
        throw new Error('动画文件中未找到动画数据');
      }

      // 获取或创建AnimationMixer
      let mixer = existingObject.userData.animationMixer as THREE.AnimationMixer;
      if (!mixer) {
        mixer = new THREE.AnimationMixer(existingObject);
        // 设置合适的时间缩放，与默认动画保持一致
        mixer.timeScale = 0.5; // 降低到一半速度，与默认动画一致
        existingObject.userData.animationMixer = mixer;
        setAnimationMixer(mixer);
      }

      // 停止所有现有动画
      mixer.stopAllAction();

      // 播放预览动画
      animationFBX.animations.forEach((clip) => {
        const action = mixer.clipAction(clip);
        action.setLoop(THREE.LoopRepeat, Infinity);
        action.play();
      });

      console.log('[动画预览] 预览动画播放成功');

    } catch (error) {
      console.error('[动画预览] 预览失败:', error);
      setAnimationError('动画预览失败: ' + handleError(error, '动画预览'));
    }
  }, [threeContext, setPreviewingAnimation, setAnimationMixer]);

  // 停止预览动画
  const stopPreviewAnimation = useCallback((nodeId: string) => {
    if (!threeContext) return;

    const { scene } = threeContext;
    const existingObject = scene.getObjectByName(nodeId);

    if (!existingObject) return;

    const mixer = existingObject.userData.animationMixer as THREE.AnimationMixer;
    if (mixer) {
      mixer.stopAllAction();
    }

    setPreviewingAnimation(nodeId, null);
    console.log('[动画预览] 已停止预览');
  }, [threeContext, setPreviewingAnimation]);

  // 设置默认动画
  const setDefaultAnimation = useCallback(async (nodeId: string, animationInfo: AnimationInfo) => {
    try {
      // 更新Context中的动画数据，将指定动画设为默认
      const nodeAnimations = getNodeAnimations(nodeId);

      // 清除所有动画的默认状态，然后设置新的默认动画
      nodeAnimations.forEach(animation => {
        if (animation.id !== animationInfo.id) {
          addAnimation(nodeId, { ...animation, isDefault: false });
        }
      });

      // 设置新的默认动画
      addAnimation(nodeId, { ...animationInfo, isDefault: true });

      // 设置当前播放动画
      setCurrentPlayingAnimation(nodeId, animationInfo.id);

      // 应用动画到场景
      await previewAnimation(nodeId, animationInfo);

      console.log('[动画设置] 已设置默认动画:', animationInfo.name);
    } catch (error) {
      console.error('[动画设置] 设置默认动画失败:', error);
      setAnimationError('设置默认动画失败: ' + handleError(error, '动画设置'));
    }
  }, [getNodeAnimations, addAnimation, setCurrentPlayingAnimation, previewAnimation]);

  return {
    isUploadingAnimation,
    animationError,
    isDragOverAnimation,
    setIsDragOverAnimation,
    handleAnimationUpload,
    previewAnimation,
    stopPreviewAnimation,
    setDefaultAnimation
  };
}; 