/**
 * useMaterialManagement - 材质管理Hook
 * 负责材质的上传、预览、应用和删除
 */

import { useCallback, useState } from 'react';
import * as THREE from 'three';
import { useNodeSystem } from '../../contexts/NodeSystemContext';
import { MaterialInfo } from '../../../../../src/types/NodeTypes';
import { 
  FILE_FORMATS, 
  validateFileExtension, 
  generateUniqueId, 
  uploadFileToServer, 
  removeFileExtension,
  handleError,
  createLoaderPromise
} from '../utils/NodePropertiesUtils';

export interface UseMaterialManagementReturn {
  isUploadingMaterial: boolean;
  materialError: string | null;
  isDragOverMaterial: boolean;
  setIsDragOverMaterial: (isDragOver: boolean) => void;
  handleMaterialUpload: (file: File, nodeId: string) => Promise<void>;
  previewMaterial: (nodeId: string, materialInfo: MaterialInfo) => Promise<void>;
  setDefaultMaterial: (nodeId: string, materialInfo: MaterialInfo) => Promise<void>;
  removeMaterialHandler: (nodeId: string, materialId: string) => Promise<void>;
}

export const useMaterialManagement = (): UseMaterialManagementReturn => {
  const {
    threeContext,
    addMaterial,
    removeMaterial,
    setCurrentMaterial,
    getNodeMaterials,
    currentMaterial
  } = useNodeSystem();

  const [isUploadingMaterial, setIsUploadingMaterial] = useState<boolean>(false);
  const [materialError, setMaterialError] = useState<string | null>(null);
  const [isDragOverMaterial, setIsDragOverMaterial] = useState<boolean>(false);

  // 处理材质文件上传
  const handleMaterialUpload = useCallback(async (file: File, nodeId: string) => {
    setIsUploadingMaterial(true);
    setMaterialError(null);

    try {
      // 验证文件格式
      const fileExtension = validateFileExtension(file.name, FILE_FORMATS.MATERIAL);

      console.log('[材质上传] 开始上传材质文件:', file.name, 'to node:', nodeId);

      // 生成材质ID
      const materialId = generateUniqueId('mat');

      // 1. 上传文件到服务器
      const uploadResult = await uploadFileToServer(file, nodeId, materialId, 'material');

      console.log('[材质上传] 材质文件上传成功:', uploadResult.filePath);

      // 2. 创建材质信息对象
      const materialInfo: MaterialInfo = {
        id: materialId,
        name: removeFileExtension(file.name),
        filePath: uploadResult.filePath,
        fileSize: file.size,
        fileType: fileExtension.substring(1),
        isDefault: false,
        uploadedAt: new Date(),
        materialType: 'diffuse' // 默认为漫反射材质
      };

      // 3. 添加到材质管理系统
      addMaterial(nodeId, materialInfo);

      console.log('[材质上传] 材质信息已保存');

    } catch (error) {
      const errorMessage = handleError(error, '材质上传');
      setMaterialError(errorMessage);
    } finally {
      setIsUploadingMaterial(false);
    }
  }, [addMaterial]);

  // 预览材质
  const previewMaterial = useCallback(async (nodeId: string, materialInfo: MaterialInfo) => {
    if (!threeContext) {
      console.warn('[材质预览] Three.js上下文未初始化');
      return;
    }

    const { scene } = threeContext;
    const existingObject = scene.getObjectByName(nodeId);

    if (!existingObject) {
      console.warn('[材质预览] 未找到目标节点:', nodeId);
      return;
    }

    try {
      console.log('[材质预览] 开始预览材质:', materialInfo.name);

      // 加载材质纹理
      const textureLoader = new THREE.TextureLoader();
      const texture = await createLoaderPromise<THREE.Texture>(textureLoader, materialInfo.filePath);

      // 设置纹理参数
      texture.wrapS = THREE.RepeatWrapping;
      texture.wrapT = THREE.RepeatWrapping;
      texture.flipY = false;

      // 应用材质到模型
      existingObject.traverse((child) => {
        if (child instanceof THREE.Mesh) {
          // 根据材质类型创建不同的材质
          let newMaterial: THREE.Material;

          switch (materialInfo.materialType) {
            case 'normal':
              // 法线贴图
              newMaterial = new THREE.MeshStandardMaterial({
                normalMap: texture,
                color: 0xffffff
              });
              break;
            case 'roughness':
              // 粗糙度贴图
              newMaterial = new THREE.MeshStandardMaterial({
                roughnessMap: texture,
                color: 0xffffff,
                metalness: 0.0
              });
              break;
            case 'metallic':
              // 金属度贴图
              newMaterial = new THREE.MeshStandardMaterial({
                metalnessMap: texture,
                color: 0xffffff,
                roughness: 0.5
              });
              break;
            case 'emissive':
              // 自发光贴图
              newMaterial = new THREE.MeshStandardMaterial({
                emissiveMap: texture,
                emissive: 0xffffff,
                emissiveIntensity: 0.5
              });
              break;
            case 'diffuse':
            default:
              // 漫反射贴图（默认）
              newMaterial = new THREE.MeshStandardMaterial({
                map: texture,
                color: 0xffffff
              });
              break;
          }

          // 保存原始材质（用于恢复）
          if (!child.userData.originalMaterial) {
            child.userData.originalMaterial = child.material;
          }

          // 应用新材质
          child.material = newMaterial;
          child.material.needsUpdate = true;
        }
      });

      // 设置当前材质状态
      setCurrentMaterial(nodeId, materialInfo.id);

      console.log('[材质预览] 材质预览成功:', materialInfo.name);

    } catch (error) {
      console.error('[材质预览] 材质预览失败:', error);
      setMaterialError('材质预览失败: ' + handleError(error, '材质预览'));
    }
  }, [threeContext, setCurrentMaterial]);

  // 设置默认材质
  const setDefaultMaterial = useCallback(async (nodeId: string, materialInfo: MaterialInfo) => {
    console.log('[材质设置] 设置默认材质:', materialInfo.name);

    try {
      // 先预览材质
      await previewMaterial(nodeId, materialInfo);

      // 更新Context中的材质数据，将指定材质设为默认
      const nodeMaterials = getNodeMaterials(nodeId);

      // 清除所有材质的默认状态，然后设置新的默认材质
      nodeMaterials.forEach(material => {
        if (material.id !== materialInfo.id) {
          addMaterial(nodeId, { ...material, isDefault: false });
        }
      });

      // 设置新的默认材质
      addMaterial(nodeId, { ...materialInfo, isDefault: true });

      console.log('[材质设置] 已设置默认材质:', materialInfo.name);
    } catch (error) {
      console.error('[材质设置] 设置默认材质失败:', error);
      setMaterialError('设置默认材质失败: ' + handleError(error, '材质设置'));
    }
  }, [previewMaterial, getNodeMaterials, addMaterial]);

  // 删除材质
  const removeMaterialHandler = useCallback(async (nodeId: string, materialId: string) => {
    console.log('[材质删除] 删除材质:', materialId);

    // 如果删除的是当前应用的材质，恢复原始材质
    const currentMaterialId = currentMaterial.get(nodeId);
    if (currentMaterialId === materialId && threeContext) {
      const { scene } = threeContext;
      const existingObject = scene.getObjectByName(nodeId);

      if (existingObject) {
        existingObject.traverse((child) => {
          if (child instanceof THREE.Mesh && child.userData.originalMaterial) {
            child.material = child.userData.originalMaterial;
            child.material.needsUpdate = true;
            delete child.userData.originalMaterial;
          }
        });

        // 清除当前材质状态
        setCurrentMaterial(nodeId, null);
      }
    }

    removeMaterial(nodeId, materialId);
  }, [removeMaterial, threeContext, setCurrentMaterial, currentMaterial]);

  return {
    isUploadingMaterial,
    materialError,
    isDragOverMaterial,
    setIsDragOverMaterial,
    handleMaterialUpload,
    previewMaterial,
    setDefaultMaterial,
    removeMaterialHandler
  };
}; 