/**
 * useModelManagement - 模型管理Hook
 * 负责模型的上传、预览、应用和删除
 */

import { useCallback, useState } from 'react';
import * as THREE from 'three';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js';
import { useNodeSystem } from '../../contexts/NodeSystemContext';
import { ModelInfo } from '../../../../../src/types/NodeTypes';
import { fbxModelLoader } from '../../../../../src/three/loaders/FBXModelLoader';
import { 
  FILE_FORMATS, 
  validateFileExtension, 
  generateUniqueId, 
  uploadFileToServer, 
  removeFileExtension, 
  copyTransform,
  handleError,
  createLoaderPromise
} from '../utils/NodePropertiesUtils';

export interface UseModelManagementReturn {
  isUploadingModel: boolean;
  uploadError: string | null;
  isDragOver: boolean;
  setIsDragOver: (isDragOver: boolean) => void;
  handleModelUpload: (file: File, nodeId: string) => Promise<void>;
  previewModel: (nodeId: string, modelInfo: ModelInfo) => Promise<void>;
  setDefaultModel: (nodeId: string, modelInfo: ModelInfo) => Promise<void>;
  removeModelHandler: (nodeId: string, modelId: string) => Promise<void>;
}

export const useModelManagement = (): UseModelManagementReturn => {
  const {
    threeContext,
    addModel,
    removeModel,
    setCurrentModel
  } = useNodeSystem();

  const [isUploadingModel, setIsUploadingModel] = useState<boolean>(false);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const [isDragOver, setIsDragOver] = useState<boolean>(false);

  // 应用模型到节点
  const applyModelToNode = useCallback(async (nodeId: string, modelPath: string, fileExtension: string) => {
    if (!threeContext) return;

    const { scene } = threeContext;
    const existingObject = scene.getObjectByName(nodeId);

    if (!existingObject) {
      console.warn('[模型应用] 未找到目标节点:', nodeId);
      return;
    }

    try {
      let newModel: THREE.Object3D;

      if (fileExtension === '.fbx') {
        console.log('[模型应用] 使用FBXLoader加载模型:', modelPath);
        const fbxModel = await fbxModelLoader.loadModel({
          url: modelPath,
          enableShadows: true,
          scale: 1
        });
        newModel = fbxModel.object;
      } else if (fileExtension === '.glb' || fileExtension === '.gltf') {
        console.log('[模型应用] 使用GLTFLoader加载模型:', modelPath);
        const gltfLoader = new GLTFLoader();
        const gltf = await createLoaderPromise<{ scene: THREE.Object3D }>(gltfLoader, modelPath);
        newModel = gltf.scene;
      } else {
        throw new Error('不支持的模型格式');
      }

      // 复制原有对象的变换属性
      copyTransform(existingObject, newModel);

      // 移除旧对象，添加新对象
      scene.remove(existingObject);
      scene.add(newModel);

      console.log('[模型应用] 模型已成功应用到节点:', nodeId);

    } catch (error) {
      console.error('[模型应用] 模型应用失败:', error);
      throw new Error('模型加载失败: ' + handleError(error, '模型应用'));
    }
  }, [threeContext]);

  // 处理模型文件上传
  const handleModelUpload = useCallback(async (file: File, nodeId: string) => {
    setIsUploadingModel(true);
    setUploadError(null);

    try {
      // 验证文件格式
      const fileExtension = validateFileExtension(file.name, FILE_FORMATS.MODEL);

      console.log('[模型上传] 开始上传模型文件:', file.name, 'to node:', nodeId);

      // 生成模型ID
      const modelId = generateUniqueId('model');

      // 1. 上传文件到服务器
      const uploadResult = await uploadFileToServer(file, nodeId, modelId, 'model');

      console.log('[模型上传] 文件上传成功:', uploadResult.filePath);

      // 2. 创建模型信息对象
      const modelInfo: ModelInfo = {
        id: modelId,
        name: removeFileExtension(file.name),
        filePath: uploadResult.filePath,
        fileSize: file.size,
        fileType: fileExtension.substring(1) as 'fbx' | 'glb' | 'gltf',
        isDefault: false, // 新上传的模型不设为默认
        uploadedAt: new Date(),
        meshCount: 0, // 将在加载后更新
        materialCount: 0, // 将在加载后更新
        hasAnimations: false // 将在加载后更新
      };

      // 3. 添加到模型管理系统
      addModel(nodeId, modelInfo);

      // 4. 加载并应用模型到Three.js场景
      if (threeContext) {
        await applyModelToNode(nodeId, uploadResult.filePath, fileExtension);
      }

      console.log('[模型上传] 模型应用成功');

    } catch (error) {
      const errorMessage = handleError(error, '模型上传');
      setUploadError(errorMessage);
    } finally {
      setIsUploadingModel(false);
    }
  }, [threeContext, applyModelToNode, addModel]);

  // 预览模型
  const previewModel = useCallback(async (nodeId: string, modelInfo: ModelInfo) => {
    console.log('[模型预览] 预览模型:', modelInfo.name);
    setCurrentModel(nodeId, modelInfo.id);
    
    try {
      // 应用模型到场景
      const fileExtension = '.' + modelInfo.fileType;
      await applyModelToNode(nodeId, modelInfo.filePath, fileExtension);
    } catch (error) {
      console.error('[模型预览] 预览失败:', error);
      setUploadError('模型预览失败: ' + handleError(error, '模型预览'));
    }
  }, [setCurrentModel, applyModelToNode]);

  // 设置默认模型
  const setDefaultModel = useCallback(async (nodeId: string, modelInfo: ModelInfo) => {
    console.log('[模型设置] 设置默认模型:', modelInfo.name);
    
    try {
      // 先预览模型
      await previewModel(nodeId, modelInfo);
      
      // 更新模型的默认状态
      addModel(nodeId, { ...modelInfo, isDefault: true });
      
      console.log('[模型设置] 已设置默认模型:', modelInfo.name);
    } catch (error) {
      console.error('[模型设置] 设置默认模型失败:', error);
      setUploadError('设置默认模型失败: ' + handleError(error, '模型设置'));
    }
  }, [previewModel, addModel]);

  // 删除模型
  const removeModelHandler = useCallback(async (nodeId: string, modelId: string) => {
    console.log('[模型删除] 删除模型:', modelId);
    removeModel(nodeId, modelId);
  }, [removeModel]);

  return {
    isUploadingModel,
    uploadError,
    isDragOver,
    setIsDragOver,
    handleModelUpload,
    previewModel,
    setDefaultModel,
    removeModelHandler
  };
}; 