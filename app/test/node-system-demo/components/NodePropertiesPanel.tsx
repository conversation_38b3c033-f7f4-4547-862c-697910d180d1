/**
 * NodePropertiesPanel - 节点属性面板组件（重构版）
 * 提供节点属性的查看和编辑功能，使用拆分的子组件和自定义hooks
 */

'use client';

import React from 'react';
import { useNodeSystem } from '../contexts/NodeSystemContext';
import { GameNodeType } from '../../../../src/types/NodeTypes';
import TabPanel from './TabPanel';
import BasicPropertiesTab from './BasicPropertiesTab';
import ModelAnimationTab from './ModelAnimationTab';
import MaterialTab from './MaterialTab';
import {
  useNodePropertyManagement,
  useModelManagement,
  useAnimationManagement,
  useMaterialManagement
} from './hooks';

interface NodePropertiesPanelProps {
  className?: string;
}

export const NodePropertiesPanel: React.FC<NodePropertiesPanelProps> = ({ className = '' }) => {
  const {
    nodes,
    selectedNode,
    setSelectedNode,
    nodeStats,
    tempInputValues,
    setTempInputValues,
    removeAnimation
  } = useNodeSystem();

  // 使用自定义hooks管理各种功能
  const {
    isSaving,
    saveMessage,
    updateNodeProperty,
    saveNodeConfiguration
  } = useNodePropertyManagement();

  const {
    isUploadingModel,
    uploadError,
    isDragOver,
    setIsDragOver,
    handleModelUpload,
    previewModel,
    setDefaultModel,
    removeModelHandler
  } = useModelManagement();

  const {
    isUploadingAnimation,
    animationError,
    isDragOverAnimation,
    setIsDragOverAnimation,
    handleAnimationUpload,
    previewAnimation,
    stopPreviewAnimation,
    setDefaultAnimation
  } = useAnimationManagement();

  const {
    isUploadingMaterial,
    materialError,
    isDragOverMaterial,
    setIsDragOverMaterial,
    handleMaterialUpload,
    previewMaterial,
    setDefaultMaterial,
    removeMaterialHandler
  } = useMaterialManagement();

  // 当选中节点改变时，重置临时输入值
  React.useEffect(() => {
    if (selectedNode) {
      console.log('[NodePropertiesPanel] 节点选中变化，重置临时输入值:', selectedNode.id);
      // 重置所有临时输入值，确保新选中的节点显示正确的初始值
      setTempInputValues({});
    }
  }, [selectedNode?.id, setTempInputValues]);

  // 如果没有选中节点，显示节点列表
  if (!selectedNode) {
    return (
      <div className={`w-96 bg-white border-l border-gray-200 flex flex-col ${className}`}>
        <div className="p-4 border-b border-gray-200 bg-gray-50">
          <h3 className="text-lg font-bold text-gray-900">🎛️ 节点属性</h3>
          <p className="text-sm text-gray-600">选择一个节点来编辑属性</p>
        </div>

        <div className="flex-1 overflow-y-auto">
          <div className="p-4">
            {nodes.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <p className="text-sm">暂无节点</p>
                <p className="text-xs mt-1">点击『添加方块』创建节点</p>
              </div>
            ) : (
              <div className="space-y-2">
                {nodes.map((node) => (
                  <div
                    key={node.id}
                    onClick={() => setSelectedNode(node)}
                    className="p-3 rounded-lg border border-gray-200 hover:border-gray-300 hover:bg-gray-50 cursor-pointer transition-colors"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {node.name}
                        </p>
                        <p className="text-xs text-gray-500 mt-1">
                          {node.type}
                        </p>
                      </div>
                    </div>

                    {/* 位置信息 */}
                    <div className="mt-2 text-xs text-gray-600">
                      {node.position ? (
                        `位置: (${node.position.x?.toFixed(1) ?? '0'}, ${node.position.y?.toFixed(1) ?? '0'}, ${node.position.z?.toFixed(1) ?? '0'})`
                      ) : (
                        '位置: 未设置'
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* 节点统计 */}
          {Object.keys(nodeStats.byType).length > 0 && (
            <div className="px-4 pb-4">
              <h5 className="text-xs font-medium text-gray-700 mb-2">类型统计</h5>
              <div className="space-y-1">
                {Object.entries(nodeStats.byType).map(([type, count]) => (
                  <div key={type} className="flex justify-between text-xs">
                    <span className="text-gray-600">{type}</span>
                    <span className="text-gray-900 font-medium">{count}</span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className={`w-96 bg-white border-l border-gray-200 flex flex-col ${className}`}>
      <div className="p-4 border-b border-gray-200 bg-gray-50">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-bold text-gray-900">🎛️ 节点属性</h3>
            <p className="text-sm text-gray-600">编辑选中节点的属性</p>
          </div>
          <button
            onClick={() => setSelectedNode(null)}
            className="text-gray-500 hover:text-gray-700 p-1 rounded transition-colors"
            title="返回节点列表"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>

      <div className="flex-1 overflow-hidden">
        <TabPanel
          tabs={[
            {
              id: 'basic',
              label: '基础属性',
              icon: (
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4" />
                </svg>
              ),
              content: (
                <BasicPropertiesTab
                  selectedNode={selectedNode}
                  tempInputValues={tempInputValues}
                  onUpdateProperty={updateNodeProperty}
                />
              )
            },
            {
              id: 'model-animation',
              label: '模型动画',
              icon: (
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                </svg>
              ),
              content: (
                <ModelAnimationTab
                  selectedNode={selectedNode}
                  isDragOver={isDragOver}
                  isDragOverAnimation={isDragOverAnimation}
                  isUploadingModel={isUploadingModel}
                  isUploadingAnimation={isUploadingAnimation}
                  uploadError={uploadError}
                  animationError={animationError}
                  onSetIsDragOver={setIsDragOver}
                  onSetIsDragOverAnimation={setIsDragOverAnimation}
                  onModelUpload={handleModelUpload}
                  onAnimationUpload={handleAnimationUpload}
                  onPreviewAnimation={previewAnimation}
                  onStopPreview={stopPreviewAnimation}
                  onSetDefaultAnimation={setDefaultAnimation}
                  onRemoveAnimation={removeAnimation}
                  onPreviewModel={previewModel}
                  onSetDefaultModel={setDefaultModel}
                  onRemoveModel={removeModelHandler}
                />
              ),
              disabled: selectedNode.type !== GameNodeType.MESH
            },
            {
              id: 'material',
              label: '材质管理',
              icon: (
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z" />
                </svg>
              ),
              content: (
                <MaterialTab
                  selectedNode={selectedNode}
                  isDragOverMaterial={isDragOverMaterial}
                  isUploadingMaterial={isUploadingMaterial}
                  materialError={materialError}
                  onSetIsDragOverMaterial={setIsDragOverMaterial}
                  onMaterialUpload={handleMaterialUpload}
                  onPreviewMaterial={previewMaterial}
                  onSetDefaultMaterial={setDefaultMaterial}
                  onRemoveMaterial={removeMaterialHandler}
                />
              ),
              disabled: selectedNode.type !== GameNodeType.MESH
            }
          ]}
          defaultActiveTab="basic"
          className="h-full"
        />
      </div>

      {/* 保存按钮 */}
      <div className="p-4 border-t border-gray-200 bg-gray-50">
        {/* 保存状态消息 */}
        {saveMessage && (
          <div className={`mb-3 p-2 rounded text-sm ${
            saveMessage.includes('成功')
              ? 'bg-green-100 text-green-800 border border-green-300'
              : 'bg-red-100 text-red-800 border border-red-300'
          }`}>
            {saveMessage}
          </div>
        )}

        <button
          onClick={saveNodeConfiguration}
          disabled={isSaving}
          className={`w-full px-4 py-2 rounded-lg transition-colors flex items-center justify-center space-x-2 ${
            isSaving
              ? 'bg-gray-400 text-white cursor-not-allowed'
              : 'bg-blue-600 text-white hover:bg-blue-700'
          }`}
        >
          {isSaving ? (
            <>
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
              <span>保存中...</span>
            </>
          ) : (
            <>
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4" />
              </svg>
              <span>保存配置</span>
            </>
          )}
        </button>
      </div>
    </div>
  );
};

export default NodePropertiesPanel;