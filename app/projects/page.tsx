"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Tabs, Tabs<PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import {
  Search,
  Filter,
  Grid3X3,
  List,
  Plus,
  MoreHorizontal,
  Play,
  Download,
  Share2,
  Trash2,
  Edit,
  Clock,
  Eye,
} from "lucide-react"
import Link from "next/link"

export default function ProjectsPage() {
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid")
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedFilter, setSelectedFilter] = useState("all")

  const projects = [
    {
      id: 1,
      title: "超级跑酷",
      description: "3D跑酷游戏，角色在城市中奔跑收集金币",
      status: "已完成",
      type: "跑酷游戏",
      thumbnail: "/placeholder.svg?height=200&width=300",
      createdAt: "2024-01-15",
      lastModified: "2小时前",
      plays: 1250,
      size: "15.2MB",
      tags: ["3D", "动作", "收集"],
    },
    {
      id: 2,
      title: "魔法消除",
      description: "消除相同颜色的魔法宝石，达到目标分数",
      status: "生成中",
      type: "消除游戏",
      thumbnail: "/placeholder.svg?height=200&width=300",
      createdAt: "2024-01-14",
      lastModified: "1天前",
      plays: 890,
      size: "8.7MB",
      tags: ["2D", "益智", "策略"],
    },
    {
      id: 3,
      title: "守护城堡",
      description: "建造防御塔阻止敌人攻击城堡",
      status: "已完成",
      type: "塔防游戏",
      thumbnail: "/placeholder.svg?height=200&width=300",
      createdAt: "2024-01-12",
      lastModified: "3天前",
      plays: 2100,
      size: "22.1MB",
      tags: ["策略", "建造", "升级"],
    },
    {
      id: 4,
      title: "极速赛车",
      description: "在赛道上驾驶赛车，超越对手获得胜利",
      status: "草稿",
      type: "竞速游戏",
      thumbnail: "/placeholder.svg?height=200&width=300",
      createdAt: "2024-01-10",
      lastModified: "5天前",
      plays: 0,
      size: "0MB",
      tags: ["3D", "竞速", "多人"],
    },
    {
      id: 5,
      title: "魔法冒险",
      description: "RPG冒险游戏，探索魔法世界",
      status: "已完成",
      type: "角色扮演",
      thumbnail: "/placeholder.svg?height=200&width=300",
      createdAt: "2024-01-08",
      lastModified: "1周前",
      plays: 3200,
      size: "45.8MB",
      tags: ["RPG", "冒险", "魔法"],
    },
    {
      id: 6,
      title: "休闲钓鱼",
      description: "轻松的钓鱼游戏，享受宁静时光",
      status: "已完成",
      type: "休闲游戏",
      thumbnail: "/placeholder.svg?height=200&width=300",
      createdAt: "2024-01-05",
      lastModified: "2周前",
      plays: 1800,
      size: "12.3MB",
      tags: ["休闲", "模拟", "放松"],
    },
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case "已完成":
        return "bg-green-100 text-green-700"
      case "生成中":
        return "bg-blue-100 text-blue-700"
      case "草稿":
        return "bg-gray-100 text-gray-700"
      default:
        return "bg-gray-100 text-gray-700"
    }
  }

  const filteredProjects = projects.filter((project) => {
    const matchesSearch =
      project.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      project.description.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesFilter = selectedFilter === "all" || project.status === selectedFilter
    return matchesSearch && matchesFilter
  })

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold mb-2">项目管理</h1>
            <p className="text-gray-600">管理您的所有游戏项目</p>
          </div>
          <Link href="/create">
            <Button size="lg">
              <Plus className="w-5 h-5 mr-2" />
              创建新项目
            </Button>
          </Link>
        </div>

        {/* Search and Filters */}
        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
              <div className="flex-1 flex gap-4">
                <div className="relative flex-1 max-w-md">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    placeholder="搜索项目名称或描述..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>

                <Tabs value={selectedFilter} onValueChange={setSelectedFilter}>
                  <TabsList>
                    <TabsTrigger value="all">全部</TabsTrigger>
                    <TabsTrigger value="已完成">已完成</TabsTrigger>
                    <TabsTrigger value="生成中">生成中</TabsTrigger>
                    <TabsTrigger value="草稿">草稿</TabsTrigger>
                  </TabsList>
                </Tabs>
              </div>

              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm">
                  <Filter className="w-4 h-4 mr-2" />
                  筛选
                </Button>
                <div className="flex border rounded-lg">
                  <Button
                    variant={viewMode === "grid" ? "default" : "ghost"}
                    size="sm"
                    onClick={() => setViewMode("grid")}
                  >
                    <Grid3X3 className="w-4 h-4" />
                  </Button>
                  <Button
                    variant={viewMode === "list" ? "default" : "ghost"}
                    size="sm"
                    onClick={() => setViewMode("list")}
                  >
                    <List className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Projects Grid/List */}
        {viewMode === "grid" ? (
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredProjects.map((project) => (
              <Card key={project.id} className="hover:shadow-lg transition-shadow cursor-pointer group">
                <div className="relative">
                  <img
                    src={project.thumbnail || "/placeholder.svg"}
                    alt={project.title}
                    className="w-full h-48 object-cover rounded-t-lg"
                  />
                  <div className="absolute top-3 left-3">
                    <Badge className={getStatusColor(project.status)}>{project.status}</Badge>
                  </div>
                  <div className="absolute top-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity">
                    <Button variant="secondary" size="sm">
                      <MoreHorizontal className="w-4 h-4" />
                    </Button>
                  </div>
                  {project.status === "已完成" && (
                    <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center rounded-t-lg">
                      <Button variant="secondary" size="sm">
                        <Play className="w-4 h-4 mr-2" />
                        预览
                      </Button>
                    </div>
                  )}
                </div>

                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <CardTitle className="text-lg mb-1">{project.title}</CardTitle>
                      <CardDescription className="text-sm line-clamp-2">{project.description}</CardDescription>
                    </div>
                  </div>
                </CardHeader>

                <CardContent className="pt-0">
                  <div className="flex flex-wrap gap-1 mb-3">
                    {project.tags.map((tag) => (
                      <Badge key={tag} variant="secondary" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>

                  <div className="flex items-center justify-between text-sm text-gray-600 mb-3">
                    <div className="flex items-center gap-1">
                      <Eye className="w-4 h-4" />
                      {project.plays.toLocaleString()}
                    </div>
                    <div className="flex items-center gap-1">
                      <Clock className="w-4 h-4" />
                      {project.lastModified}
                    </div>
                  </div>

                  <div className="flex gap-2">
                    <Button variant="outline" size="sm" className="flex-1 bg-transparent">
                      <Edit className="w-4 h-4 mr-1" />
                      编辑
                    </Button>
                    {project.status === "已完成" && (
                      <Button variant="outline" size="sm">
                        <Download className="w-4 h-4" />
                      </Button>
                    )}
                    <Button variant="outline" size="sm">
                      <Share2 className="w-4 h-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <Card>
            <CardContent className="p-0">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="border-b">
                    <tr className="text-left">
                      <th className="p-4 font-medium">项目</th>
                      <th className="p-4 font-medium">状态</th>
                      <th className="p-4 font-medium">类型</th>
                      <th className="p-4 font-medium">播放次数</th>
                      <th className="p-4 font-medium">大小</th>
                      <th className="p-4 font-medium">最后修改</th>
                      <th className="p-4 font-medium">操作</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredProjects.map((project) => (
                      <tr key={project.id} className="border-b hover:bg-gray-50">
                        <td className="p-4">
                          <div className="flex items-center gap-3">
                            <img
                              src={project.thumbnail || "/placeholder.svg"}
                              alt={project.title}
                              className="w-12 h-8 object-cover rounded"
                            />
                            <div>
                              <div className="font-medium">{project.title}</div>
                              <div className="text-sm text-gray-600 line-clamp-1">{project.description}</div>
                            </div>
                          </div>
                        </td>
                        <td className="p-4">
                          <Badge className={getStatusColor(project.status)}>{project.status}</Badge>
                        </td>
                        <td className="p-4 text-sm text-gray-600">{project.type}</td>
                        <td className="p-4 text-sm text-gray-600">{project.plays.toLocaleString()}</td>
                        <td className="p-4 text-sm text-gray-600">{project.size}</td>
                        <td className="p-4 text-sm text-gray-600">{project.lastModified}</td>
                        <td className="p-4">
                          <div className="flex items-center gap-1">
                            <Button variant="ghost" size="sm">
                              <Edit className="w-4 h-4" />
                            </Button>
                            {project.status === "已完成" && (
                              <Button variant="ghost" size="sm">
                                <Download className="w-4 h-4" />
                              </Button>
                            )}
                            <Button variant="ghost" size="sm">
                              <Share2 className="w-4 h-4" />
                            </Button>
                            <Button variant="ghost" size="sm" className="text-red-600 hover:text-red-700">
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Empty State */}
        {filteredProjects.length === 0 && (
          <Card className="text-center py-12">
            <CardContent>
              <div className="text-gray-400 mb-4">
                <Grid3X3 className="w-16 h-16 mx-auto" />
              </div>
              <h3 className="text-lg font-medium mb-2">没有找到项目</h3>
              <p className="text-gray-600 mb-6">{searchQuery ? "尝试调整搜索条件" : "开始创建您的第一个游戏项目"}</p>
              <Link href="/create">
                <Button>
                  <Plus className="w-4 h-4 mr-2" />
                  创建新项目
                </Button>
              </Link>
            </CardContent>
          </Card>
        )}

        {/* Stats */}
        <div className="grid md:grid-cols-4 gap-4 mt-8">
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-blue-600">{projects.length}</div>
              <div className="text-sm text-gray-600">总项目数</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-green-600">
                {projects.filter((p) => p.status === "已完成").length}
              </div>
              <div className="text-sm text-gray-600">已完成</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-orange-600">
                {projects.reduce((sum, p) => sum + p.plays, 0).toLocaleString()}
              </div>
              <div className="text-sm text-gray-600">总播放次数</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-purple-600">
                {projects.reduce((sum, p) => sum + Number.parseFloat(p.size.replace("MB", "")), 0).toFixed(1)}MB
              </div>
              <div className="text-sm text-gray-600">总存储空间</div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
