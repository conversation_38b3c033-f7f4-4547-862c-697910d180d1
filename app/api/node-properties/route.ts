import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs/promises';
import path from 'path';

interface NodeProperty {
  id: string;
  name: string;
  type: string;
  position: { x: number; y: number; z: number };
  rotation: { x: number; y: number; z: number };
  scaling: { x: number; y: number; z: number };
  modelPath?: string;
  color?: { r: number; g: number; b: number };
  // 摄像机特有属性
  fov?: number;
  viewLocked?: boolean;
  models?: Array<{
    id: string;
    name: string;
    filePath: string;
    fileSize: number;
    fileType: string;
    isDefault: boolean;
    uploadedAt: string;
  }>;
  materials?: Array<{
    id: string;
    name: string;
    filePath: string;
    fileSize: number;
    fileType: string;
    isDefault: boolean;
    uploadedAt: string;
    materialType?: string;
  }>;
  animations?: Array<{
    id: string;
    name: string;
    filePath: string;
    duration: number;
    isDefault: boolean;
    uploadedAt: string;
    clips: Array<{
      name: string;
      duration: number;
      tracks: number;
    }>;
  }>;
  lastModified: string;
}

interface NodePropertiesStore {
  nodes: NodeProperty[];
  lastUpdated: string;
}

const NODE_PROPERTIES_FILE = path.join(process.cwd(), 'data', 'node-properties.json');

// 确保数据目录存在
async function ensureDataDir(): Promise<void> {
  const dataDir = path.dirname(NODE_PROPERTIES_FILE);
  try {
    await fs.access(dataDir);
  } catch {
    await fs.mkdir(dataDir, { recursive: true });
  }
}

// 读取节点属性
async function readNodeProperties(): Promise<NodePropertiesStore> {
  try {
    await ensureDataDir();
    const data = await fs.readFile(NODE_PROPERTIES_FILE, 'utf-8');
    return JSON.parse(data);
  } catch {
    return {
      nodes: [],
      lastUpdated: new Date().toISOString()
    };
  }
}

// 写入节点属性
async function writeNodeProperties(store: NodePropertiesStore): Promise<void> {
  await ensureDataDir();
  store.lastUpdated = new Date().toISOString();
  await fs.writeFile(NODE_PROPERTIES_FILE, JSON.stringify(store, null, 2), 'utf-8');
}

// GET - 获取所有节点属性
export async function GET(): Promise<NextResponse> {
  try {
    console.log('[API] 获取节点属性');
    
    const store = await readNodeProperties();
    
    return NextResponse.json({
      success: true,
      data: {
        nodes: store.nodes,
        totalNodes: store.nodes.length,
        lastUpdated: store.lastUpdated
      }
    });

  } catch (error) {
    console.error('[API] 获取节点属性失败:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '未知错误'
    }, { status: 500 });
  }
}

// POST - 保存节点属性
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    const { nodeProperty } = await request.json();
    
    if (!nodeProperty || !nodeProperty.id) {
      return NextResponse.json({
        success: false,
        error: '缺少必要参数：nodeProperty或nodeProperty.id'
      }, { status: 400 });
    }

    console.log(`[API] 保存节点属性: ${nodeProperty.id}`);

    const store = await readNodeProperties();
    
    // 查找是否已存在该节点
    const existingIndex = store.nodes.findIndex(node => node.id === nodeProperty.id);
    
    const nodeToSave: NodeProperty = {
      id: nodeProperty.id,
      name: nodeProperty.name || `节点_${nodeProperty.id}`,
      type: nodeProperty.type || 'unknown',
      position: {
        x: Number(nodeProperty.position?.x || 0),
        y: Number(nodeProperty.position?.y || 0),
        z: Number(nodeProperty.position?.z || 0)
      },
      rotation: {
        x: Number(nodeProperty.rotation?.x || 0),
        y: Number(nodeProperty.rotation?.y || 0),
        z: Number(nodeProperty.rotation?.z || 0)
      },
      scaling: {
        x: Number(nodeProperty.scaling?.x || 1),
        y: Number(nodeProperty.scaling?.y || 1),
        z: Number(nodeProperty.scaling?.z || 1)
      },
      modelPath: nodeProperty.modelPath,
      color: nodeProperty.color ? {
        r: Number(nodeProperty.color.r || 1),
        g: Number(nodeProperty.color.g || 1),
        b: Number(nodeProperty.color.b || 1)
      } : undefined,
      // 保存摄像机特有属性
      fov: nodeProperty.fov !== undefined ? Number(nodeProperty.fov) : undefined,
      viewLocked: nodeProperty.viewLocked !== undefined ? Boolean(nodeProperty.viewLocked) : undefined,
      // 保存模型信息
      models: nodeProperty.models ? nodeProperty.models.map((model: any) => ({
        id: model.id,
        name: model.name,
        filePath: model.filePath,
        fileSize: Number(model.fileSize || 0),
        fileType: model.fileType,
        isDefault: Boolean(model.isDefault),
        uploadedAt: model.uploadedAt instanceof Date ? model.uploadedAt.toISOString() : model.uploadedAt
      })) : undefined,
      // 保存材质信息
      materials: nodeProperty.materials ? nodeProperty.materials.map((material: any) => ({
        id: material.id,
        name: material.name,
        filePath: material.filePath,
        fileSize: Number(material.fileSize || 0),
        fileType: material.fileType,
        isDefault: Boolean(material.isDefault),
        uploadedAt: material.uploadedAt instanceof Date ? material.uploadedAt.toISOString() : material.uploadedAt,
        materialType: material.materialType
      })) : undefined,
      // 保存动画信息
      animations: nodeProperty.animations ? nodeProperty.animations.map((animation: any) => ({
        id: animation.id,
        name: animation.name,
        filePath: animation.filePath,
        duration: Number(animation.duration || 0),
        isDefault: Boolean(animation.isDefault),
        uploadedAt: animation.uploadedAt instanceof Date ? animation.uploadedAt.toISOString() : animation.uploadedAt,
        clips: animation.clips || []
      })) : undefined,
      lastModified: new Date().toISOString()
    };

    if (existingIndex >= 0) {
      // 更新现有节点
      store.nodes[existingIndex] = nodeToSave;
      console.log(`[API] 更新节点属性: ${nodeProperty.id}`);
    } else {
      // 添加新节点
      store.nodes.push(nodeToSave);
      console.log(`[API] 新增节点属性: ${nodeProperty.id}`);
    }

    await writeNodeProperties(store);

    return NextResponse.json({
      success: true,
      message: existingIndex >= 0 ? '节点属性更新成功' : '节点属性保存成功',
      data: {
        nodeProperty: nodeToSave,
        totalNodes: store.nodes.length
      }
    });

  } catch (error) {
    console.error('[API] 保存节点属性失败:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '未知错误'
    }, { status: 500 });
  }
}

// DELETE - 删除节点属性
export async function DELETE(request: NextRequest): Promise<NextResponse> {
  try {
    const { searchParams } = new URL(request.url);
    const nodeId = searchParams.get('nodeId');

    if (!nodeId) {
      return NextResponse.json({
        success: false,
        error: '缺少nodeId参数'
      }, { status: 400 });
    }

    console.log(`[API] 删除节点属性: ${nodeId}`);

    const store = await readNodeProperties();
    const nodeIndex = store.nodes.findIndex(node => node.id === nodeId);
    
    if (nodeIndex === -1) {
      return NextResponse.json({
        success: false,
        error: '节点不存在'
      }, { status: 404 });
    }

    const deletedNode = store.nodes[nodeIndex];
    store.nodes.splice(nodeIndex, 1);

    await writeNodeProperties(store);

    console.log(`[API] 成功删除节点属性: ${deletedNode.name}`);

    return NextResponse.json({
      success: true,
      message: '节点属性删除成功',
      data: {
        deletedNode,
        remainingNodes: store.nodes.length
      }
    });

  } catch (error) {
    console.error('[API] 删除节点属性失败:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '未知错误'
    }, { status: 500 });
  }
} 