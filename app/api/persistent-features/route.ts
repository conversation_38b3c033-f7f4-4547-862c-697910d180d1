import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs/promises';
import path from 'path';

interface PersistentFeature {
  id: string;
  name: string;
  description: string;
  scriptContent: string;
  addedAt: string;
  isActive: boolean;
}

const FEATURES_FILE = path.join(process.cwd(), 'data', 'persistent-features.json');

// 确保数据目录存在
async function ensureDataDir(): Promise<void> {
  const dataDir = path.dirname(FEATURES_FILE);
  try {
    await fs.access(dataDir);
  } catch {
    await fs.mkdir(dataDir, { recursive: true });
  }
}

// 读取功能列表
async function readFeatures(): Promise<PersistentFeature[]> {
  try {
    await ensureDataDir();
    const data = await fs.readFile(FEATURES_FILE, 'utf-8');
    return JSON.parse(data);
  } catch {
    return [];
  }
}

// 写入功能列表
async function writeFeatures(features: PersistentFeature[]): Promise<void> {
  await ensureDataDir();
  await fs.writeFile(FEATURES_FILE, JSON.stringify(features, null, 2), 'utf-8');
}

// GET - 获取所有持久化功能
export async function GET(): Promise<NextResponse> {
  try {
    const features = await readFeatures();
    
    return NextResponse.json({
      success: true,
      data: {
        features,
        totalCount: features.length,
        activeCount: features.filter(f => f.isActive).length
      }
    });

  } catch (error) {
    console.error('[API] 获取持久化功能失败:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '未知错误'
    }, { status: 500 });
  }
}

// POST - 添加新的持久化功能
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    const { id, name, description, scriptContent } = await request.json();

    if (!id || !name || !scriptContent) {
      return NextResponse.json({
        success: false,
        error: '缺少必要参数：id、name或scriptContent'
      }, { status: 400 });
    }

    console.log(`[API] 添加持久化功能: ${name}`);

    const features = await readFeatures();
    
    // 检查是否已存在
    const existingIndex = features.findIndex(f => f.id === id);
    
    const newFeature: PersistentFeature = {
      id,
      name,
      description: description || '无描述',
      scriptContent,
      addedAt: new Date().toISOString(),
      isActive: true
    };

    if (existingIndex >= 0) {
      // 更新现有功能
      features[existingIndex] = newFeature;
      console.log(`[API] 更新持久化功能: ${name}`);
    } else {
      // 添加新功能
      features.push(newFeature);
      console.log(`[API] 新增持久化功能: ${name}`);
    }

    await writeFeatures(features);

    return NextResponse.json({
      success: true,
      message: existingIndex >= 0 ? '功能更新成功' : '功能添加成功',
      data: {
        feature: newFeature,
        totalCount: features.length
      }
    });

  } catch (error) {
    console.error('[API] 添加持久化功能失败:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '未知错误'
    }, { status: 500 });
  }
}

// DELETE - 删除持久化功能
export async function DELETE(request: NextRequest): Promise<NextResponse> {
  try {
    const { searchParams } = new URL(request.url);
    const featureId = searchParams.get('featureId');

    if (!featureId) {
      return NextResponse.json({
        success: false,
        error: '缺少featureId参数'
      }, { status: 400 });
    }

    console.log(`[API] 删除持久化功能: ${featureId}`);

    const features = await readFeatures();
    const featureIndex = features.findIndex(f => f.id === featureId);
    
    if (featureIndex === -1) {
      return NextResponse.json({
        success: false,
        error: '功能不存在'
      }, { status: 404 });
    }

    const deletedFeature = features[featureIndex];
    features.splice(featureIndex, 1);

    await writeFeatures(features);

    console.log(`[API] 成功删除持久化功能: ${deletedFeature.name}`);

    return NextResponse.json({
      success: true,
      message: '功能删除成功',
      data: {
        deletedFeature,
        remainingCount: features.length
      }
    });

  } catch (error) {
    console.error('[API] 删除持久化功能失败:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '未知错误'
    }, { status: 500 });
  }
} 