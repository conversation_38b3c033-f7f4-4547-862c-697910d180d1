import { NextRequest, NextResponse } from 'next/server'
import fs from 'fs/promises'
import path from 'path'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const gameId = searchParams.get('gameId')
    
    if (!gameId) {
      return NextResponse.json({ 
        exists: false, 
        error: 'Missing gameId parameter' 
      }, { status: 400 })
    }
    
    // 检查components/generated目录中的组件文件
    const componentPath = path.join(process.cwd(), 'components', 'generated', `${gameId}.tsx`)
    
    try {
      await fs.access(componentPath)
      console.log(`[API] 组件文件存在: ${componentPath}`)
      
      return NextResponse.json({ 
        exists: true, 
        componentPath: componentPath,
        gameId 
      })
    } catch (error) {
      console.log(`[API] 组件文件不存在: ${componentPath}`)
      
      return NextResponse.json({ 
        exists: false, 
        componentPath: componentPath,
        gameId,
        error: 'Component file not found'
      })
    }
    
  } catch (error) {
    console.error('[API] 检查组件文件时出错:', error)
    
    return NextResponse.json({ 
      exists: false, 
      error: 'Internal server error' 
    }, { status: 500 })
  }
}
