/**
 * HTML导出API端点
 * 处理PlayableAgent场景的HTML5广告导出请求
 */

import { NextRequest, NextResponse } from 'next/server';
import { ExportOptions } from '../../../src/services/HTMLExporter';

export interface ExportRequest {
  sceneData: {
    nodes: Record<string, {
      position?: { x: number; y: number; z: number };
      rotation?: { x: number; y: number; z: number };
      scale?: { x: number; y: number; z: number };
      color?: string;
      model?: string;
      material?: string;
      animation?: string;
    }>;
    scripts: Array<{
      id: string;
      name: string;
      content: string;
      isActive: boolean;
    }>;
    camera: {
      position: [number, number, number];
      target: [number, number, number];
    };
    lights: Array<{
      type: string;
      color: number;
      intensity: number;
      position: [number, number, number];
      target?: [number, number, number];
    }>;
    background: string;
  };
  options: ExportOptions;
}

/**
 * POST /api/export-html
 * 导出HTML5广告文件
 */
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    const body: ExportRequest = await request.json();
    const { sceneData, options } = body;

    // 验证请求数据
    if (!sceneData || !sceneData.nodes) {
      return NextResponse.json(
        { error: '缺少场景数据' },
        { status: 400 }
      );
    }

    // 生成HTML内容
    const htmlContent = await generateStandaloneHTML(sceneData, options);

    // 检查文件大小
    const fileSizeKB = new Blob([htmlContent]).size / 1024;
    const maxSizeKB = (options.maxFileSize || 5) * 1024;

    if (fileSizeKB > maxSizeKB) {
      return NextResponse.json(
        { 
          error: `文件大小超出限制`,
          details: `当前大小: ${Math.round(fileSizeKB)}KB, 最大限制: ${Math.round(maxSizeKB)}KB`
        },
        { status: 413 }
      );
    }

    // 返回HTML内容
    return new NextResponse(htmlContent, {
      status: 200,
      headers: {
        'Content-Type': 'text/html; charset=utf-8',
        'Content-Disposition': 'attachment; filename="playable-game.html"',
        'Content-Length': htmlContent.length.toString(),
        'X-File-Size-KB': Math.round(fileSizeKB).toString()
      }
    });

  } catch (error) {
    console.error('HTML导出API错误:', error);
    return NextResponse.json(
      { 
        error: '导出失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}

/**
 * 生成独立的HTML文件
 */
async function generateStandaloneHTML(
  sceneData: ExportRequest['sceneData'],
  options: ExportOptions
): Promise<string> {
  // 获取Three.js库代码
  const threeJsCode = await getThreeJsLibrary();
  
  // 生成场景初始化代码
  const sceneInitCode = generateSceneCode(sceneData);
  
  // 生成交互代码
  const interactionCode = generateInteractionCode(sceneData);

  // 生成完整HTML
  const html = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>PlayableAgent Game</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            overflow: hidden;
            background: #000;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            touch-action: manipulation;
        }
        
        #gameContainer {
            width: 100vw;
            height: 100vh;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        #gameCanvas {
            display: block;
            max-width: 100%;
            max-height: 100%;
            background: ${sceneData.background || '#000000'};
        }
        
        #ui {
            position: absolute;
            top: 20px;
            left: 20px;
            color: white;
            z-index: 100;
            font-size: 14px;
            text-shadow: 0 1px 2px rgba(0,0,0,0.8);
        }
        
        #cta {
            position: absolute;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            background: linear-gradient(135deg, #ff6b35, #f7931e);
            color: white;
            border: none;
            padding: 16px 32px;
            font-size: 18px;
            font-weight: bold;
            border-radius: 30px;
            cursor: pointer;
            z-index: 100;
            box-shadow: 0 4px 20px rgba(255, 107, 53, 0.4);
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
            min-width: 200px;
        }
        
        #cta:hover {
            background: linear-gradient(135deg, #e55a2b, #e8851d);
            transform: translateX(-50%) translateY(-3px);
            box-shadow: 0 6px 25px rgba(255, 107, 53, 0.6);
        }
        
        #cta:active {
            transform: translateX(-50%) translateY(-1px);
            box-shadow: 0 2px 10px rgba(255, 107, 53, 0.8);
        }
        
        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 18px;
            z-index: 50;
        }
        
        .loading::after {
            content: '';
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #ffffff40;
            border-top: 2px solid #ffffff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-left: 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        @media (max-width: 768px) {
            #ui {
                font-size: 12px;
                top: 10px;
                left: 10px;
            }
            
            #cta {
                bottom: 20px;
                padding: 14px 28px;
                font-size: 16px;
                min-width: 180px;
            }
        }
    </style>
</head>
<body>
    <div id="gameContainer">
        <div class="loading">加载中...</div>
        <canvas id="gameCanvas"></canvas>
        <div id="ui" style="display: none;">
            <div>🎮 点击拖拽控制视角</div>
            <div>⌨️ WASD键移动角色</div>
        </div>
        <button id="cta" onclick="handleCTA()" style="display: none;">
            🎯 立即下载游戏
        </button>
    </div>

    <script>
        // 场景数据
        const SCENE_DATA = ${JSON.stringify(sceneData, null, 2)};
        
        // Three.js库
        ${threeJsCode}
        
        // 场景初始化
        ${sceneInitCode}
        
        // 交互逻辑
        ${interactionCode}
        
        // CTA处理函数
        function handleCTA() {
            // 广告平台集成
            try {
                // MRAID 2.0 支持
                if (typeof mraid !== 'undefined' && mraid.open) {
                    mraid.open('https://play.google.com/store/apps/details?id=your.app.id');
                    return;
                }
                
                // Google Ads ExitAPI
                if (typeof ExitApi !== 'undefined' && ExitApi.exit) {
                    ExitApi.exit();
                    return;
                }
                
                // Facebook Playable Ads
                if (typeof FbPlayableAd !== 'undefined' && FbPlayableAd.onCTAClick) {
                    FbPlayableAd.onCTAClick();
                    return;
                }
                
                // Unity Ads
                if (typeof gameReady !== 'undefined') {
                    gameReady();
                    return;
                }
                
                // 备用方案
                window.open('https://play.google.com/store/apps/details?id=your.app.id', '_blank');
            } catch (error) {
                console.error('CTA处理错误:', error);
                // 最终备用方案
                window.open('https://play.google.com/store/apps/details?id=your.app.id', '_blank');
            }
        }
        
        // 游戏初始化
        function initializeGame() {
            try {
                // 隐藏加载提示
                const loading = document.querySelector('.loading');
                if (loading) loading.style.display = 'none';
                
                // 显示UI和CTA
                const ui = document.getElementById('ui');
                const cta = document.getElementById('cta');
                if (ui) ui.style.display = 'block';
                if (cta) cta.style.display = 'block';
                
                // 启动游戏
                if (typeof startGame === 'function') {
                    startGame();
                }
                
                // 通知广告平台游戏已准备就绪
                if (typeof mraid !== 'undefined' && mraid.isViewable) {
                    console.log('MRAID: 游戏已准备就绪');
                }
                
            } catch (error) {
                console.error('游戏初始化错误:', error);
            }
        }
        
        // 页面加载完成后初始化
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initializeGame);
        } else {
            initializeGame();
        }
        
        // 错误处理
        window.addEventListener('error', function(event) {
            console.error('全局错误:', event.error);
        });
        
        // 防止页面滚动（移动设备）
        document.addEventListener('touchmove', function(event) {
            event.preventDefault();
        }, { passive: false });
        
    </script>
</body>
</html>`;

  return options.compressOutput ? compressHTML(html) : html;
}

/**
 * 获取Three.js库代码
 */
async function getThreeJsLibrary(): Promise<string> {
  try {
    // 尝试从CDN获取Three.js代码（生产环境应该使用本地文件）
    const threeJsUrl = 'https://unpkg.com/three@0.178.0/build/three.min.js';
    const response = await fetch(threeJsUrl);

    if (response.ok) {
      const threeJsCode = await response.text();
      return `
        // Three.js 核心库 (v0.178.0)
        ${threeJsCode}

        // 确保THREE对象全局可用
        if (typeof THREE !== 'undefined') {
          console.log('Three.js v' + THREE.REVISION + ' 已成功加载');
        } else {
          console.error('Three.js 加载失败');
        }
      `;
    } else {
      throw new Error('CDN获取失败');
    }
  } catch (error) {
    console.warn('从CDN获取Three.js失败，使用备用方案:', error);
    return getThreeJsFallback();
  }
}

/**
 * Three.js备用代码（简化版本用于演示）
 */
function getThreeJsFallback(): string {
  return `
    // Three.js 备用实现（简化版本）
    console.log('使用Three.js备用实现');

    window.THREE = {
      REVISION: '178',

      // 场景类
      Scene: function() {
        this.children = [];
        this.background = null;
        this.fog = null;

        this.add = function(object) {
          this.children.push(object);
          object.parent = this;
        };

        this.remove = function(object) {
          const index = this.children.indexOf(object);
          if (index > -1) {
            this.children.splice(index, 1);
            object.parent = null;
          }
        };

        this.traverse = function(callback) {
          callback(this);
          for (let i = 0; i < this.children.length; i++) {
            if (this.children[i].traverse) {
              this.children[i].traverse(callback);
            }
          }
        };
      },

      // 透视相机
      PerspectiveCamera: function(fov, aspect, near, far) {
        this.fov = fov || 75;
        this.aspect = aspect || 1;
        this.near = near || 0.1;
        this.far = far || 1000;
        this.position = { x: 0, y: 0, z: 5 };
        this.rotation = { x: 0, y: 0, z: 0 };

        this.position.set = function(x, y, z) {
          this.x = x; this.y = y; this.z = z;
        };

        this.updateProjectionMatrix = function() {
          // 简化的投影矩阵更新
        };
      },

      // WebGL渲染器
      WebGLRenderer: function(options) {
        options = options || {};
        this.domElement = options.canvas || document.createElement('canvas');
        this.shadowMap = { enabled: false, type: null };

        this.setSize = function(width, height) {
          this.domElement.width = width;
          this.domElement.height = height;
          this.domElement.style.width = width + 'px';
          this.domElement.style.height = height + 'px';
        };

        this.render = function(scene, camera) {
          // 简化的渲染实现
          const ctx = this.domElement.getContext('2d');
          if (ctx) {
            // 清除画布
            ctx.fillStyle = scene.background || '#000000';
            ctx.fillRect(0, 0, this.domElement.width, this.domElement.height);

            // 简单的3D效果模拟
            ctx.fillStyle = '#00ff00';
            ctx.fillRect(
              this.domElement.width / 2 - 25,
              this.domElement.height / 2 - 25,
              50, 50
            );

            // 添加一些动态效果
            const time = Date.now() * 0.001;
            ctx.fillStyle = '#ff6b35';
            ctx.beginPath();
            ctx.arc(
              this.domElement.width / 2 + Math.sin(time) * 100,
              this.domElement.height / 2 + Math.cos(time) * 50,
              20, 0, Math.PI * 2
            );
            ctx.fill();
          }
        };
      },

      // 几何体
      BoxGeometry: function(width, height, depth) {
        this.width = width || 1;
        this.height = height || 1;
        this.depth = depth || 1;
        this.type = 'BoxGeometry';
      },

      PlaneGeometry: function(width, height) {
        this.width = width || 1;
        this.height = height || 1;
        this.type = 'PlaneGeometry';
      },

      // 材质
      MeshPhongMaterial: function(parameters) {
        parameters = parameters || {};
        this.color = parameters.color || 0x00ff00;
        this.type = 'MeshPhongMaterial';
      },

      MeshBasicMaterial: function(parameters) {
        parameters = parameters || {};
        this.color = parameters.color || 0x00ff00;
        this.type = 'MeshBasicMaterial';
      },

      // 网格
      Mesh: function(geometry, material) {
        this.geometry = geometry;
        this.material = material;
        this.position = { x: 0, y: 0, z: 0 };
        this.rotation = { x: 0, y: 0, z: 0 };
        this.scale = { x: 1, y: 1, z: 1 };
        this.castShadow = false;
        this.receiveShadow = false;
        this.parent = null;

        this.position.set = function(x, y, z) {
          this.x = x; this.y = y; this.z = z;
        };

        this.rotation.set = function(x, y, z) {
          this.x = x; this.y = y; this.z = z;
        };

        this.scale.set = function(x, y, z) {
          this.x = x; this.y = y; this.z = z;
        };
      },

      // 光照
      AmbientLight: function(color, intensity) {
        this.color = { getHex: function() { return color || 0xffffff; } };
        this.intensity = intensity || 1;
        this.type = 'AmbientLight';
        this.isLight = true;
      },

      DirectionalLight: function(color, intensity) {
        this.color = { getHex: function() { return color || 0xffffff; } };
        this.intensity = intensity || 1;
        this.type = 'DirectionalLight';
        this.position = { x: 0, y: 1, z: 0 };
        this.target = { position: { x: 0, y: 0, z: 0 } };
        this.castShadow = false;
        this.isLight = true;

        this.position.set = function(x, y, z) {
          this.x = x; this.y = y; this.z = z;
        };
      },

      // 颜色
      Color: function(color) {
        this.r = 1; this.g = 1; this.b = 1;

        if (typeof color === 'string') {
          this.setHex(color);
        } else if (typeof color === 'number') {
          this.setHex(color);
        }

        this.setHex = function(hex) {
          if (typeof hex === 'string') {
            hex = parseInt(hex.replace('#', ''), 16);
          }
          this.r = ((hex >> 16) & 255) / 255;
          this.g = ((hex >> 8) & 255) / 255;
          this.b = (hex & 255) / 255;
        };

        this.getHexString = function() {
          const r = Math.round(this.r * 255);
          const g = Math.round(this.g * 255);
          const b = Math.round(this.b * 255);
          return ((r << 16) | (g << 8) | b).toString(16).padStart(6, '0');
        };
      },

      // 常量
      PCFSoftShadowMap: 'PCFSoftShadowMap'
    };

    console.log('Three.js 备用实现已加载');
  `;
}

/**
 * 生成场景代码
 */
function generateSceneCode(sceneData: ExportRequest['sceneData']): string {
  return `
    // 全局变量
    let scene, camera, renderer, gameObjects = {};
    let animationId;

    // 场景初始化函数
    function startGame() {
      console.log('开始初始化游戏场景');

      try {
        // 创建场景
        scene = new THREE.Scene();
        scene.background = new THREE.Color('${sceneData.background}');

        // 创建相机
        camera = new THREE.PerspectiveCamera(
          75,
          window.innerWidth / window.innerHeight,
          0.1,
          1000
        );
        camera.position.set(${sceneData.camera.position.join(', ')});

        // 创建渲染器
        const canvas = document.getElementById('gameCanvas');
        renderer = new THREE.WebGLRenderer({
          canvas: canvas,
          antialias: true,
          alpha: true
        });
        renderer.setSize(window.innerWidth, window.innerHeight);
        renderer.shadowMap.enabled = true;
        renderer.shadowMap.type = THREE.PCFSoftShadowMap;

        // 创建光照
        ${generateLightingCode(sceneData.lights)}

        // 创建场景对象
        ${generateSceneObjectsCode(sceneData.nodes)}

        // 执行脚本
        ${generateScriptExecutionCode(sceneData.scripts)}

        // 开始渲染循环
        animate();

        // 处理窗口大小变化
        window.addEventListener('resize', onWindowResize);

        console.log('游戏场景初始化完成');

      } catch (error) {
        console.error('场景初始化失败:', error);
      }
    }

    // 渲染循环
    function animate() {
      animationId = requestAnimationFrame(animate);

      // 简单的动画效果
      const time = Date.now() * 0.001;

      // 旋转场景中的对象
      Object.values(gameObjects).forEach((obj, index) => {
        if (obj && obj.rotation) {
          obj.rotation.y = time + index * 0.5;
        }
      });

      // 渲染场景
      if (renderer && scene && camera) {
        renderer.render(scene, camera);
      }
    }

    // 窗口大小变化处理
    function onWindowResize() {
      if (camera && renderer) {
        camera.aspect = window.innerWidth / window.innerHeight;
        camera.updateProjectionMatrix();
        renderer.setSize(window.innerWidth, window.innerHeight);
      }
    }

    // 清理函数
    function cleanup() {
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
    }

    // 页面卸载时清理
    window.addEventListener('beforeunload', cleanup);
  `;
}

/**
 * 生成光照代码
 */
function generateLightingCode(lights: ExportRequest['sceneData']['lights']): string {
  if (!lights || lights.length === 0) {
    return `
      // 默认光照
      const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
      scene.add(ambientLight);

      const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
      directionalLight.position.set(10, 10, 5);
      directionalLight.castShadow = true;
      scene.add(directionalLight);
    `;
  }

  return lights.map((light, index) => {
    switch (light.type) {
      case 'AmbientLight':
        return `
          const ambientLight_${index} = new THREE.AmbientLight(0x${light.color.toString(16)}, ${light.intensity});
          scene.add(ambientLight_${index});
        `;
      case 'DirectionalLight':
        return `
          const directionalLight_${index} = new THREE.DirectionalLight(0x${light.color.toString(16)}, ${light.intensity});
          directionalLight_${index}.position.set(${light.position.join(', ')});
          directionalLight_${index}.castShadow = true;
          scene.add(directionalLight_${index});
        `;
      default:
        return '';
    }
  }).join('\n');
}

/**
 * 生成场景对象代码
 */
function generateSceneObjectsCode(nodes: ExportRequest['sceneData']['nodes']): string {
  if (!nodes || Object.keys(nodes).length === 0) {
    return `
      // 默认场景对象
      // 地面
      const groundGeometry = new THREE.PlaneGeometry(20, 20);
      const groundMaterial = new THREE.MeshPhongMaterial({ color: 0x999999 });
      const ground = new THREE.Mesh(groundGeometry, groundMaterial);
      ground.rotation.x = -Math.PI / 2;
      ground.receiveShadow = true;
      scene.add(ground);
      gameObjects['ground'] = ground;

      // 示例方块
      const boxGeometry = new THREE.BoxGeometry(1, 1, 1);
      const boxMaterial = new THREE.MeshPhongMaterial({ color: 0x00ff00 });
      const box = new THREE.Mesh(boxGeometry, boxMaterial);
      box.position.set(0, 0.5, 0);
      box.castShadow = true;
      scene.add(box);
      gameObjects['box'] = box;
    `;
  }

  return Object.entries(nodes).map(([nodeId, nodeData]) => {
    const pos = nodeData.position || { x: 0, y: 0, z: 0 };
    const rot = nodeData.rotation || { x: 0, y: 0, z: 0 };
    const scale = nodeData.scale || { x: 1, y: 1, z: 1 };
    const color = nodeData.color || '#00ff00';

    return `
      // 创建节点: ${nodeId}
      const geometry_${nodeId} = new THREE.BoxGeometry(${scale.x}, ${scale.y}, ${scale.z});
      const material_${nodeId} = new THREE.MeshPhongMaterial({
        color: 0x${color.replace('#', '')}
      });
      const mesh_${nodeId} = new THREE.Mesh(geometry_${nodeId}, material_${nodeId});
      mesh_${nodeId}.position.set(${pos.x}, ${pos.y}, ${pos.z});
      mesh_${nodeId}.rotation.set(${rot.x}, ${rot.y}, ${rot.z});
      mesh_${nodeId}.castShadow = true;
      mesh_${nodeId}.receiveShadow = true;
      scene.add(mesh_${nodeId});
      gameObjects['${nodeId}'] = mesh_${nodeId};
    `;
  }).join('\n');
}

/**
 * 生成脚本执行代码
 */
function generateScriptExecutionCode(scripts: ExportRequest['sceneData']['scripts']): string {
  const activeScripts = scripts.filter(script => script.isActive);

  if (activeScripts.length === 0) {
    return '// 没有活跃的脚本';
  }

  return activeScripts.map(script => `
    // 执行脚本: ${script.name}
    try {
      (function() {
        // 提供脚本执行环境
        const threeScene = scene;
        const threeCamera = camera;
        const threeRenderer = renderer;
        const objects = gameObjects;

        // 执行脚本内容
        ${script.content}
      })();
    } catch (error) {
      console.error('脚本执行错误 (${script.name}):', error);
    }
  `).join('\n');
}

/**
 * 生成交互代码
 */
function generateInteractionCode(_sceneData: ExportRequest['sceneData']): string {
  return `
    // 交互系统初始化
    console.log('交互系统已初始化');

    // 键盘控制状态
    const keys = {};
    let mouseDown = false;
    let mousePos = { x: 0, y: 0 };
    let lastMousePos = { x: 0, y: 0 };

    // 键盘事件监听
    document.addEventListener('keydown', (event) => {
      keys[event.code] = true;

      // WASD控制示例
      if (gameObjects.box) {
        const speed = 0.1;
        switch(event.code) {
          case 'KeyW':
            gameObjects.box.position.z -= speed;
            break;
          case 'KeyS':
            gameObjects.box.position.z += speed;
            break;
          case 'KeyA':
            gameObjects.box.position.x -= speed;
            break;
          case 'KeyD':
            gameObjects.box.position.x += speed;
            break;
        }
      }

      event.preventDefault();
    });

    document.addEventListener('keyup', (event) => {
      keys[event.code] = false;
    });

    // 鼠标控制
    document.addEventListener('mousedown', (event) => {
      mouseDown = true;
      lastMousePos.x = event.clientX;
      lastMousePos.y = event.clientY;
    });

    document.addEventListener('mousemove', (event) => {
      mousePos.x = event.clientX;
      mousePos.y = event.clientY;

      if (mouseDown && camera) {
        const deltaX = event.clientX - lastMousePos.x;
        const deltaY = event.clientY - lastMousePos.y;

        // 简单的相机旋转控制
        camera.position.x += deltaX * 0.01;
        camera.position.y -= deltaY * 0.01;

        lastMousePos.x = event.clientX;
        lastMousePos.y = event.clientY;
      }
    });

    document.addEventListener('mouseup', () => {
      mouseDown = false;
    });

    // 触摸控制（移动设备）
    let touchStart = { x: 0, y: 0 };
    let touchDown = false;

    document.addEventListener('touchstart', (event) => {
      event.preventDefault();
      touchDown = true;
      touchStart.x = event.touches[0].clientX;
      touchStart.y = event.touches[0].clientY;
    }, { passive: false });

    document.addEventListener('touchmove', (event) => {
      event.preventDefault();

      if (touchDown && event.touches.length > 0) {
        const touchX = event.touches[0].clientX;
        const touchY = event.touches[0].clientY;

        const deltaX = touchX - touchStart.x;
        const deltaY = touchY - touchStart.y;

        // 触摸控制逻辑
        if (Math.abs(deltaX) > 30 || Math.abs(deltaY) > 30) {
          if (gameObjects.box) {
            const speed = 0.05;
            gameObjects.box.position.x += deltaX * speed * 0.01;
            gameObjects.box.position.z += deltaY * speed * 0.01;
          }

          touchStart.x = touchX;
          touchStart.y = touchY;
        }
      }
    }, { passive: false });

    document.addEventListener('touchend', (event) => {
      event.preventDefault();
      touchDown = false;
    }, { passive: false });

    // 点击/触摸CTA按钮的特殊效果
    const ctaButton = document.getElementById('cta');
    if (ctaButton) {
      ctaButton.addEventListener('click', () => {
        // 添加点击效果
        if (gameObjects.box) {
          gameObjects.box.scale.set(1.2, 1.2, 1.2);
          setTimeout(() => {
            if (gameObjects.box) {
              gameObjects.box.scale.set(1, 1, 1);
            }
          }, 200);
        }
      });
    }

    // 防止页面滚动和缩放
    document.addEventListener('touchmove', (event) => {
      if (event.target === document.body || event.target === document.documentElement) {
        event.preventDefault();
      }
    }, { passive: false });

    document.addEventListener('gesturestart', (event) => {
      event.preventDefault();
    });

    document.addEventListener('gesturechange', (event) => {
      event.preventDefault();
    });

    document.addEventListener('gestureend', (event) => {
      event.preventDefault();
    });
  `;
}

/**
 * 压缩HTML
 */
function compressHTML(html: string): string {
  return html
    .replace(/\s+/g, ' ')
    .replace(/<!--[\s\S]*?-->/g, '')
    .replace(/>\s+</g, '><')
    .trim();
}
