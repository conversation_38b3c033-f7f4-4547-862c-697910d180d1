/**
 * HTML导出API端点
 * 处理PlayableAgent场景的HTML5广告导出请求
 */

import { NextRequest, NextResponse } from 'next/server';
import { HTMLExporter, ExportOptions } from '../../../src/services/HTMLExporter';

export interface ExportRequest {
  sceneData: {
    nodes: Record<string, {
      position?: { x: number; y: number; z: number };
      rotation?: { x: number; y: number; z: number };
      scale?: { x: number; y: number; z: number };
      color?: string;
      model?: string;
      material?: string;
      animation?: string;
    }>;
    scripts: Array<{
      id: string;
      name: string;
      content: string;
      isActive: boolean;
    }>;
    camera: {
      position: [number, number, number];
      target: [number, number, number];
    };
    lights: Array<{
      type: string;
      color: number;
      intensity: number;
      position: [number, number, number];
      target?: [number, number, number];
    }>;
    background: string;
  };
  options: ExportOptions;
}

/**
 * POST /api/export-html
 * 导出HTML5广告文件
 */
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    const body: ExportRequest = await request.json();
    const { sceneData, options } = body;

    // 验证请求数据
    if (!sceneData || !sceneData.nodes) {
      return NextResponse.json(
        { error: '缺少场景数据' },
        { status: 400 }
      );
    }

    // 创建HTML导出器
    const exporter = new HTMLExporter(options);

    // 生成HTML内容
    const htmlContent = await generateStandaloneHTML(sceneData, options);

    // 检查文件大小
    const fileSizeKB = new Blob([htmlContent]).size / 1024;
    const maxSizeKB = (options.maxFileSize || 5) * 1024;

    if (fileSizeKB > maxSizeKB) {
      return NextResponse.json(
        { 
          error: `文件大小超出限制`,
          details: `当前大小: ${Math.round(fileSizeKB)}KB, 最大限制: ${Math.round(maxSizeKB)}KB`
        },
        { status: 413 }
      );
    }

    // 返回HTML内容
    return new NextResponse(htmlContent, {
      status: 200,
      headers: {
        'Content-Type': 'text/html; charset=utf-8',
        'Content-Disposition': 'attachment; filename="playable-game.html"',
        'Content-Length': htmlContent.length.toString(),
        'X-File-Size-KB': Math.round(fileSizeKB).toString()
      }
    });

  } catch (error) {
    console.error('HTML导出API错误:', error);
    return NextResponse.json(
      { 
        error: '导出失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}

/**
 * 生成独立的HTML文件
 */
async function generateStandaloneHTML(
  sceneData: ExportRequest['sceneData'],
  options: ExportOptions
): Promise<string> {
  // 获取Three.js库代码
  const threeJsCode = await getThreeJsLibrary();
  
  // 生成场景初始化代码
  const sceneInitCode = generateSceneCode(sceneData);
  
  // 生成交互代码
  const interactionCode = generateInteractionCode(sceneData);

  // 生成完整HTML
  const html = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>PlayableAgent Game</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            overflow: hidden;
            background: #000;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            touch-action: manipulation;
        }
        
        #gameContainer {
            width: 100vw;
            height: 100vh;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        #gameCanvas {
            display: block;
            max-width: 100%;
            max-height: 100%;
            background: ${sceneData.background || '#000000'};
        }
        
        #ui {
            position: absolute;
            top: 20px;
            left: 20px;
            color: white;
            z-index: 100;
            font-size: 14px;
            text-shadow: 0 1px 2px rgba(0,0,0,0.8);
        }
        
        #cta {
            position: absolute;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            background: linear-gradient(135deg, #ff6b35, #f7931e);
            color: white;
            border: none;
            padding: 16px 32px;
            font-size: 18px;
            font-weight: bold;
            border-radius: 30px;
            cursor: pointer;
            z-index: 100;
            box-shadow: 0 4px 20px rgba(255, 107, 53, 0.4);
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
            min-width: 200px;
        }
        
        #cta:hover {
            background: linear-gradient(135deg, #e55a2b, #e8851d);
            transform: translateX(-50%) translateY(-3px);
            box-shadow: 0 6px 25px rgba(255, 107, 53, 0.6);
        }
        
        #cta:active {
            transform: translateX(-50%) translateY(-1px);
            box-shadow: 0 2px 10px rgba(255, 107, 53, 0.8);
        }
        
        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 18px;
            z-index: 50;
        }
        
        .loading::after {
            content: '';
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #ffffff40;
            border-top: 2px solid #ffffff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-left: 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        @media (max-width: 768px) {
            #ui {
                font-size: 12px;
                top: 10px;
                left: 10px;
            }
            
            #cta {
                bottom: 20px;
                padding: 14px 28px;
                font-size: 16px;
                min-width: 180px;
            }
        }
    </style>
</head>
<body>
    <div id="gameContainer">
        <div class="loading">加载中...</div>
        <canvas id="gameCanvas"></canvas>
        <div id="ui" style="display: none;">
            <div>🎮 点击拖拽控制视角</div>
            <div>⌨️ WASD键移动角色</div>
        </div>
        <button id="cta" onclick="handleCTA()" style="display: none;">
            🎯 立即下载游戏
        </button>
    </div>

    <script>
        // 场景数据
        const SCENE_DATA = ${JSON.stringify(sceneData, null, 2)};
        
        // Three.js库
        ${threeJsCode}
        
        // 场景初始化
        ${sceneInitCode}
        
        // 交互逻辑
        ${interactionCode}
        
        // CTA处理函数
        function handleCTA() {
            // 广告平台集成
            try {
                // MRAID 2.0 支持
                if (typeof mraid !== 'undefined' && mraid.open) {
                    mraid.open('https://play.google.com/store/apps/details?id=your.app.id');
                    return;
                }
                
                // Google Ads ExitAPI
                if (typeof ExitApi !== 'undefined' && ExitApi.exit) {
                    ExitApi.exit();
                    return;
                }
                
                // Facebook Playable Ads
                if (typeof FbPlayableAd !== 'undefined' && FbPlayableAd.onCTAClick) {
                    FbPlayableAd.onCTAClick();
                    return;
                }
                
                // Unity Ads
                if (typeof gameReady !== 'undefined') {
                    gameReady();
                    return;
                }
                
                // 备用方案
                window.open('https://play.google.com/store/apps/details?id=your.app.id', '_blank');
            } catch (error) {
                console.error('CTA处理错误:', error);
                // 最终备用方案
                window.open('https://play.google.com/store/apps/details?id=your.app.id', '_blank');
            }
        }
        
        // 游戏初始化
        function initializeGame() {
            try {
                // 隐藏加载提示
                const loading = document.querySelector('.loading');
                if (loading) loading.style.display = 'none';
                
                // 显示UI和CTA
                const ui = document.getElementById('ui');
                const cta = document.getElementById('cta');
                if (ui) ui.style.display = 'block';
                if (cta) cta.style.display = 'block';
                
                // 启动游戏
                if (typeof startGame === 'function') {
                    startGame();
                }
                
                // 通知广告平台游戏已准备就绪
                if (typeof mraid !== 'undefined' && mraid.isViewable) {
                    console.log('MRAID: 游戏已准备就绪');
                }
                
            } catch (error) {
                console.error('游戏初始化错误:', error);
            }
        }
        
        // 页面加载完成后初始化
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initializeGame);
        } else {
            initializeGame();
        }
        
        // 错误处理
        window.addEventListener('error', function(event) {
            console.error('全局错误:', event.error);
        });
        
        // 防止页面滚动（移动设备）
        document.addEventListener('touchmove', function(event) {
            event.preventDefault();
        }, { passive: false });
        
    </script>
</body>
</html>`;

  return options.compressOutput ? compressHTML(html) : html;
}

/**
 * 获取Three.js库代码
 */
async function getThreeJsLibrary(): Promise<string> {
  // 这里应该返回压缩的Three.js代码
  // 实际部署时需要包含完整的Three.js库
  return `
    // Three.js 简化版本（演示用）
    console.log('Three.js 库已加载');
    
    // 基础Three.js对象结构
    window.THREE = window.THREE || {
      Scene: function() { /* 简化实现 */ },
      PerspectiveCamera: function() { /* 简化实现 */ },
      WebGLRenderer: function() { /* 简化实现 */ },
      // ... 其他Three.js对象
    };
  `;
}

/**
 * 生成场景代码
 */
function generateSceneCode(sceneData: ExportRequest['sceneData']): string {
  return `
    // 场景初始化代码
    function startGame() {
      console.log('开始初始化游戏场景');
      console.log('场景数据:', SCENE_DATA);
      
      // 这里会包含完整的Three.js场景创建逻辑
      // 基于传入的sceneData创建3D场景
    }
  `;
}

/**
 * 生成交互代码
 */
function generateInteractionCode(sceneData: ExportRequest['sceneData']): string {
  return `
    // 交互逻辑代码
    console.log('交互系统已初始化');
    
    // 键盘控制
    const keys = {};
    document.addEventListener('keydown', (e) => keys[e.code] = true);
    document.addEventListener('keyup', (e) => keys[e.code] = false);
    
    // 触摸控制
    let touchStart = { x: 0, y: 0 };
    document.addEventListener('touchstart', (e) => {
      touchStart.x = e.touches[0].clientX;
      touchStart.y = e.touches[0].clientY;
    });
  `;
}

/**
 * 压缩HTML
 */
function compressHTML(html: string): string {
  return html
    .replace(/\s+/g, ' ')
    .replace(/<!--[\s\S]*?-->/g, '')
    .replace(/>\s+</g, '><')
    .trim();
}
