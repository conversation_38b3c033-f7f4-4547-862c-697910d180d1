import { NextRequest, NextResponse } from 'next/server'
import fs from 'fs/promises'
import path from 'path'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const gameId = searchParams.get('gameId')
    
    if (!gameId) {
      return NextResponse.json({ 
        success: false, 
        error: 'Missing gameId parameter' 
      }, { status: 400 })
    }
    
    // 检查components/generated目录中的组件文件
    const componentPath = path.join(process.cwd(), 'components', 'generated', `${gameId}.tsx`)
    
    try {
      const componentCode = await fs.readFile(componentPath, 'utf-8')
      console.log(`[API] 成功读取组件文件: ${componentPath} (${componentCode.length} 字符)`)
      
      return NextResponse.json({ 
        success: true, 
        componentCode,
        componentPath,
        gameId 
      })
    } catch (error) {
      console.log(`[API] 组件文件不存在: ${componentPath}`)
      
      return NextResponse.json({ 
        success: false, 
        error: 'Component file not found',
        componentPath,
        gameId
      }, { status: 404 })
    }
    
  } catch (error) {
    console.error('[API] 获取组件文件时出错:', error)
    
    return NextResponse.json({ 
      success: false, 
      error: 'Internal server error' 
    }, { status: 500 })
  }
}
