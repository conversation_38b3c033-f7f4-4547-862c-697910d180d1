import { NextRequest, NextResponse } from 'next/server';
import { writeFile, mkdir } from 'fs/promises';
import { join } from 'path';
import { existsSync } from 'fs';

export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    console.log('[模型上传API] 开始处理上传请求');
    
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const nodeId = formData.get('nodeId') as string;
    const type = formData.get('type') as string || 'model'; // 默认为模型类型

    if (!file) {
      return NextResponse.json({
        success: false,
        error: '未找到文件'
      }, { status: 400 });
    }

    if (!nodeId) {
      return NextResponse.json({
        success: false,
        error: '未提供节点ID'
      }, { status: 400 });
    }

    // 根据类型验证文件格式
    const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
    let validExtensions: string[] = [];
    let errorMessage = '';

    switch (type) {
      case 'model':
        validExtensions = ['.glb', '.gltf', '.fbx'];
        errorMessage = '模型文件支持格式: .glb, .gltf, .fbx';
        break;
      case 'animation':
        validExtensions = ['.fbx'];
        errorMessage = '动画文件支持格式: .fbx';
        break;
      case 'material':
        validExtensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tga', '.json'];
        errorMessage = '材质文件支持格式: .jpg, .jpeg, .png, .bmp, .tga, .json';
        break;
      default:
        validExtensions = ['.glb', '.gltf', '.fbx'];
        errorMessage = '不支持的文件类型';
    }

    if (!validExtensions.includes(fileExtension)) {
      return NextResponse.json({
        success: false,
        error: errorMessage
      }, { status: 400 });
    }
    
    // 验证文件大小
    const maxSize = type === 'material' ? 10 * 1024 * 1024 : 100 * 1024 * 1024; // 材质文件10MB，其他100MB
    if (file.size > maxSize) {
      const maxSizeText = type === 'material' ? '10MB' : '100MB';
      return NextResponse.json({
        success: false,
        error: `文件过大，请选择小于${maxSizeText}的文件`
      }, { status: 400 });
    }

    // 根据类型创建不同的上传目录
    let subDir = 'models';
    switch (type) {
      case 'animation':
        subDir = 'animations';
        break;
      case 'material':
        subDir = 'materials';
        break;
      default:
        subDir = 'models';
    }

    const uploadDir = join(process.cwd(), 'public', 'uploads', subDir);
    if (!existsSync(uploadDir)) {
      await mkdir(uploadDir, { recursive: true });
    }
    
    // 生成唯一文件名
    const timestamp = Date.now();
    const sanitizedFileName = file.name.replace(/[^a-zA-Z0-9.-]/g, '_');
    const fileName = `${nodeId}_${timestamp}_${sanitizedFileName}`;
    const filePath = join(uploadDir, fileName);
    
    // 保存文件
    const bytes = await file.arrayBuffer();
    await writeFile(filePath, new Uint8Array(bytes));
    
    // 生成相对路径用于Web访问
    const webPath = `/uploads/${subDir}/${fileName}`;

    console.log(`[${type}上传API] 文件上传成功: ${fileName}, 大小: ${file.size} bytes, 类型: ${type}`);
    
    return NextResponse.json({
      success: true,
      data: {
        fileName,
        originalName: file.name,
        filePath: webPath,
        fileSize: file.size,
        fileType: type,
        nodeId,
        uploadedAt: new Date().toISOString()
      }
    });
    
  } catch (error) {
    console.error('[模型上传API] 上传失败:', error);
    return NextResponse.json({ 
      success: false, 
      error: error instanceof Error ? error.message : '上传失败' 
    }, { status: 500 });
  }
}

// GET - 获取上传的模型列表
export async function GET(): Promise<NextResponse> {
  try {
    console.log('[API] 获取上传的模型列表');
    
    // This function is not implemented in the new_code, so it will return an error.
    // If the user wants to implement it, they should provide the new_code.
    return NextResponse.json({
      success: false,
      error: '模型列表获取功能待实现'
    }, { status: 501 }); // 501 Not Implemented

  } catch (error) {
    console.error('[API] 获取模型列表失败:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '未知错误'
    }, { status: 500 });
  }
}

// DELETE - 删除上传的模型文件
export async function DELETE(request: NextRequest): Promise<NextResponse> {
  try {
    const { searchParams } = new URL(request.url);
    const fileName = searchParams.get('fileName');

    if (!fileName) {
      return NextResponse.json({
        success: false,
        error: '缺少fileName参数'
      }, { status: 400 });
    }

    // This function is not implemented in the new_code, so it will return an error.
    // If the user wants to implement it, they should provide the new_code.
    return NextResponse.json({
      success: false,
      error: '模型文件删除功能待实现'
    }, { status: 501 }); // 501 Not Implemented

  } catch (error) {
    console.error('[API] 删除模型文件失败:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '未知错误'
    }, { status: 500 });
  }
} 