import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs/promises';
import path from 'path';

interface ChatMessage {
  id: string;
  type: 'user' | 'agent';
  content: string;
  timestamp: string;
  metadata?: {
    scriptGenerated?: boolean;
    scriptId?: string;
    featureType?: string;
  };
}

interface ChatHistory {
  sessionId: string;
  projectName: string;
  messages: ChatMessage[];
  lastUpdated: string;
  totalMessages: number;
}

const CHAT_HISTORY_FILE = path.join(process.cwd(), 'data', 'chat-history.json');
const MAX_MESSAGES_PER_SESSION = 50; // 保留最近50条消息

// 确保数据目录存在
async function ensureDataDir(): Promise<void> {
  const dataDir = path.dirname(CHAT_HISTORY_FILE);
  try {
    await fs.access(dataDir);
  } catch {
    await fs.mkdir(dataDir, { recursive: true });
  }
}

// 读取对话历史
async function readChatHistory(): Promise<ChatHistory[]> {
  try {
    await ensureDataDir();
    const data = await fs.readFile(CHAT_HISTORY_FILE, 'utf-8');
    return JSON.parse(data);
  } catch {
    return [];
  }
}

// 写入对话历史
async function writeChatHistory(histories: ChatHistory[]): Promise<void> {
  await ensureDataDir();
  await fs.writeFile(CHAT_HISTORY_FILE, JSON.stringify(histories, null, 2), 'utf-8');
}

// 获取当前会话ID（简化版，基于日期）
function getCurrentSessionId(): string {
  const today = new Date().toISOString().split('T')[0];
  return `session_${today}`;
}

// GET - 获取对话历史
export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    const { searchParams } = new URL(request.url);
    const sessionId = searchParams.get('sessionId') || getCurrentSessionId();
    
    console.log(`[API] 获取对话历史: ${sessionId}`);
    
    const histories = await readChatHistory();
    const sessionHistory = histories.find(h => h.sessionId === sessionId);
    
    if (!sessionHistory) {
      return NextResponse.json({
        success: true,
        data: {
          sessionId,
          projectName: 'PlayableAgent游戏项目',
          messages: [],
          totalMessages: 0
        }
      });
    }
    
    return NextResponse.json({
      success: true,
      data: {
        sessionId: sessionHistory.sessionId,
        projectName: sessionHistory.projectName,
        messages: sessionHistory.messages,
        totalMessages: sessionHistory.totalMessages,
        lastUpdated: sessionHistory.lastUpdated
      }
    });

  } catch (error) {
    console.error('[API] 获取对话历史失败:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '未知错误'
    }, { status: 500 });
  }
}

// POST - 保存消息到对话历史
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    const { sessionId, messages, projectName } = await request.json();
    
    if (!messages || !Array.isArray(messages)) {
      return NextResponse.json({
        success: false,
        error: '缺少必要参数：messages'
      }, { status: 400 });
    }

    const currentSessionId = sessionId || getCurrentSessionId();
    console.log(`[API] 保存对话历史: ${currentSessionId}, 消息数: ${messages.length}`);

    const histories = await readChatHistory();
    const existingIndex = histories.findIndex(h => h.sessionId === currentSessionId);
    
    // 确保消息格式正确，转换Date对象为字符串
    const processedMessages = messages.map((msg: Record<string, unknown>) => {
      const chatMsg = msg as unknown as ChatMessage;
      return {
        ...chatMsg,
        timestamp: msg.timestamp instanceof Date ? msg.timestamp.toISOString() : String(msg.timestamp || new Date().toISOString())
      };
    });

    // 限制消息数量，保留最新的消息
    const limitedMessages = processedMessages.slice(-MAX_MESSAGES_PER_SESSION);
    
    const historyEntry: ChatHistory = {
      sessionId: currentSessionId,
      projectName: projectName || 'PlayableAgent游戏项目',
      messages: limitedMessages,
      lastUpdated: new Date().toISOString(),
      totalMessages: limitedMessages.length
    };

    if (existingIndex >= 0) {
      // 更新现有会话
      histories[existingIndex] = historyEntry;
      console.log(`[API] 更新会话历史: ${currentSessionId}`);
    } else {
      // 添加新会话
      histories.push(historyEntry);
      console.log(`[API] 创建新会话历史: ${currentSessionId}`);
    }

    // 限制总会话数量，保留最近的10个会话
    const limitedHistories = histories
      .sort((a, b) => new Date(b.lastUpdated).getTime() - new Date(a.lastUpdated).getTime())
      .slice(0, 10);

    await writeChatHistory(limitedHistories);

    return NextResponse.json({
      success: true,
      message: '对话历史保存成功',
      data: {
        sessionId: currentSessionId,
        totalMessages: limitedMessages.length,
        savedAt: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('[API] 保存对话历史失败:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '未知错误'
    }, { status: 500 });
  }
}

// DELETE - 清空对话历史
export async function DELETE(request: NextRequest): Promise<NextResponse> {
  try {
    const { searchParams } = new URL(request.url);
    const sessionId = searchParams.get('sessionId');

    if (!sessionId) {
      return NextResponse.json({
        success: false,
        error: '缺少sessionId参数'
      }, { status: 400 });
    }

    console.log(`[API] 删除对话历史: ${sessionId}`);

    const histories = await readChatHistory();
    const filteredHistories = histories.filter(h => h.sessionId !== sessionId);

    await writeChatHistory(filteredHistories);

    return NextResponse.json({
      success: true,
      message: '对话历史删除成功',
      data: {
        deletedSessionId: sessionId,
        remainingSessions: filteredHistories.length
      }
    });

  } catch (error) {
    console.error('[API] 删除对话历史失败:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '未知错误'
    }, { status: 500 });
  }
} 