import { NextRequest, NextResponse } from 'next/server'
import { GameProjectManager, GameProjectStatus } from '@/src/project/GameProjectManager'
import { OptimizationManager } from '@/src/agents/OptimizationManager'

// 获取项目管理器实例
const projectManager = GameProjectManager.getInstance()

// 创建优化管理器实例
const optimizationManager = new OptimizationManager({
  enableCaching: true,
  enableTemplateSelection: true,
  enableAccuracyValidation: true,
  enablePerformanceOptimization: true,
  maxIterations: 2, // 迭代时使用较少的迭代次数
  qualityThreshold: 70, // 迭代时降低质量阈值
  tokenLimit: 15000
})

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: projectId } = await params
    const body = await request.json()
    const { userMessage, iterationType = 'modify' } = body

    console.log(`开始为项目 ${projectId} 进行迭代优化...`)

    // 获取项目
    const project = projectManager.getProject(projectId)
    if (!project) {
      return NextResponse.json(
        { error: '项目不存在' },
        { status: 404 }
      )
    }

    // 检查项目是否已完成初始生成
    if (project.status !== GameProjectStatus.COMPLETED) {
      return NextResponse.json(
        { error: '项目尚未完成初始生成，无法进行迭代' },
        { status: 400 }
      )
    }

    if (!userMessage || !userMessage.trim()) {
      return NextResponse.json(
        { error: '请提供修改要求' },
        { status: 400 }
      )
    }

    // 更新项目状态为生成中
    projectManager.updateProject(projectId, {
      status: GameProjectStatus.GENERATING
    })

    const startTime = Date.now()

    try {
      // 构建迭代提示词
      const iterationPrompt = buildIterationPrompt(
        project.userInput.description,
        userMessage,
        iterationType,
        project.gameDesign
      )

      // 使用优化管理器进行迭代生成
      const optimizationResult = await optimizationManager.optimizedGeneration(iterationPrompt)

      const iterationTime = Date.now() - startTime

      if (optimizationResult.success && optimizationResult.gameDesign && optimizationResult.generatedCode) {
        // 更新项目
        const updatedProject = projectManager.updateProject(projectId, {
          status: GameProjectStatus.COMPLETED,
          gameDesign: optimizationResult.gameDesign,
          generatedCode: optimizationResult.generatedCode,
          metadata: {
            totalIterations: project.metadata.totalIterations + optimizationResult.performance.iterations,
            generationTime: project.metadata.generationTime + iterationTime,
            tokensUsed: project.metadata.tokensUsed + optimizationResult.performance.tokensUsed,
            qualityScore: optimizationResult.quality.overallScore,
            templateUsed: (optimizationResult.gameDesign as any).templateInfo?.selectedTemplate || project.metadata.templateUsed
          }
        })

        console.log(`项目 ${projectId} 迭代成功`, {
          quality: optimizationResult.quality,
          performance: optimizationResult.performance
        })

        return NextResponse.json({
          success: true,
          project: updatedProject,
          iteration: {
            userMessage,
            iterationType,
            quality: optimizationResult.quality,
            performance: optimizationResult.performance,
            suggestions: optimizationResult.suggestions
          },
          agentResponse: generateAgentResponse(optimizationResult, userMessage)
        })

      } else {
        // 迭代失败，恢复原状态
        projectManager.updateProject(projectId, {
          status: GameProjectStatus.COMPLETED,
          metadata: {
            ...project.metadata,
            totalIterations: project.metadata.totalIterations + optimizationResult.performance.iterations,
            tokensUsed: project.metadata.tokensUsed + optimizationResult.performance.tokensUsed
          }
        })

        return NextResponse.json({
          success: false,
          error: '迭代优化失败',
          details: optimizationResult.errors,
          agentResponse: `抱歉，我无法完成您的修改要求："${userMessage}"。请尝试更具体的描述或简化修改内容。`
        }, { status: 500 })
      }

    } catch (iterationError) {
      console.error(`项目 ${projectId} 迭代过程中出错:`, iterationError)
      
      // 恢复项目状态
      projectManager.updateProject(projectId, {
        status: GameProjectStatus.COMPLETED
      })

      return NextResponse.json({
        success: false,
        error: '迭代过程中出错',
        details: iterationError instanceof Error ? iterationError.message : '未知错误',
        agentResponse: `处理您的请求时遇到了技术问题，请稍后重试。`
      }, { status: 500 })
    }

  } catch (error) {
    console.error('迭代优化API错误:', error)
    
    return NextResponse.json(
      { 
        error: '迭代优化失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    )
  }
}

/**
 * 构建迭代提示词
 */
function buildIterationPrompt(
  originalDescription: string,
  userMessage: string,
  iterationType: string,
  currentDesign?: any
): string {
  const basePrompt = `基于以下现有游戏进行${iterationType === 'modify' ? '修改' : '优化'}：

原始游戏描述：
${originalDescription}

${currentDesign ? `
当前游戏设计：
${JSON.stringify(currentDesign, null, 2)}
` : ''}

用户修改要求：
${userMessage}

请根据用户的要求对游戏进行相应的调整和优化，保持游戏的核心特色，同时满足新的需求。`

  return basePrompt
}

/**
 * 生成Agent响应消息
 */
function generateAgentResponse(optimizationResult: any, userMessage: string): string {
  const quality = optimizationResult.quality.overallScore
  const templateUsed = optimizationResult.gameDesign?.templateInfo?.selectedTemplate

  let response = `✅ 我已经根据您的要求"${userMessage}"对游戏进行了优化！\n\n`
  
  response += `🎯 **优化结果**：\n`
  response += `- 质量分数：${quality}分\n`
  response += `- 使用模板：${templateUsed || '自定义'}\n`
  response += `- 迭代次数：${optimizationResult.performance.iterations}次\n\n`

  if (optimizationResult.suggestions && optimizationResult.suggestions.length > 0) {
    response += `💡 **进一步优化建议**：\n`
    optimizationResult.suggestions.slice(0, 2).forEach((suggestion: any, index: number) => {
      response += `${index + 1}. ${suggestion.description}\n`
    })
  }

  response += `\n您可以继续提出修改要求，我会帮您进一步完善游戏！`

  return response
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: projectId } = await params

    // 获取项目迭代历史
    const project = projectManager.getProject(projectId)
    if (!project) {
      return NextResponse.json(
        { error: '项目不存在' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      projectId,
      designIterations: project.designIterations.length,
      codeIterations: project.codeIterations.length,
      totalIterations: project.metadata.totalIterations,
      canIterate: project.status === GameProjectStatus.COMPLETED
    })

  } catch (error) {
    console.error('获取迭代历史API错误:', error)
    
    return NextResponse.json(
      { 
        error: '获取迭代历史失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    )
  }
}
