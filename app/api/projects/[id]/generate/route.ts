import { NextRequest, NextResponse } from 'next/server'
import { GameProjectManager, GameProjectStatus } from '@/src/project/GameProjectManager'
import { OptimizationManager } from '@/src/agents/OptimizationManager'
import { GeneratedCode } from '@/src/agents/CodeGenerationAgent'
import { IdConsistencyGuard } from '@/src/utils/IdConsistencyGuard'
import fs from 'fs/promises'
import path from 'path'

// 获取项目管理器实例
const projectManager = GameProjectManager.getInstance()

// 创建优化管理器实例
const optimizationManager = new OptimizationManager({
  enableCaching: true,
  enableTemplateSelection: true,
  enableAccuracyValidation: true,
  enablePerformanceOptimization: true,
  maxIterations: 3,
  qualityThreshold: 75,
  tokenLimit: 20000
})

// 保存生成的文件
async function saveGeneratedFiles(gameId: string, code: GeneratedCode): Promise<void> {
  try {
    // 保存到项目的components/generated目录，便于Canvas页面动态导入
    const projectComponentsDir = path.join(process.cwd(), 'components', 'generated');
    await fs.mkdir(projectComponentsDir, { recursive: true });

    if (code.component) {
      const projectTsxPath = path.join(projectComponentsDir, `${gameId}.tsx`);
      await fs.writeFile(projectTsxPath, code.component, 'utf-8');
      console.log(`[ProjectGenerate] 保存项目组件: ${projectTsxPath} (${code.component.length} 字符)`);
    }

    // 同时保存到public/games目录用于下载
    const publicGamesDir = path.join(process.cwd(), 'public', 'games', gameId);
    await fs.mkdir(publicGamesDir, { recursive: true });

    if (code.component) {
      const publicTsxPath = path.join(publicGamesDir, code.fileName || 'GameComponent.tsx');
      await fs.writeFile(publicTsxPath, code.component, 'utf-8');
    }

    // 保存依赖项信息
    if (code.dependencies.length > 0) {
      const depsPath = path.join(publicGamesDir, 'dependencies.json');
      await fs.writeFile(depsPath, JSON.stringify(code.dependencies, null, 2), 'utf-8');
    }

    // 保存组件元数据
    if (code.metadata) {
      const metadataPath = path.join(publicGamesDir, 'metadata.json');
      await fs.writeFile(metadataPath, JSON.stringify(code.metadata, null, 2), 'utf-8');
    }

  } catch (error) {
    console.error('[ProjectGenerate] 保存文件失败:', error);
    throw error;
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: projectId } = await params
    console.log(`开始为项目 ${projectId} 生成游戏...`)

    // 注册项目ID到一致性保护机制
    IdConsistencyGuard.registerProjectId(projectId, 'ProjectGenerate API');

    // 获取项目
    const project = projectManager.getProject(projectId)
    if (!project) {
      IdConsistencyGuard.unregisterProjectId(projectId, 'ProjectGenerate API - Project Not Found');
      return NextResponse.json(
        { error: '项目不存在' },
        { status: 404 }
      )
    }

    // 更新项目状态为设计中
    projectManager.updateProject(projectId, {
      status: GameProjectStatus.DESIGNING
    })

    const startTime = Date.now()

    try {
      // 使用优化管理器生成游戏（传递项目ID确保一致性）
      const optimizationResult = await optimizationManager.optimizedGeneration(
        project.userInput.description,
        projectId  // 传递项目ID
      )

      const generationTime = Date.now() - startTime

      if (optimizationResult.success && optimizationResult.gameDesign && optimizationResult.generatedCode) {
        // 保存生成的文件（使用项目ID作为gameId，确保ID一致性）
        console.log(`[ProjectGenerate] 使用项目ID保存文件: ${projectId}`)

        // 验证ID一致性
        IdConsistencyGuard.validateProjectId(projectId, 'File Saving');

        await saveGeneratedFiles(projectId, optimizationResult.generatedCode)

        // 更新项目为完成状态
        const updatedProject = projectManager.updateProject(projectId, {
          status: GameProjectStatus.COMPLETED,
          gameDesign: optimizationResult.gameDesign,
          generatedCode: optimizationResult.generatedCode,
          metadata: {
            totalIterations: optimizationResult.performance.iterations,
            generationTime,
            tokensUsed: optimizationResult.performance.tokensUsed,
            qualityScore: optimizationResult.quality.overallScore,
            templateUsed: (optimizationResult.gameDesign as any).templateInfo?.selectedTemplate
          }
        })

        console.log(`项目 ${projectId} 生成成功`, {
          quality: optimizationResult.quality,
          performance: optimizationResult.performance
        })

        return NextResponse.json({
          success: true,
          project: updatedProject,
          optimizationResult: {
            quality: optimizationResult.quality,
            performance: optimizationResult.performance,
            suggestions: optimizationResult.suggestions
          }
        })

      } else {
        // 生成失败，更新项目状态
        projectManager.updateProject(projectId, {
          status: GameProjectStatus.ERROR,
          metadata: {
            totalIterations: optimizationResult.performance.iterations,
            generationTime,
            tokensUsed: optimizationResult.performance.tokensUsed,
            qualityScore: 0
          }
        })

        return NextResponse.json({
          success: false,
          error: '游戏生成失败',
          details: optimizationResult.errors,
          optimizationResult: {
            quality: optimizationResult.quality,
            performance: optimizationResult.performance,
            suggestions: optimizationResult.suggestions
          }
        }, { status: 500 })
      }

    } catch (generationError) {
      console.error(`项目 ${projectId} 生成过程中出错:`, generationError)
      
      // 更新项目状态为错误
      projectManager.updateProject(projectId, {
        status: GameProjectStatus.ERROR,
        metadata: {
          generationTime: Date.now() - startTime,
          qualityScore: 0
        }
      })

      return NextResponse.json({
        success: false,
        error: '游戏生成过程中出错',
        details: generationError instanceof Error ? generationError.message : '未知错误'
      }, { status: 500 })
    }

  } catch (error) {
    console.error('游戏生成API错误:', error)
    
    return NextResponse.json(
      { 
        error: '游戏生成失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    )
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: projectId } = await params

    // 获取项目生成状态
    const project = projectManager.getProject(projectId)
    if (!project) {
      return NextResponse.json(
        { error: '项目不存在' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      projectId,
      status: project.status,
      progress: project.status === GameProjectStatus.COMPLETED ? 100 : 
                project.status === GameProjectStatus.ERROR ? 0 :
                project.status === GameProjectStatus.GENERATING ? 50 :
                project.status === GameProjectStatus.DESIGNING ? 25 : 0,
      metadata: project.metadata,
      hasDesign: !!project.gameDesign,
      hasCode: !!project.generatedCode
    })

  } catch (error) {
    console.error('获取生成状态API错误:', error)
    
    return NextResponse.json(
      { 
        error: '获取生成状态失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    )
  }
}
