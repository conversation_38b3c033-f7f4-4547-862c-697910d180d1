import { NextRequest } from 'next/server'
import { GameProjectManager, GameProjectStatus } from '@/src/project/GameProjectManager'
import { PlayableGenAPI, GameGenerationRequest } from '@/src/agents/PlayableGenAPI'
import { StreamCallback } from '@/src/agents/CustomClaude4Client'
import { IdConsistencyGuard } from '@/src/utils/IdConsistencyGuard'
import { existsSync } from 'fs'
import { join } from 'path'


// 获取项目管理器实例
const projectManager = GameProjectManager.getInstance()

// 创建PlayableGenAPI实例
const playableGenAPI = new PlayableGenAPI({
  outputDir: './generated-games',
  enableLogging: true,
  codeGeneration: {
    framework: "babylon.js",
    outputFormat: "tsx",
    includeAssets: true,
    minify: false
  }
})

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: projectId } = await params
    const body = await request.json()
    const { description, preferences } = body

    if (!description) {
      return new Response('游戏描述不能为空', { status: 400 })
    }

    // 验证项目是否存在
    const project = projectManager.getProject(projectId)
    if (!project) {
      return new Response('项目不存在', { status: 404 })
    }

    // 注册项目ID到一致性保护机制
    IdConsistencyGuard.registerProjectId(projectId, 'Project Stream Generate API')

    console.log(`开始为项目 ${projectId} 进行流式生成:`, { description, preferences })

    // 更新项目状态
    projectManager.updateProject(projectId, {
      status: GameProjectStatus.GENERATING
    })

    // 构建游戏生成请求
    const gameRequest: GameGenerationRequest = {
      description,
      preferences: preferences || {
        gameType: "action",
        difficulty: "medium",
        theme: "现代风格",
        controls: ["触屏", "键盘"]
      },
      outputOptions: {
        format: "html",
        includeAssets: true,
        minify: false
      }
    }

    // 创建SSE响应
    const encoder = new TextEncoder()
    const stream = new ReadableStream({
      start(controller) {
        // 流式回调函数
        const streamCallback: StreamCallback = {
          onToken: (token: string) => {
            const data = JSON.stringify({
              type: 'token',
              data: token
            })
            controller.enqueue(encoder.encode(`data: ${data}\n\n`))
          },
          onStatus: (status: string) => {
            const data = JSON.stringify({
              type: 'status',
              data: status
            })
            controller.enqueue(encoder.encode(`data: ${data}\n\n`))
          },
          onProgress: (progress) => {
            const data = JSON.stringify({
              type: 'progress',
              data: progress
            })
            controller.enqueue(encoder.encode(`data: ${data}\n\n`))
          }
        }

        // 自定义的流式生成逻辑，使用项目ID
        generateGameStreamWithProjectId(projectId, gameRequest, streamCallback)
          .then(result => {
            // 更新项目状态
            if (result.success) {
              projectManager.updateProject(projectId, {
                status: GameProjectStatus.COMPLETED,
                generatedCode: result.files
              })
            } else {
              projectManager.updateProject(projectId, {
                status: GameProjectStatus.ERROR
              })
            }

            // 发送最终结果，确保gameId是项目ID
            const finalResult = {
              ...result,
              gameId: projectId // 确保返回的gameId是项目ID
            }
            
            const data = JSON.stringify({
              type: 'complete',
              data: finalResult
            })
            controller.enqueue(encoder.encode(`data: ${data}\n\n`))
            controller.close()
          })
          .catch(error => {
            // 更新项目状态为错误
            projectManager.updateProject(projectId, {
              status: GameProjectStatus.ERROR
            })

            // 发送错误信息
            const data = JSON.stringify({
              type: 'error',
              data: {
                message: error instanceof Error ? error.message : '未知错误'
              }
            })
            controller.enqueue(encoder.encode(`data: ${data}\n\n`))
            controller.close()
          })
      }
    })

    return new Response(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST',
        'Access-Control-Allow-Headers': 'Content-Type',
      },
    })

  } catch (error) {
    console.error('项目流式生成失败:', error)
    return new Response(
      JSON.stringify({ error: '服务器内部错误' }),
      { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    )
  }
}

// 自定义的流式生成函数，使用项目ID作为游戏ID
async function generateGameStreamWithProjectId(
  projectId: string,
  request: GameGenerationRequest,
  streamCallback: StreamCallback
): Promise<{ success: boolean; gameId: string; files?: Record<string, string>; errors?: string[] }> {
  try {
    streamCallback.onStatus?.('开始游戏生成流程...')

    // 使用PlayableGenAPI的流式生成方法，支持实时输出，传递项目ID
    const result = await playableGenAPI.generateGameStream(request, streamCallback, projectId)
    
    // 验证ID一致性
    IdConsistencyGuard.validateProjectId(projectId, 'Stream Generation Complete')

    // 如果生成成功，文件已经被PlayableGenAPI保存到正确位置，无需重新保存
    if (result.success) {
      streamCallback.onStatus?.('游戏生成完成！')
      return {
        success: true,
        gameId: projectId, // 返回项目ID作为游戏ID
        files: { [`${projectId}.tsx`]: 'Component saved successfully' }, // 标记文件已保存
        errors: result.errors
      }
    }

    return {
      success: false,
      gameId: projectId,
      errors: result.errors || ['游戏生成失败']
    }

  } catch (error) {
    console.error(`[ProjectStreamGenerate] 生成失败 ${projectId}:`, error)
    
    // 即使出现错误，也要检查文件是否已经生成
    // 如果文件已经存在，说明生成实际上是成功的
    try {
      const componentPath = join(process.cwd(), 'components', 'generated', `${projectId}.tsx`)
      if (existsSync(componentPath)) {
        console.log(`[ProjectStreamGenerate] 文件已存在，认为生成成功: ${componentPath}`)
        return {
          success: true,
          gameId: projectId,
          files: { [`${projectId}.tsx`]: 'Component saved successfully' },
          errors: []
        }
      }
    } catch (checkError) {
      console.error(`[ProjectStreamGenerate] 文件检查失败:`, checkError)
    }
    
    return {
      success: false,
      gameId: projectId,
      errors: [error instanceof Error ? error.message : '生成失败']
    }
  }
}

 