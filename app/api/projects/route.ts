import { NextRequest, NextResponse } from 'next/server'
import { GameProjectManager, CreateProjectRequest, GameProjectStatus } from '@/src/project/GameProjectManager'

// 获取项目管理器实例
const projectManager = GameProjectManager.getInstance()

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const query = searchParams.get('q')
    const projectId = searchParams.get('id')

    // 获取单个项目
    if (projectId) {
      const project = projectManager.getProject(projectId)
      if (!project) {
        return NextResponse.json(
          { error: '项目不存在' },
          { status: 404 }
        )
      }
      return NextResponse.json(project)
    }

    // 搜索项目
    if (query) {
      const projects = projectManager.searchProjects(query)
      return NextResponse.json({ projects })
    }

    // 获取所有项目
    const projects = projectManager.getAllProjects()
    const stats = projectManager.getProjectStats()

    return NextResponse.json({
      projects,
      stats
    })

  } catch (error) {
    console.error('获取项目API错误:', error)
    
    return NextResponse.json(
      { 
        error: '获取项目失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const createRequest: CreateProjectRequest = body

    // 验证必需字段
    if (!createRequest.name || !createRequest.description) {
      return NextResponse.json(
        { error: '项目名称和描述是必需的' },
        { status: 400 }
      )
    }

    // 创建项目
    const project = projectManager.createProject(createRequest)

    console.log('创建项目成功:', project.id)

    return NextResponse.json(project, { status: 201 })

  } catch (error) {
    console.error('创建项目API错误:', error)
    
    return NextResponse.json(
      { 
        error: '创建项目失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { projectId, updates } = body

    if (!projectId) {
      return NextResponse.json(
        { error: '项目ID是必需的' },
        { status: 400 }
      )
    }

    // 更新项目
    const updatedProject = projectManager.updateProject(projectId, updates)
    
    if (!updatedProject) {
      return NextResponse.json(
        { error: '项目不存在' },
        { status: 404 }
      )
    }

    console.log('更新项目成功:', projectId)

    return NextResponse.json(updatedProject)

  } catch (error) {
    console.error('更新项目API错误:', error)
    
    return NextResponse.json(
      { 
        error: '更新项目失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const projectId = searchParams.get('id')

    if (!projectId) {
      return NextResponse.json(
        { error: '项目ID是必需的' },
        { status: 400 }
      )
    }

    // 删除项目
    const success = projectManager.deleteProject(projectId)
    
    if (!success) {
      return NextResponse.json(
        { error: '项目不存在' },
        { status: 404 }
      )
    }

    console.log('删除项目成功:', projectId)

    return NextResponse.json({ success: true })

  } catch (error) {
    console.error('删除项目API错误:', error)
    
    return NextResponse.json(
      { 
        error: '删除项目失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    )
  }
}
