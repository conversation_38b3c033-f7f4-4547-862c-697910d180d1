import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs/promises';
import path from 'path';

export async function POST(request: NextRequest) {
  try {
    const { templateName, scriptContent, description, functionType, targetNodeTypes } = await request.json();

    if (!templateName || !scriptContent) {
      return NextResponse.json({
        success: false,
        error: '缺少必要参数：templateName或scriptContent'
      }, { status: 400 });
    }

    console.log(`[API] 保存脚本模板: ${templateName}`);

    // 创建模板保存目录
    const templatesDir = path.join(process.cwd(), 'script-templates');
    await fs.mkdir(templatesDir, { recursive: true });

    // 构建安全的文件名
    const safeFileName = templateName.replace(/[^a-zA-Z0-9_\-\u4e00-\u9fa5]/g, '_');
    const templateId = `template_${Date.now()}_${Math.random().toString(36).substring(2)}`;
    const fileName = `${templateId}_${safeFileName}.js`;
    const filePath = path.join(templatesDir, fileName);

    // 构建模板内容
    const templateContent = `/**
 * 脚本模板文件
 * 
 * 模板ID: ${templateId}
 * 模板名称: ${templateName}
 * 创建时间: ${new Date().toLocaleString()}
 * 功能类型: ${functionType || 'unknown'}
 * 目标节点类型: ${targetNodeTypes?.join(', ') || 'unknown'}
 * 描述: ${description || '无描述'}
 * 
 * 使用说明：
 * 1. 复制此模板代码到新脚本中
 * 2. 根据需要修改参数和逻辑
 * 3. 测试脚本功能
 */

${scriptContent}`;

    // 保存模板文件
    await fs.writeFile(filePath, templateContent, 'utf-8');

    // 保存模板元数据
    const metadataPath = path.join(templatesDir, `${templateId}_metadata.json`);
    const metadataContent = {
      templateId,
      templateName,
      fileName,
      description,
      functionType,
      targetNodeTypes,
      createdAt: new Date().toISOString(),
      filePath: filePath.replace(process.cwd(), ''),
      fileSize: templateContent.length,
      usageCount: 0
    };

    await fs.writeFile(metadataPath, JSON.stringify(metadataContent, null, 2), 'utf-8');

    console.log(`[API] 脚本模板已保存: ${filePath}`);

    return NextResponse.json({
      success: true,
      message: '脚本模板保存成功',
      data: {
        templateId,
        templateName,
        fileName,
        filePath: filePath.replace(process.cwd(), ''),
        createdAt: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('[API] 脚本模板保存失败:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '未知错误',
      details: '脚本模板保存过程中发生错误'
    }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const templateId = searchParams.get('templateId');

    if (!templateId) {
      // 返回所有模板列表
      const templatesDir = path.join(process.cwd(), 'script-templates');
      
      try {
        const files = await fs.readdir(templatesDir);
        const metadataFiles = files.filter(file => file.endsWith('_metadata.json'));
        
        const templateList = await Promise.all(
          metadataFiles.map(async (file) => {
            const metadataPath = path.join(templatesDir, file);
            const metadataContent = await fs.readFile(metadataPath, 'utf-8');
            return JSON.parse(metadataContent);
          })
        );

        // 按创建时间倒序排列
        templateList.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

        return NextResponse.json({
          success: true,
          data: {
            templates: templateList,
            totalCount: templateList.length
          }
        });

      } catch {
        // 目录不存在或为空
        return NextResponse.json({
          success: true,
          data: {
            templates: [],
            totalCount: 0
          }
        });
      }
    } else {
      // 返回特定模板的信息
      const templatesDir = path.join(process.cwd(), 'script-templates');
      const metadataPath = path.join(templatesDir, `${templateId}_metadata.json`);
      
      try {
        const metadataContent = await fs.readFile(metadataPath, 'utf-8');
        const metadata = JSON.parse(metadataContent);

        // 读取模板文件内容
        const templatePath = path.join(process.cwd(), metadata.filePath);
        const templateContent = await fs.readFile(templatePath, 'utf-8');
        
        // 提取脚本代码（去除模板注释）
        const codeMatch = templateContent.match(/\*\/\s*([\s\S]*)$/);
        const scriptCode = codeMatch ? codeMatch[1].trim() : templateContent;

        // 增加使用计数
        metadata.usageCount = (metadata.usageCount || 0) + 1;
        metadata.lastUsed = new Date().toISOString();
        await fs.writeFile(metadataPath, JSON.stringify(metadata, null, 2), 'utf-8');

        return NextResponse.json({
          success: true,
          data: {
            ...metadata,
            scriptContent: scriptCode
          }
        });

      } catch {
        return NextResponse.json({
          success: false,
          error: '模板不存在',
          details: `无法找到ID为 ${templateId} 的模板`
        }, { status: 404 });
      }
    }

  } catch (error) {
    console.error('[API] 获取脚本模板失败:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '未知错误'
    }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const templateId = searchParams.get('templateId');

    if (!templateId) {
      return NextResponse.json({
        success: false,
        error: '缺少templateId参数'
      }, { status: 400 });
    }

    console.log(`[API] 删除脚本模板: ${templateId}`);

    const templatesDir = path.join(process.cwd(), 'script-templates');
    
    // 读取元数据获取文件信息
    const metadataPath = path.join(templatesDir, `${templateId}_metadata.json`);
    const metadataContent = await fs.readFile(metadataPath, 'utf-8');
    const metadata = JSON.parse(metadataContent);

    // 删除模板文件
    const templatePath = path.join(process.cwd(), metadata.filePath);
    await fs.unlink(templatePath);

    // 删除元数据文件
    await fs.unlink(metadataPath);

    console.log(`[API] 脚本模板已删除: ${templateId}`);

    return NextResponse.json({
      success: true,
      message: '脚本模板删除成功',
      data: {
        templateId,
        deletedAt: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('[API] 脚本模板删除失败:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '未知错误',
      details: '脚本模板删除过程中发生错误'
    }, { status: 500 });
  }
} 