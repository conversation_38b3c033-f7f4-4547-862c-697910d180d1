import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs/promises';
import path from 'path';

export async function POST(request: NextRequest) {
  try {
    const { scriptId, scriptName, scriptContent, metadata } = await request.json();

    if (!scriptId || !scriptName || !scriptContent) {
      return NextResponse.json({
        success: false,
        error: '缺少必要参数：scriptId、scriptName或scriptContent'
      }, { status: 400 });
    }

    console.log(`[API] 保存脚本请求: ${scriptName}`);

    // 创建脚本保存目录
    const scriptsDir = path.join(process.cwd(), 'generated-scripts');
    await fs.mkdir(scriptsDir, { recursive: true });

    // 构建文件名，确保是安全的文件名
    const safeFileName = scriptName.replace(/[^a-zA-Z0-9_\-\u4e00-\u9fa5]/g, '_');
    const fileName = `${scriptId}_${safeFileName}.js`;  // 使用.js扩展名避免TypeScript检查
    const filePath = path.join(scriptsDir, fileName);

    // 构建完整的脚本内容，包含元数据注释
    const fullContent = `/**
 * 自动生成的脚本文件
 * 
 * 脚本ID: ${scriptId}
 * 脚本名称: ${scriptName}
 * 生成时间: ${new Date().toLocaleString()}
 * 功能类型: ${metadata?.functionType || 'unknown'}
 * 目标节点: ${metadata?.targetNodeTypes?.join(', ') || 'unknown'}
 * 依赖项: ${metadata?.dependencies?.join(', ') || 'none'}
 * 描述: ${metadata?.description || '无描述'}
 */

${scriptContent}`;

    // 保存文件
    await fs.writeFile(filePath, fullContent, 'utf-8');

    console.log(`[API] 脚本已保存: ${filePath}`);

    // 同时保存元数据文件
    const metadataPath = path.join(scriptsDir, `${scriptId}_metadata.json`);
    const metadataContent = {
      scriptId,
      scriptName,
      fileName,
      savedAt: new Date().toISOString(),
      metadata: metadata || {},
      filePath: filePath.replace(process.cwd(), ''),
      fileSize: fullContent.length
    };

    await fs.writeFile(metadataPath, JSON.stringify(metadataContent, null, 2), 'utf-8');

    return NextResponse.json({
      success: true,
      message: '脚本保存成功',
      data: {
        scriptId,
        fileName,
        filePath: filePath.replace(process.cwd(), ''),
        fileSize: fullContent.length,
        savedAt: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('[API] 脚本保存失败:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '未知错误',
      details: '脚本保存过程中发生错误'
    }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const scriptId = searchParams.get('scriptId');

    if (!scriptId) {
      // 返回所有已保存的脚本列表
      const scriptsDir = path.join(process.cwd(), 'generated-scripts');
      
      try {
        const files = await fs.readdir(scriptsDir);
        const metadataFiles = files.filter(file => file.endsWith('_metadata.json'));
        
        const scriptList = await Promise.all(
          metadataFiles.map(async (file) => {
            const metadataPath = path.join(scriptsDir, file);
            const metadataContent = await fs.readFile(metadataPath, 'utf-8');
            return JSON.parse(metadataContent);
          })
        );

        return NextResponse.json({
          success: true,
          data: {
            scripts: scriptList,
            totalCount: scriptList.length
          }
        });

      } catch {
        // 目录不存在或为空
        return NextResponse.json({
          success: true,
          data: {
            scripts: [],
            totalCount: 0
          }
        });
      }
    } else {
      // 返回特定脚本的信息
      const scriptsDir = path.join(process.cwd(), 'generated-scripts');
      const metadataPath = path.join(scriptsDir, `${scriptId}_metadata.json`);
      
      try {
        const metadataContent = await fs.readFile(metadataPath, 'utf-8');
        const metadata = JSON.parse(metadataContent);

        // 读取脚本文件内容
        const scriptPath = path.join(process.cwd(), metadata.filePath);
        const scriptContent = await fs.readFile(scriptPath, 'utf-8');

        return NextResponse.json({
          success: true,
          data: {
            ...metadata,
            content: scriptContent
          }
        });

      } catch {
        return NextResponse.json({
          success: false,
          error: '脚本不存在',
          details: `无法找到ID为 ${scriptId} 的脚本`
        }, { status: 404 });
      }
    }

  } catch (error) {
    console.error('[API] 获取脚本信息失败:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '未知错误'
    }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    const { scriptId, newScriptName } = await request.json();

    if (!scriptId || !newScriptName) {
      return NextResponse.json({
        success: false,
        error: '缺少必要参数：scriptId或newScriptName'
      }, { status: 400 });
    }

    console.log(`[API] 重命名脚本请求: ${scriptId} -> ${newScriptName}`);

    const scriptsDir = path.join(process.cwd(), 'generated-scripts');
    
    // 读取现有元数据
    const metadataPath = path.join(scriptsDir, `${scriptId}_metadata.json`);
    const metadataContent = await fs.readFile(metadataPath, 'utf-8');
    const metadata = JSON.parse(metadataContent);

    // 读取原脚本内容
    const oldScriptPath = path.join(process.cwd(), metadata.filePath);
    const scriptContent = await fs.readFile(oldScriptPath, 'utf-8');

    // 从脚本内容中提取原始代码（去除元数据注释）
    const codeMatch = scriptContent.match(/\*\/\s*([\s\S]*)$/);
    const originalCode = codeMatch ? codeMatch[1].trim() : scriptContent;

    // 生成新的安全文件名
    const safeFileName = newScriptName.replace(/[^a-zA-Z0-9_\-\u4e00-\u9fa5]/g, '_');
    const newFileName = `${scriptId}_${safeFileName}.js`;
    const newFilePath = path.join(scriptsDir, newFileName);

    // 重新生成脚本内容，使用新名称
    const newFullContent = `/**
 * 自动生成的脚本文件
 * 
 * 脚本ID: ${scriptId}
 * 脚本名称: ${newScriptName}
 * 生成时间: ${metadata.savedAt ? new Date(metadata.savedAt).toLocaleString() : new Date().toLocaleString()}
 * 最后更新: ${new Date().toLocaleString()}
 * 功能类型: ${metadata.metadata?.functionType || 'unknown'}
 * 目标节点: ${metadata.metadata?.targetNodeTypes?.join(', ') || 'unknown'}
 * 依赖项: ${metadata.metadata?.dependencies?.join(', ') || 'none'}
 * 描述: ${metadata.metadata?.description || '无描述'}
 */

${originalCode}`;

    // 保存新的脚本文件
    await fs.writeFile(newFilePath, newFullContent, 'utf-8');

    // 删除旧的脚本文件（如果文件名不同）
    if (oldScriptPath !== newFilePath) {
      await fs.unlink(oldScriptPath);
    }

    // 更新元数据
    const updatedMetadata = {
      ...metadata,
      scriptName: newScriptName,
      fileName: newFileName,
      filePath: newFilePath.replace(process.cwd(), ''),
      updatedAt: new Date().toISOString(),
      fileSize: newFullContent.length
    };

    await fs.writeFile(metadataPath, JSON.stringify(updatedMetadata, null, 2), 'utf-8');

    console.log(`[API] 脚本已重命名: ${metadata.scriptName} -> ${newScriptName}`);

    return NextResponse.json({
      success: true,
      message: '脚本重命名成功',
      data: {
        scriptId,
        oldName: metadata.scriptName,
        newName: newScriptName,
        fileName: newFileName,
        filePath: newFilePath.replace(process.cwd(), ''),
        updatedAt: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('[API] 脚本重命名失败:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '未知错误',
      details: '脚本重命名过程中发生错误'
    }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const scriptId = searchParams.get('scriptId');

    if (!scriptId) {
      return NextResponse.json({
        success: false,
        error: '缺少scriptId参数'
      }, { status: 400 });
    }

    console.log(`[API] 删除脚本请求: ${scriptId}`);

    const scriptsDir = path.join(process.cwd(), 'generated-scripts');
    
    // 读取元数据获取文件信息
    const metadataPath = path.join(scriptsDir, `${scriptId}_metadata.json`);
    const metadataContent = await fs.readFile(metadataPath, 'utf-8');
    const metadata = JSON.parse(metadataContent);

    // 删除脚本文件
    const scriptPath = path.join(process.cwd(), metadata.filePath);
    await fs.unlink(scriptPath);

    // 删除元数据文件
    await fs.unlink(metadataPath);

    console.log(`[API] 脚本已删除: ${scriptId}`);

    return NextResponse.json({
      success: true,
      message: '脚本删除成功',
      data: {
        scriptId,
        deletedAt: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('[API] 脚本删除失败:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '未知错误',
      details: '脚本删除过程中发生错误'
    }, { status: 500 });
  }
} 