"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Play, Zap, Brain, Gamepad2, Sparkles, ArrowRight, Upload, MessageSquare } from "lucide-react"
import Link from "next/link"

export default function HomePage() {
  const templates = [
    {
      id: 1,
      title: "跑酷游戏",
      description: "经典跑酷玩法模板",
      image: "/placeholder.svg?height=120&width=200",
      category: "动作",
      plays: "12.5K",
    },
    {
      id: 2,
      title: "消除游戏",
      description: "三消类游戏模板",
      image: "/placeholder.svg?height=120&width=200",
      category: "益智",
      plays: "8.3K",
    },
    {
      id: 3,
      title: "塔防游戏",
      description: "策略塔防模板",
      image: "/placeholder.svg?height=120&width=200",
      category: "策略",
      plays: "6.7K",
    },
  ]

  const recentProjects = [
    {
      id: 1,
      title: "超级跑酷",
      status: "已完成",
      lastModified: "2小时前",
      thumbnail: "/placeholder.svg?height=80&width=120",
    },
    {
      id: 2,
      title: "魔法消除",
      status: "生成中",
      lastModified: "1天前",
      thumbnail: "/placeholder.svg?height=80&width=120",
    },
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100">
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 text-white">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative container mx-auto px-4 py-20">
          <div className="max-w-4xl mx-auto text-center">
            <div className="flex justify-center mb-6">
              <Badge variant="secondary" className="bg-white/20 text-white border-white/30">
                <Sparkles className="w-4 h-4 mr-2" />
                AI驱动的游戏制作平台
              </Badge>
            </div>
            <h1 className="text-5xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-white to-blue-100 bg-clip-text text-transparent">
              Ignis
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-blue-100">用自然语言描述，AI自动生成互动广告游戏</p>
            <p className="text-lg mb-10 text-blue-200 max-w-2xl mx-auto">
              无需编程经验，只需描述您的创意想法，我们的AI将为您生成完整的可玩广告游戏
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/create">
                <Button size="lg" className="bg-white text-blue-600 hover:bg-blue-50 text-lg px-8 py-3">
                  <Play className="w-5 h-5 mr-2" />
                  开始创建
                </Button>
              </Link>
              <Button
                size="lg"
                variant="outline"
                className="border-white/30 text-white hover:bg-white/10 text-lg px-8 py-3 bg-transparent"
              >
                <MessageSquare className="w-5 h-5 mr-2" />
                查看演示
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">核心功能亮点</h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">三大核心功能，让游戏制作变得简单高效</p>
          </div>

          <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            <Card className="text-center hover:shadow-lg transition-shadow border-0 shadow-md">
              <CardHeader>
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Brain className="w-8 h-8 text-blue-600" />
                </div>
                <CardTitle className="text-xl">智能理解</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-base">AI理解自然语言描述，自动分析游戏需求和玩法机制</CardDescription>
              </CardContent>
            </Card>

            <Card className="text-center hover:shadow-lg transition-shadow border-0 shadow-md">
              <CardHeader>
                <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Zap className="w-8 h-8 text-purple-600" />
                </div>
                <CardTitle className="text-xl">快速生成</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-base">多Agent协作，自动生成3D场景、游戏逻辑和交互效果</CardDescription>
              </CardContent>
            </Card>

            <Card className="text-center hover:shadow-lg transition-shadow border-0 shadow-md">
              <CardHeader>
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Gamepad2 className="w-8 h-8 text-green-600" />
                </div>
                <CardTitle className="text-xl">实时预览</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-base">实时3D预览，支持游戏交互测试和迭代优化</CardDescription>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Quick Start Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="grid lg:grid-cols-2 gap-12 items-start">
            {/* Templates */}
            <div>
              <div className="flex items-center justify-between mb-8">
                <h3 className="text-2xl font-bold">模板库</h3>
                <Link href="/templates">
                  <Button variant="outline" size="sm">
                    查看全部 <ArrowRight className="w-4 h-4 ml-2" />
                  </Button>
                </Link>
              </div>

              <div className="space-y-4">
                {templates.map((template) => (
                  <Card key={template.id} className="hover:shadow-md transition-shadow cursor-pointer">
                    <CardContent className="p-4">
                      <div className="flex gap-4">
                        <img
                          src={template.image || "/placeholder.svg"}
                          alt={template.title}
                          className="w-20 h-12 object-cover rounded bg-gray-100"
                        />
                        <div className="flex-1">
                          <div className="flex items-center justify-between mb-1">
                            <h4 className="font-semibold">{template.title}</h4>
                            <Badge variant="secondary" className="text-xs">
                              {template.category}
                            </Badge>
                          </div>
                          <p className="text-sm text-gray-600 mb-2">{template.description}</p>
                          <div className="flex items-center text-xs text-gray-500">
                            <Play className="w-3 h-3 mr-1" />
                            {template.plays} 次使用
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>

            {/* Recent Projects */}
            <div>
              <div className="flex items-center justify-between mb-8">
                <h3 className="text-2xl font-bold">最近项目</h3>
                <Link href="/projects">
                  <Button variant="outline" size="sm">
                    项目管理 <ArrowRight className="w-4 h-4 ml-2" />
                  </Button>
                </Link>
              </div>

              <div className="space-y-4">
                {recentProjects.map((project) => (
                  <Card key={project.id} className="hover:shadow-md transition-shadow cursor-pointer">
                    <CardContent className="p-4">
                      <div className="flex gap-4">
                        <img
                          src={project.thumbnail || "/placeholder.svg"}
                          alt={project.title}
                          className="w-20 h-12 object-cover rounded bg-gray-100"
                        />
                        <div className="flex-1">
                          <div className="flex items-center justify-between mb-1">
                            <h4 className="font-semibold">{project.title}</h4>
                            <Badge variant={project.status === "已完成" ? "default" : "secondary"} className="text-xs">
                              {project.status}
                            </Badge>
                          </div>
                          <p className="text-sm text-gray-600">{project.lastModified}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>

              <Card className="border-dashed border-2 border-gray-300 hover:border-blue-400 transition-colors cursor-pointer">
                <CardContent className="p-8 text-center">
                  <Upload className="w-8 h-8 text-gray-400 mx-auto mb-3" />
                  <p className="text-gray-600 mb-2">创建新项目</p>
                  <p className="text-sm text-gray-500">描述您的游戏创意</p>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">准备好创建您的第一个游戏了吗？</h2>
          <p className="text-xl mb-8 text-blue-100 max-w-2xl mx-auto">只需几分钟，就能将您的创意变成可玩的互动广告</p>
          <Link href="/create">
            <Button size="lg" className="bg-white text-blue-600 hover:bg-blue-50 text-lg px-8 py-3">
              <Sparkles className="w-5 h-5 mr-2" />
              立即开始创建
            </Button>
          </Link>
        </div>
      </section>
    </div>
  )
}
