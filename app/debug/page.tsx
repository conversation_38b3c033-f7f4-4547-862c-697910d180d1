"use client"

import { useState, useEffect } from "react"
import { useLangGraphClient, LangGraphStatus } from "@/components/langgraph/LangGraphClient"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import { <PERSON><PERSON>, <PERSON>bsContent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { ScrollArea } from "@/components/ui/scroll-area"
import {
  Play,
  RefreshCw,
  Eye,
  Activity,
  ExternalLink,
  Monitor,
  Wifi,
  GitBranch,
  Settings
} from "lucide-react"

interface GraphInfo {
  graph_id: string;
  name: string;
  description?: string;
  status: "idle" | "running" | "completed" | "error";
}

interface ExecutionLog {
  timestamp: string;
  level: "info" | "warning" | "error";
  message: string;
  data?: any;
}

export default function DebugPage() {
  const [graphs, setGraphs] = useState<GraphInfo[]>([])
  const [selectedGraph, setSelectedGraph] = useState<string>("")
  const [isConnected, setIsConnected] = useState(false)
  const [executionLogs, setExecutionLogs] = useState<ExecutionLog[]>([])
  const [userInput, setUserInput] = useState("")
  const [isExecuting, setIsExecuting] = useState(false)

  // 使用LangGraph客户端Hook
  const {
    isConnected: langGraphConnected,
    connectionError,
    agentStatuses,
    streamingExecution,
    getAssistants,
    createThread,
    streamWorkflow,
    stopExecution,
    clearExecutionHistory
  } = useLangGraphClient()

  // 检查LangGraph客户端连接状态并加载助手列表
  useEffect(() => {
    const loadAssistants = async () => {
      try {
        if (langGraphConnected) {
          setIsConnected(true)
          addLog("info", "LangGraph客户端连接成功")

          // 获取可用的助手列表
          const assistants = await getAssistants()
          if (assistants.length > 0) {
            const graphList: GraphInfo[] = assistants.map((assistant: any) => ({
              graph_id: assistant.assistant_id,
              name: assistant.name || assistant.assistant_id.replace(/_/g, " ").replace(/\b\w/g, (l: string) => l.toUpperCase()),
              description: getGraphDescription(assistant.assistant_id),
              status: "idle"
            }))
            setGraphs(graphList)
            if (graphList.length > 0 && !selectedGraph) {
              setSelectedGraph(graphList[0].graph_id)
            }
            addLog("info", `发现 ${graphList.length} 个可用工作流`)
          } else {
            addLog("warning", "未发现可用的工作流")
          }
        } else {
          setIsConnected(false)
          if (connectionError) {
            addLog("error", "LangGraph客户端连接失败", connectionError)
          } else {
            addLog("warning", "LangGraph客户端未连接")
          }
        }
      } catch (error) {
        setIsConnected(false)
        addLog("error", "获取工作流列表失败", error)
      }
    }

    loadAssistants()
  }, [langGraphConnected, connectionError, getAssistants, selectedGraph])

  // 获取图的描述
  const getGraphDescription = (graphId: string): string => {
    switch (graphId) {
      case "playable_gen_workflow":
        return "完整的游戏生成工作流，包含设计和代码生成阶段"
      case "game_design_agent":
        return "专门负责游戏设计的Agent，分析需求并生成设计方案"
      case "code_generation_agent":
        return "专门负责代码生成的Agent，将设计转换为可执行代码"
      default:
        return "LangGraph工作流"
    }
  }

  // 添加执行日志
  const addLog = (level: ExecutionLog["level"], message: string, data?: any) => {
    const log: ExecutionLog = {
      timestamp: new Date().toLocaleTimeString(),
      level,
      message,
      data
    }
    setExecutionLogs(prev => [log, ...prev].slice(0, 100)) // 保留最近100条日志
  }

  // 执行工作流
  const executeWorkflow = async () => {
    if (!selectedGraph || !userInput.trim()) {
      addLog("warning", "请选择工作流并输入测试数据")
      return
    }

    setIsExecuting(true)
    addLog("info", `开始执行工作流: ${selectedGraph}`)

    try {
      // 创建新线程（可选）
      const thread = await createThread()

      // 使用LangGraph SDK流式执行工作流
      await streamWorkflow(
        selectedGraph,
        {
          userRequirement: userInput,
          messages: [{ role: "user", content: userInput }]
        },
        thread?.thread_id,
        "updates" // 流式模式：updates, messages, values
      )

      addLog("info", "工作流执行完成")

    } catch (error) {
      console.error('执行工作流失败:', error)
      addLog("error", "执行工作流失败", error)
    } finally {
      setIsExecuting(false)
    }
  }

  // 清空日志
  const clearLogs = () => {
    setExecutionLogs([])
    addLog("info", "日志已清空")
  }

  // 打开Studio UI
  const openStudioUI = () => {
    window.open("https://smith.langchain.com/studio?baseUrl=http://localhost:2024", "_blank")
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* 页面标题 */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">LangGraph 调试控制台</h1>
              <p className="text-gray-600 mt-2">PlayableGen Agent工作流可视化调试工具</p>
            </div>
            <div className="flex items-center gap-4">
              <Badge variant={isConnected ? "default" : "destructive"} className="flex items-center gap-2">
                <div className={`w-2 h-2 rounded-full ${isConnected ? "bg-green-500" : "bg-red-500"}`} />
                {isConnected ? "已连接" : "未连接"}
              </Badge>
              <Button onClick={openStudioUI} variant="outline" className="flex items-center gap-2">
                <ExternalLink className="w-4 h-4" />
                打开Studio UI
              </Button>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* 左侧：工作流控制 */}
          <div className="lg:col-span-1 space-y-6">
            {/* 工作流选择 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <GitBranch className="w-5 h-5" />
                  工作流选择
                </CardTitle>
                <CardDescription>选择要调试的Agent工作流</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {graphs.map((graph) => (
                  <div
                    key={graph.graph_id}
                    className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                      selectedGraph === graph.graph_id
                        ? "border-blue-500 bg-blue-50"
                        : "border-gray-200 hover:border-gray-300"
                    }`}
                    onClick={() => setSelectedGraph(graph.graph_id)}
                  >
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-medium">{graph.name}</h4>
                        <p className="text-sm text-gray-600 mt-1">{graph.description}</p>
                      </div>
                      <Badge variant="outline">{graph.status}</Badge>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* 输入控制 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="w-5 h-5" />
                  执行控制
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium mb-2 block">测试输入</label>
                  <Textarea
                    placeholder="输入游戏需求描述，例如：创建一个简单的跑酷游戏..."
                    value={userInput}
                    onChange={(e) => setUserInput(e.target.value)}
                    rows={4}
                  />
                </div>
                <div className="flex gap-2">
                  <Button 
                    onClick={executeWorkflow} 
                    disabled={!isConnected || isExecuting || !selectedGraph}
                    className="flex-1"
                  >
                    {isExecuting ? (
                      <>
                        <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                        执行中...
                      </>
                    ) : (
                      <>
                        <Play className="w-4 h-4 mr-2" />
                        执行工作流
                      </>
                    )}
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 右侧：监控和日志 */}
          <div className="lg:col-span-2">
            <Tabs defaultValue="logs" className="h-full">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="logs" className="flex items-center gap-2">
                  <Activity className="w-4 h-4" />
                  执行日志
                </TabsTrigger>
                <TabsTrigger value="monitor" className="flex items-center gap-2">
                  <Monitor className="w-4 h-4" />
                  实时监控
                </TabsTrigger>
                <TabsTrigger value="websocket" className="flex items-center gap-2">
                  <Wifi className="w-4 h-4" />
                  WebSocket状态
                </TabsTrigger>
                <TabsTrigger value="studio" className="flex items-center gap-2">
                  <Eye className="w-4 h-4" />
                  可视化界面
                </TabsTrigger>
              </TabsList>

              <TabsContent value="logs" className="mt-6">
                <Card className="h-[600px]">
                  <CardHeader className="flex flex-row items-center justify-between">
                    <CardTitle>执行日志</CardTitle>
                    <Button onClick={clearLogs} variant="outline" size="sm">
                      清空日志
                    </Button>
                  </CardHeader>
                  <CardContent>
                    <ScrollArea className="h-[500px]">
                      <div className="space-y-2">
                        {executionLogs.length === 0 ? (
                          <p className="text-gray-500 text-center py-8">暂无日志记录</p>
                        ) : (
                          executionLogs.map((log, index) => (
                            <div
                              key={index}
                              className={`p-3 rounded-lg text-sm ${
                                log.level === "error"
                                  ? "bg-red-50 border border-red-200"
                                  : log.level === "warning"
                                  ? "bg-yellow-50 border border-yellow-200"
                                  : "bg-gray-50 border border-gray-200"
                              }`}
                            >
                              <div className="flex items-center justify-between mb-1">
                                <Badge
                                  variant={
                                    log.level === "error"
                                      ? "destructive"
                                      : log.level === "warning"
                                      ? "secondary"
                                      : "outline"
                                  }
                                >
                                  {log.level}
                                </Badge>
                                <span className="text-xs text-gray-500">{log.timestamp}</span>
                              </div>
                              <p className="font-medium">{log.message}</p>
                              {log.data && (
                                <pre className="mt-2 text-xs bg-white p-2 rounded border overflow-x-auto">
                                  {JSON.stringify(log.data, null, 2)}
                                </pre>
                              )}
                            </div>
                          ))
                        )}
                      </div>
                    </ScrollArea>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="monitor" className="mt-6">
                <Card className="h-[600px]">
                  <CardHeader>
                    <CardTitle>实时监控</CardTitle>
                    <CardDescription>Agent执行状态和性能监控</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ScrollArea className="h-[500px]">
                      <div className="space-y-4">
                        {/* Agent状态监控 */}
                        <div className="space-y-3">
                          <h4 className="font-medium text-sm text-gray-700">Agent状态</h4>
                          {agentStatuses.length === 0 ? (
                            <p className="text-gray-500 text-center py-4">暂无Agent状态信息</p>
                          ) : (
                            agentStatuses.map((agent) => (
                              <div key={agent.agentId} className="p-3 border rounded-lg bg-gray-50">
                                <div className="flex items-center justify-between mb-2">
                                  <h5 className="font-medium text-sm">{agent.agentName}</h5>
                                  <Badge
                                    variant={
                                      agent.status === "completed"
                                        ? "default"
                                        : agent.status === "working"
                                        ? "secondary"
                                        : agent.status === "error"
                                        ? "destructive"
                                        : "outline"
                                    }
                                  >
                                    {agent.status}
                                  </Badge>
                                </div>
                                {agent.currentTask && (
                                  <p className="text-xs text-gray-600 mb-2">{agent.currentTask}</p>
                                )}
                                {agent.progress !== undefined && (
                                  <div className="w-full bg-gray-200 rounded-full h-1.5">
                                    <div
                                      className="bg-blue-600 h-1.5 rounded-full transition-all duration-300"
                                      style={{ width: `${agent.progress}%` }}
                                    />
                                  </div>
                                )}
                              </div>
                            ))
                          )}
                        </div>

                        {/* 流式执行监控 */}
                        <div className="space-y-3">
                          <h4 className="font-medium text-sm text-gray-700">流式执行状态</h4>
                          <div className="p-3 border rounded-lg bg-blue-50">
                            <div className="flex items-center justify-between mb-2">
                              <span className="text-sm font-medium">执行状态</span>
                              <Badge variant={streamingExecution.isRunning ? "secondary" : "outline"}>
                                {streamingExecution.isRunning ? "运行中" : "空闲"}
                              </Badge>
                            </div>
                            {streamingExecution.assistantId && (
                              <p className="text-xs text-gray-600 mb-1">
                                助手: {streamingExecution.assistantId}
                              </p>
                            )}
                            {streamingExecution.threadId && (
                              <p className="text-xs text-gray-600 mb-2">
                                线程: {streamingExecution.threadId}
                              </p>
                            )}
                            <p className="text-xs text-gray-600">
                              更新数量: {streamingExecution.updates.length}
                            </p>
                          </div>
                        </div>

                        {/* 最新流式更新 */}
                        {streamingExecution.updates.length > 0 && (
                          <div className="space-y-3">
                            <h4 className="font-medium text-sm text-gray-700">最新流式更新</h4>
                            <div className="space-y-2">
                              {streamingExecution.updates.slice(-5).map((update, index) => (
                                <div key={index} className="p-2 border rounded text-xs bg-white">
                                  <div className="flex items-center justify-between mb-1">
                                    <Badge variant="outline" className="text-xs">
                                      {update.event}
                                    </Badge>
                                  </div>
                                  <pre className="text-xs text-gray-600 overflow-x-auto">
                                    {JSON.stringify(update.data, null, 2)}
                                  </pre>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    </ScrollArea>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="websocket" className="mt-6">
                <Card className="h-[600px]">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Wifi className="w-5 h-5" />
                      WebSocket 实时通信状态
                    </CardTitle>
                    <CardDescription>
                      监控Agent执行状态和进度更新的实时通信
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <LangGraphStatus
                      isConnected={langGraphConnected}
                      connectionError={connectionError}
                    />
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="studio" className="mt-6">
                <Card className="h-[600px]">
                  <CardHeader>
                    <CardTitle>LangGraph Studio 可视化界面</CardTitle>
                    <CardDescription>在新窗口中打开完整的可视化调试界面</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="text-center py-20">
                      <Eye className="w-16 h-16 mx-auto mb-4 text-blue-500" />
                      <h3 className="text-lg font-semibold mb-2">LangGraph Studio</h3>
                      <p className="text-gray-600 mb-6">
                        使用官方的LangGraph Studio进行可视化调试
                      </p>
                      <Button onClick={openStudioUI} size="lg" className="flex items-center gap-2">
                        <ExternalLink className="w-5 h-5" />
                        打开Studio UI
                      </Button>
                      <div className="mt-8 text-left bg-gray-50 p-4 rounded-lg">
                        <h4 className="font-medium mb-2">Studio UI 功能：</h4>
                        <ul className="text-sm text-gray-600 space-y-1">
                          <li>• 可视化工作流图结构</li>
                          <li>• 实时查看Agent执行状态</li>
                          <li>• 交互式调试和测试</li>
                          <li>• 状态检查和修改</li>
                          <li>• 执行历史和日志查看</li>
                        </ul>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>
    </div>
  )
}
