"use client"

import React, { useRef, useEffect, useState, useCallback } from 'react'

interface VirtualJoystickProps {
  /** 摇杆大小 */
  size?: number
  /** 摇杆手柄大小 */
  knobSize?: number
  /** 是否启用 */
  enabled?: boolean
  /** 方向变化回调 */
  onDirectionChange?: (direction: { x: number; y: number }) => void
  /** 摇杆开始回调 */
  onStart?: () => void
  /** 摇杆结束回调 */
  onEnd?: () => void
  /** CSS类名 */
  className?: string
}

interface JoystickState {
  isActive: boolean
  centerX: number
  centerY: number
  knobX: number
  knobY: number
  direction: { x: number; y: number }
}

/**
 * 自定义虚拟摇杆组件
 * 支持点击任意位置出现，摇杆中心点跟随拖拽方向
 * 基于原游戏的虚拟摇杆机制实现
 */
export default function VirtualJoystick({
  size = 120,
  knobSize = 40,
  enabled = true,
  onDirectionChange,
  onStart,
  onEnd,
  className = ""
}: VirtualJoystickProps) {
  const containerRef = useRef<HTMLDivElement>(null)
  const [joystick, setJoystick] = useState<JoystickState>({
    isActive: false,
    centerX: 0,
    centerY: 0,
    knobX: 0,
    knobY: 0,
    direction: { x: 0, y: 0 }
  })

  // 计算方向向量
  const calculateDirection = useCallback((centerX: number, centerY: number, knobX: number, knobY: number) => {
    const deltaX = knobX - centerX
    const deltaY = knobY - centerY
    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY)
    const maxDistance = (size - knobSize) / 2

    if (distance === 0) {
      return { x: 0, y: 0 }
    }

    // 归一化方向向量
    const normalizedX = deltaX / distance
    const normalizedY = deltaY / distance

    // 限制在摇杆范围内
    const clampedDistance = Math.min(distance, maxDistance)
    const strength = clampedDistance / maxDistance

    return {
      x: normalizedX * strength,
      y: -normalizedY * strength // Y轴反转，因为屏幕坐标系Y向下
    }
  }, [size, knobSize])

  // 更新摇杆位置
  const updateJoystick = useCallback((clientX: number, clientY: number, isStart: boolean = false) => {
    if (!containerRef.current) return

    const rect = containerRef.current.getBoundingClientRect()
    const containerCenterX = rect.left + rect.width / 2
    const containerCenterY = rect.top + rect.height / 2

    let centerX: number, centerY: number

    if (isStart) {
      // 摇杆出现在点击位置
      centerX = clientX - rect.left
      centerY = clientY - rect.top
    } else {
      centerX = joystick.centerX
      centerY = joystick.centerY
    }

    // 计算摇杆手柄位置
    const deltaX = (clientX - rect.left) - centerX
    const deltaY = (clientY - rect.top) - centerY
    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY)
    const maxDistance = (size - knobSize) / 2

    let knobX: number, knobY: number

    if (distance <= maxDistance) {
      knobX = centerX + deltaX
      knobY = centerY + deltaY
    } else {
      // 限制在摇杆范围内
      const angle = Math.atan2(deltaY, deltaX)
      knobX = centerX + Math.cos(angle) * maxDistance
      knobY = centerY + Math.sin(angle) * maxDistance
    }

    // 计算方向向量
    const direction = calculateDirection(centerX, centerY, knobX, knobY)

    const newState: JoystickState = {
      isActive: true,
      centerX,
      centerY,
      knobX,
      knobY,
      direction
    }

    setJoystick(newState)

    // 触发回调
    if (onDirectionChange) {
      onDirectionChange(direction)
    }
  }, [joystick.centerX, joystick.centerY, size, knobSize, calculateDirection, onDirectionChange])

  // 处理触摸/鼠标开始
  const handleStart = useCallback((clientX: number, clientY: number) => {
    if (!enabled) return

    updateJoystick(clientX, clientY, true)

    if (onStart) {
      onStart()
    }
  }, [enabled, updateJoystick, onStart])

  // 处理触摸/鼠标移动
  const handleMove = useCallback((clientX: number, clientY: number) => {
    if (!joystick.isActive || !enabled) return

    updateJoystick(clientX, clientY, false)
  }, [joystick.isActive, enabled, updateJoystick])

  // 处理触摸/鼠标结束
  const handleEnd = useCallback(() => {
    if (!joystick.isActive) return

    setJoystick(prev => ({
      ...prev,
      isActive: false,
      direction: { x: 0, y: 0 }
    }))

    if (onDirectionChange) {
      onDirectionChange({ x: 0, y: 0 })
    }

    if (onEnd) {
      onEnd()
    }
  }, [joystick.isActive, onDirectionChange, onEnd])

  // 鼠标事件处理
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    e.preventDefault()
    handleStart(e.clientX, e.clientY)
  }, [handleStart])

  const handleMouseMove = useCallback((e: MouseEvent) => {
    e.preventDefault()
    handleMove(e.clientX, e.clientY)
  }, [handleMove])

  const handleMouseUp = useCallback((e: MouseEvent) => {
    e.preventDefault()
    handleEnd()
  }, [handleEnd])

  // 触摸事件处理
  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    e.preventDefault()
    const touch = e.touches[0]
    handleStart(touch.clientX, touch.clientY)
  }, [handleStart])

  const handleTouchMove = useCallback((e: TouchEvent) => {
    e.preventDefault()
    const touch = e.touches[0]
    if (touch) {
      handleMove(touch.clientX, touch.clientY)
    }
  }, [handleMove])

  const handleTouchEnd = useCallback((e: TouchEvent) => {
    e.preventDefault()
    handleEnd()
  }, [handleEnd])

  // 设置全局事件监听
  useEffect(() => {
    if (joystick.isActive) {
      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)
      document.addEventListener('touchmove', handleTouchMove, { passive: false })
      document.addEventListener('touchend', handleTouchEnd, { passive: false })

      return () => {
        document.removeEventListener('mousemove', handleMouseMove)
        document.removeEventListener('mouseup', handleMouseUp)
        document.removeEventListener('touchmove', handleTouchMove)
        document.removeEventListener('touchend', handleTouchEnd)
      }
    }
  }, [joystick.isActive, handleMouseMove, handleMouseUp, handleTouchMove, handleTouchEnd])

  if (!enabled) {
    return null
  }

  return (
    <div
      ref={containerRef}
      className={`absolute inset-0 pointer-events-auto ${className}`}
      onMouseDown={handleMouseDown}
      onTouchStart={handleTouchStart}
      style={{
        zIndex: 10000,
        touchAction: 'none',
        backgroundColor: 'transparent' // 确保透明背景
      }}
    >
      {joystick.isActive && (
        <>
          {/* 摇杆底盘 */}
          <div
            className="absolute rounded-full border-2 border-white/30 bg-black/20"
            style={{
              left: joystick.centerX - size / 2,
              top: joystick.centerY - size / 2,
              width: size,
              height: size,
              pointerEvents: 'none'
            }}
          />
          {/* 摇杆手柄 */}
          <div
            className="absolute rounded-full bg-white/80 border-2 border-white shadow-lg"
            style={{
              left: joystick.knobX - knobSize / 2,
              top: joystick.knobY - knobSize / 2,
              width: knobSize,
              height: knobSize,
              pointerEvents: 'none'
            }}
          />
        </>
      )}
    </div>
  )
}
