"use client"

import { useState, useEffect, useCallback } from "react"
import { Client } from "@langchain/langgraph-sdk"

// LangGraph消息类型定义
export interface LangGraphMessage {
  type: 'agent_status' | 'progress_update' | 'stream_chunk' | 'error' | 'completed'
  data?: any
  timestamp: number
  id: string
}

// Agent状态类型
export interface AgentStatus {
  agentId: string
  agentName: string
  status: 'idle' | 'working' | 'completed' | 'error'
  currentTask?: string
  progress?: number
}

// 流式执行状态
export interface StreamingExecution {
  threadId: string | null
  assistantId: string
  isRunning: boolean
  messages: any[]
  updates: any[]
}

// LangGraph客户端Hook
export function useLangGraphClient() {
  const [client, setClient] = useState<Client | null>(null)
  const [isConnected, setIsConnected] = useState(false)
  const [connectionError, setConnectionError] = useState<string | null>(null)
  const [agentStatuses, setAgentStatuses] = useState<AgentStatus[]>([])
  const [streamingExecution, setStreamingExecution] = useState<StreamingExecution>({
    threadId: null,
    assistantId: '',
    isRunning: false,
    messages: [],
    updates: []
  })

  // 初始化LangGraph客户端
  useEffect(() => {
    try {
      const langGraphClient = new Client({
        apiUrl: "http://localhost:2024"
      })
      setClient(langGraphClient)
      setIsConnected(true)
      setConnectionError(null)
      console.log('[LangGraph] 客户端初始化成功')
    } catch (error) {
      console.error('[LangGraph] 客户端初始化失败:', error)
      setConnectionError(error instanceof Error ? error.message : '未知错误')
      setIsConnected(false)
    }
  }, [])

  // 获取可用的助手列表
  const getAssistants = useCallback(async () => {
    if (!client) return []
    
    try {
      const assistants = await client.assistants.search({
        metadata: null,
        offset: 0,
        limit: 10,
      })
      console.log('[LangGraph] 获取助手列表:', assistants)
      return assistants
    } catch (error) {
      console.error('[LangGraph] 获取助手列表失败:', error)
      return []
    }
  }, [client])

  // 创建新线程
  const createThread = useCallback(async () => {
    if (!client) return null
    
    try {
      const thread = await client.threads.create()
      console.log('[LangGraph] 创建线程成功:', thread.thread_id)
      return thread
    } catch (error) {
      console.error('[LangGraph] 创建线程失败:', error)
      return null
    }
  }, [client])

  // 流式执行工作流
  const streamWorkflow = useCallback(async (
    assistantId: string,
    input: any,
    threadId?: string | null,
    streamMode: "updates" | "messages" | "values" = "updates"
  ) => {
    if (!client) {
      console.error('[LangGraph] 客户端未初始化')
      return
    }

    try {
      setStreamingExecution(prev => ({
        ...prev,
        assistantId,
        threadId: threadId || null,
        isRunning: true,
        messages: [],
        updates: []
      }))

      // 更新Agent状态为工作中
      setAgentStatuses(prev => [
        ...prev.filter(a => a.agentId !== assistantId),
        {
          agentId: assistantId,
          agentName: assistantId,
          status: 'working',
          currentTask: '执行工作流',
          progress: 0
        }
      ])

      console.log(`[LangGraph] 开始流式执行: ${assistantId}`)
      
      const streamResponse = client.runs.stream(
        threadId,
        assistantId,
        {
          input,
          streamMode
        }
      )

      let chunkCount = 0
      for await (const chunk of streamResponse) {
        chunkCount++
        console.log(`[LangGraph] 接收到流式数据 #${chunkCount}:`, chunk.event, chunk.data)
        
        // 更新流式执行状态
        setStreamingExecution(prev => ({
          ...prev,
          updates: [...prev.updates, chunk],
          messages: streamMode === "messages" ? [...prev.messages, chunk] : prev.messages
        }))

        // 更新Agent进度
        setAgentStatuses(prev => 
          prev.map(agent => 
            agent.agentId === assistantId 
              ? { ...agent, progress: Math.min(90, (chunkCount * 10)) }
              : agent
          )
        )
      }

      // 执行完成
      setStreamingExecution(prev => ({ ...prev, isRunning: false }))
      setAgentStatuses(prev => 
        prev.map(agent => 
          agent.agentId === assistantId 
            ? { ...agent, status: 'completed', progress: 100, currentTask: '执行完成' }
            : agent
        )
      )

      console.log(`[LangGraph] 流式执行完成: ${assistantId}`)

    } catch (error) {
      console.error('[LangGraph] 流式执行失败:', error)
      
      setStreamingExecution(prev => ({ ...prev, isRunning: false }))
      setAgentStatuses(prev => 
        prev.map(agent => 
          agent.agentId === assistantId 
            ? { ...agent, status: 'error', currentTask: '执行失败' }
            : agent
        )
      )
    }
  }, [client])

  // 停止当前执行
  const stopExecution = useCallback(() => {
    setStreamingExecution(prev => ({ ...prev, isRunning: false }))
    setAgentStatuses(prev => 
      prev.map(agent => ({ ...agent, status: 'idle', currentTask: undefined }))
    )
  }, [])

  // 清除执行历史
  const clearExecutionHistory = useCallback(() => {
    setStreamingExecution({
      threadId: null,
      assistantId: '',
      isRunning: false,
      messages: [],
      updates: []
    })
  }, [])

  return {
    client,
    isConnected,
    connectionError,
    agentStatuses,
    streamingExecution,
    getAssistants,
    createThread,
    streamWorkflow,
    stopExecution,
    clearExecutionHistory
  }
}

// LangGraph状态显示组件
export function LangGraphStatus({ 
  isConnected, 
  connectionError 
}: { 
  isConnected: boolean
  connectionError: string | null 
}) {
  return (
    <div className="flex items-center gap-2">
      <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`} />
      <span className="text-sm">
        LangGraph: {isConnected ? '已连接' : connectionError || '未连接'}
      </span>
    </div>
  )
}
