"use client"

import { useState } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Home, Plus, FolderOpen, Settings, Menu, X, User, LogOut, TestTube, Bug, Activity, Palette } from "lucide-react"

export function Navigation() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const pathname = usePathname()

  const navItems = [
    { href: "/", label: "首页", icon: Home },
    { href: "/create", label: "创建", icon: Plus },
    { href: "/canvas", label: "画布", icon: Palette },
    { href: "/test/node-system-demo", label: "节点系统", icon: TestTube },
    { href: "/projects", label: "项目", icon: FolderOpen },
    { href: "/debug", label: "调试", icon: Bug },
    { href: "/mcp-monitoring", label: "MCP监控", icon: Activity },
    { href: "/test-token", label: "Token测试", icon: TestTube },
    { href: "/settings", label: "设置", icon: Settings },
  ]

  const isActive = (href: string) => {
    if (href === "/") {
      return pathname === "/"
    }
    return pathname.startsWith(href)
  }

  return (
    <>
      {/* Desktop Navigation */}
      <nav className="hidden md:flex bg-white border-b px-6 py-2">
        <div className="flex items-center justify-between w-full max-w-7xl mx-auto">
          <div className="flex items-center gap-8">
            <Link href="/" className="flex items-center gap-2">
              <div className="w-12 h-12 bg-black rounded-lg flex items-center justify-center p-1">
                <img 
                  src="/ignis.png" 
                  alt="Ignis" 
                  className="w-full h-full object-contain"
                />
              </div>
              <span className="text-xl font-bold">Ignis</span>
            </Link>

            <div className="flex items-center gap-1">
              {navItems.map((item) => {
                const Icon = item.icon
                return (
                  <Link key={item.href} href={item.href}>
                    <Button variant={isActive(item.href) ? "default" : "ghost"} className="flex items-center gap-2">
                      <Icon className="w-4 h-4" />
                      {item.label}
                    </Button>
                  </Link>
                )
              })}
            </div>
          </div>

          <div className="flex items-center gap-4">
            <Badge variant="secondary" className="bg-green-100 text-green-700">
              在线
            </Badge>
            <Button variant="outline" size="sm">
              <User className="w-4 h-4 mr-2" />
              账户
            </Button>
          </div>
        </div>
      </nav>

      {/* Mobile Navigation */}
      <nav className="md:hidden bg-white border-b">
        <div className="flex items-center justify-between px-4 py-2">
          <Link href="/" className="flex items-center gap-2">
            <div className="w-10 h-10 bg-black rounded-lg flex items-center justify-center p-1">
              <img 
                src="/ignis.png" 
                alt="Ignis" 
                className="w-full h-full object-contain"
              />
            </div>
            <span className="text-lg font-bold">Ignis</span>
          </Link>

          <Button variant="ghost" size="sm" onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}>
            {isMobileMenuOpen ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
          </Button>
        </div>

        {/* Mobile Menu */}
        {isMobileMenuOpen && (
          <div className="border-t bg-white">
            <div className="px-4 py-2 space-y-1">
              {navItems.map((item) => {
                const Icon = item.icon
                return (
                  <Link key={item.href} href={item.href}>
                    <Button
                      variant={isActive(item.href) ? "default" : "ghost"}
                      className="w-full justify-start gap-2"
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      <Icon className="w-4 h-4" />
                      {item.label}
                    </Button>
                  </Link>
                )
              })}
              <div className="border-t pt-2 mt-2">
                <Button variant="ghost" className="w-full justify-start gap-2">
                  <User className="w-4 h-4" />
                  账户设置
                </Button>
                <Button variant="ghost" className="w-full justify-start gap-2 text-red-600">
                  <LogOut className="w-4 h-4" />
                  退出登录
                </Button>
              </div>
            </div>
          </div>
        )}
      </nav>
    </>
  )
}
