"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { 
  DollarSign, 
  Activity, 
  TrendingUp, 
  Download, 
  Trash2,
  RefreshCw,
  BarChart3,
  Clock
} from "lucide-react";
import { tokenTracker, TokenUsageStats, TokenUsageRecord } from "@/src/utils/TokenUsageTracker";

interface TokenUsagePanelProps {
  className?: string;
}

/**
 * Token使用统计面板组件
 * 显示AI模型调用的token使用情况和成本统计
 */
export default function TokenUsagePanel({ className }: TokenUsagePanelProps) {
  const [stats, setStats] = useState<TokenUsageStats | null>(null);
  const [recentRecords, setRecentRecords] = useState<TokenUsageRecord[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [timeRange, setTimeRange] = useState<'today' | 'week' | 'month' | 'all'>('today');

  // 刷新数据
  const refreshData = async () => {
    setIsLoading(true);
    try {
      // 计算时间范围
      let timeFilter: { start: number; end: number } | undefined;
      const now = Date.now();
      
      switch (timeRange) {
        case 'today':
          timeFilter = {
            start: new Date().setHours(0, 0, 0, 0),
            end: now
          };
          break;
        case 'week':
          timeFilter = {
            start: now - 7 * 24 * 60 * 60 * 1000,
            end: now
          };
          break;
        case 'month':
          timeFilter = {
            start: now - 30 * 24 * 60 * 60 * 1000,
            end: now
          };
          break;
        case 'all':
        default:
          timeFilter = undefined;
          break;
      }

      const currentStats = tokenTracker.getStats(timeFilter);
      const records = tokenTracker.getRecentRecords(20);
      
      setStats(currentStats);
      setRecentRecords(records);
    } catch (error) {
      console.error('刷新token统计数据失败:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // 导出数据
  const exportData = () => {
    const data = tokenTracker.exportData();
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `token-usage-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // 清除历史数据
  const clearHistory = () => {
    if (confirm('确定要清除所有历史数据吗？此操作不可撤销。')) {
      tokenTracker.clearHistory();
      refreshData();
    }
  };

  // 格式化货币
  const formatCurrency = (amount: number) => {
    return `$${amount.toFixed(4)}`;
  };

  // 格式化数字
  const formatNumber = (num: number) => {
    return num.toLocaleString();
  };

  // 格式化时间
  const formatTime = (timestamp: number) => {
    return new Date(timestamp).toLocaleString('zh-CN');
  };

  // 初始化和定时刷新
  useEffect(() => {
    refreshData();
    const interval = setInterval(refreshData, 30000); // 30秒刷新一次
    return () => clearInterval(interval);
  }, [timeRange]);

  if (!stats) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center h-32">
          <div className="text-center">
            <RefreshCw className="w-6 h-6 animate-spin mx-auto mb-2" />
            <p className="text-sm text-gray-500">加载统计数据中...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* 头部控制栏 */}
      <div className="flex items-center justify-between">
        <h2 className="text-lg font-semibold">Token使用统计</h2>
        <div className="flex items-center gap-2">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value as any)}
            className="px-3 py-1 border rounded-md text-sm"
          >
            <option value="today">今天</option>
            <option value="week">最近7天</option>
            <option value="month">最近30天</option>
            <option value="all">全部</option>
          </select>
          <Button
            size="sm"
            variant="outline"
            onClick={refreshData}
            disabled={isLoading}
          >
            <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
          </Button>
          <Button size="sm" variant="outline" onClick={exportData}>
            <Download className="w-4 h-4" />
          </Button>
          <Button size="sm" variant="outline" onClick={clearHistory}>
            <Trash2 className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* 概览卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Activity className="w-4 h-4 text-blue-500" />
              <span className="text-sm font-medium">总调用次数</span>
            </div>
            <p className="text-2xl font-bold mt-1">{formatNumber(stats.totalCalls)}</p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <BarChart3 className="w-4 h-4 text-green-500" />
              <span className="text-sm font-medium">总Token数</span>
            </div>
            <p className="text-2xl font-bold mt-1">{formatNumber(stats.totalTokens)}</p>
            <p className="text-xs text-gray-500 mt-1">
              输入: {formatNumber(stats.totalPromptTokens)} | 输出: {formatNumber(stats.totalCompletionTokens)}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <DollarSign className="w-4 h-4 text-red-500" />
              <span className="text-sm font-medium">总成本</span>
            </div>
            <p className="text-2xl font-bold mt-1">{formatCurrency(stats.totalCost)}</p>
            <p className="text-xs text-gray-500 mt-1">
              平均: {formatCurrency(stats.averageCostPerCall)}/次
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <TrendingUp className="w-4 h-4 text-purple-500" />
              <span className="text-sm font-medium">平均Token</span>
            </div>
            <p className="text-2xl font-bold mt-1">{Math.round(stats.averageTokensPerCall)}</p>
            <p className="text-xs text-gray-500 mt-1">每次调用</p>
          </CardContent>
        </Card>
      </div>

      {/* 详细统计 */}
      <Tabs defaultValue="agents" className="w-full">
        <TabsList>
          <TabsTrigger value="agents">按Agent统计</TabsTrigger>
          <TabsTrigger value="models">按模型统计</TabsTrigger>
          <TabsTrigger value="recent">最近记录</TabsTrigger>
        </TabsList>

        <TabsContent value="agents" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Agent使用统计</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {Object.entries(stats.byAgent).map(([agentType, agentStats]) => (
                  <div key={agentType} className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <p className="font-medium">{agentType}</p>
                      <p className="text-sm text-gray-500">
                        {agentStats.calls} 次调用 • {formatNumber(agentStats.tokens)} tokens
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">{formatCurrency(agentStats.cost)}</p>
                      <Progress 
                        value={(agentStats.cost / stats.totalCost) * 100} 
                        className="w-20 h-2 mt-1"
                      />
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="models" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-base">模型使用统计</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {Object.entries(stats.byModel).map(([model, modelStats]) => (
                  <div key={model} className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <p className="font-medium">{model}</p>
                      <p className="text-sm text-gray-500">
                        {modelStats.calls} 次调用 • {formatNumber(modelStats.tokens)} tokens
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">{formatCurrency(modelStats.cost)}</p>
                      <Progress 
                        value={(modelStats.cost / stats.totalCost) * 100} 
                        className="w-20 h-2 mt-1"
                      />
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="recent" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-base">最近调用记录</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 max-h-96 overflow-y-auto">
                {recentRecords.map((record) => (
                  <div key={record.id} className="flex items-center justify-between p-3 border rounded-lg text-sm">
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <Badge variant="outline">{record.agentType}</Badge>
                        <Badge variant="secondary">{record.model}</Badge>
                        {record.duration && (
                          <span className="text-gray-500 flex items-center gap-1">
                            <Clock className="w-3 h-3" />
                            {record.duration}ms
                          </span>
                        )}
                      </div>
                      <p className="text-gray-600 mt-1 truncate">
                        {record.taskDescription || '无任务描述'}
                      </p>
                      <p className="text-xs text-gray-500 mt-1">
                        {formatTime(record.timestamp)}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">{formatCurrency(record.cost)}</p>
                      <p className="text-xs text-gray-500">
                        {formatNumber(record.totalTokens)} tokens
                      </p>
                      <p className="text-xs text-gray-500">
                        {record.promptTokens}→{record.completionTokens}
                      </p>
                    </div>
                  </div>
                ))}
                {recentRecords.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    暂无调用记录
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
