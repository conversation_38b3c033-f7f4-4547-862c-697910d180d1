# Node System Demo - 脚本生成功能使用指南

## 概述

`node-system-demo` 页面实现了集成LangGraph Agent的脚本生成功能，允许用户通过自然语言描述生成场景节点脚本并自动保存到项目文件系统中。

## 功能特性

### 1. 智能脚本生成
- **Agent工作流**: `script_generation_workflow`
- **流式输出**: 实时显示Agent处理过程
- **场景感知**: 基于当前3D场景上下文生成脚本

### 2. 自动文件保存
- **自动保存**: 生成的脚本自动保存到 `generated-scripts/` 目录
- **元数据管理**: 保存脚本的详细元数据信息
- **版本控制**: 支持脚本的版本管理和历史记录

### 3. 场景上下文集成
- **节点信息**: 自动获取当前场景的节点数量和类型
- **资源清单**: 包含可用的3D模型和纹理资源
- **选中节点**: 基于用户当前选中的节点提供上下文

## 使用步骤

### 1. 启动服务

确保LangGraph服务器已启动：
```bash
npx @langchain/langgraph-cli dev
```

启动Next.js应用：
```bash
npm run dev
```

### 2. 访问页面

访问 `http://localhost:3000/test/node-system-demo`

### 3. 使用脚本生成

1. **场景操作**: 在3D视图中添加、选择节点
2. **描述需求**: 在Agent对话框中输入脚本需求
3. **实时反馈**: 观察Agent的流式处理过程
4. **获取结果**: 脚本自动生成并保存

## 使用示例

### 示例1: 相机控制脚本
```
请为选中的相机节点生成一个环绕旋转的脚本，让相机围绕场景中心旋转
```

### 示例2: 物体动画脚本
```
为当前场景中的所有立方体生成一个上下弹跳的动画脚本
```

### 示例3: 交互脚本
```
生成一个点击检测脚本，当用户点击任何模型时显示其名称
```

## 技术架构

### Agent工作流
```typescript
用户需求 → script_generation_workflow → 脚本代码
```

### API调用流程
```
前端页面 → /api/script-generation → LangGraph工作流 → /api/save-script → 文件系统
```

### 文件保存结构
```
generated-scripts/
├── {scriptId}_{scriptName}.ts     # 脚本文件
└── {scriptId}_metadata.json       # 元数据文件
```

## 配置选项

### 场景上下文配置
脚本生成时会自动包含以下场景信息：
- 节点总数和类型分布
- 可用3D模型资源
- 可用纹理资源
- 当前选中的节点信息

### Agent配置
- **模型**: Claude 4 Sonnet
- **温度**: 0.7
- **最大tokens**: 20000
- **流式输出**: 启用

## 故障排除

### 常见问题

1. **LangGraph服务器未启动**
   - 检查 `http://localhost:2024` 是否可访问
   - 重新启动LangGraph服务器

2. **脚本保存失败**
   - 检查文件系统权限
   - 确认 `generated-scripts` 目录可写

3. **流式输出中断**
   - 检查网络连接
   - 刷新页面重试

### 调试信息

查看浏览器控制台和服务器日志获取详细错误信息：
```bash
# 查看LangGraph服务器日志
npx @langchain/langgraph-cli dev --verbose

# 查看Next.js应用日志
npm run dev
```

## API参考

### 脚本生成API
- **端点**: `POST /api/script-generation`
- **参数**: 
  - `userRequirement`: 用户需求描述
  - `sceneContext`: 场景上下文信息
  - `streamMode`: 是否启用流式输出

### 脚本保存API
- **端点**: `POST /api/save-script`
- **参数**:
  - `scriptId`: 脚本唯一ID
  - `scriptName`: 脚本名称
  - `scriptContent`: 脚本内容
  - `metadata`: 元数据信息

## 最佳实践

1. **清晰描述需求**: 提供具体、明确的脚本功能描述
2. **场景准备**: 在生成脚本前先设置好相关的3D场景
3. **批量操作**: 为相关功能生成多个脚本时保持场景一致性
4. **测试验证**: 生成的脚本应在实际场景中进行测试

## 扩展开发

如需扩展功能，可以修改以下文件：
- `src/langgraph/agents/script-generation.ts`: Agent工作流逻辑
- `app/api/script-generation/route.ts`: API路由处理
- `app/test/node-system-demo/page.tsx`: 前端界面和交互
- `app/api/save-script/route.ts`: 文件保存逻辑

---

**注意**: 此功能需要有效的Claude API密钥和LangGraph服务器运行环境。 