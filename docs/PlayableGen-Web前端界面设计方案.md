# PlayableGen Web前端界面设计方案

## 1. 整体架构布局

采用现代化**工作台式布局**，类似Figma设计理念：
- **顶部导航栏**：Logo、项目名称、功能标签页（创建、画布、项目、设置）
- **左侧边栏**：项目管理、模板库、资产库（可折叠）
- **主工作区域**：核心内容展示区域（自适应）
- **右侧面板**：AI对话、属性面板、进度监控（上下文相关）
- **底部状态栏**：连接状态、性能指标、快捷操作

## 2. 核心页面设计

### 2.1 首页/欢迎页面
- **Hero区域**：平台介绍、价值主张、"开始创建"主按钮
- **快速开始**：模板库展示、最近项目列表、示例演示
- **功能亮点**：三个核心功能的图标化展示

### 2.2 项目创建页面
- **智能输入区域**：大尺寸多行文本框，支持自然语言描述
- **AI辅助功能**：实时提示、智能补全、语音输入
- **参考资料上传**：拖拽上传图片、视频、3D模型
- **快速配置**：游戏类型、屏幕方向、目标平台标签选择
- **模板系统**：预设游戏类型模板快速插入

### 2.3 "可玩画布"页面（核心）
- **中央画布**：Babylon.js 3D渲染视口，支持实时交互
- **左侧工具面板**：场景树、属性编辑器、资产库
- **右侧AI面板**：对话窗口、进度监控、建议系统
- **底部控制栏**：播放控制、时间轴、性能监控
- **核心功能**：实时3D预览、Inspector集成、AI对话系统

### 2.4 项目管理页面
- **双视图模式**：网格视图（卡片）和列表视图（表格）
- **智能搜索**：项目名称、标签、内容全文搜索
- **多维筛选**：按状态、类型、时间、标签筛选
- **批量操作**：多选删除、导出、分享、归档

### 2.5 设置/配置页面
- **界面设置**：主题、语言、布局偏好
- **AI配置**：模型选择、生成参数、API设置
- **性能优化**：渲染质量、内存限制、缓存设置
- **账户管理**：个人信息、团队协作、权限控制

## 3. 关键功能组件

### 3.1 自然语言输入界面
**核心特性**：
- 智能提示系统：基于输入内容提供相关建议
- 语音输入支持：语音转文字功能
- 模板快速插入：常用描述模板快捷选择
- 实时内容分析：检查完整性并给出改进建议
- 多媒体支持：文字、图片、视频、音频混合输入

### 3.2 实时任务进度可视化
**可视化方式**：
- 流程图展示：DAG（有向无环图）可视化呈现
- 状态指示器：不同颜色和图标表示各阶段状态
- 进度条显示：整体进度和单个任务进度
- 时间估算：剩余时间和完成预期动态更新

### 3.3 AI Agent工作状态监控
**监控内容**：
- Agent状态：空闲、工作中、等待、错误等状态
- 任务队列：当前任务和等待队列可视化
- 性能指标：CPU、内存、API调用次数等资源
- 工作历史：每个Agent的工作记录和统计

### 3.4 迭代修改对话界面
**功能特性**：
- 自然语言交互：用户用自然语言描述修改需求
- 上下文理解：AI理解项目状态和历史对话
- 实时反馈：修改执行过程的实时进度显示
- 智能建议：AI主动提供优化和改进建议

### 3.5 资产预览和管理
**资产分类**：3D模型、纹理贴图、音效文件、代码文件、配置文件
**管理功能**：预览功能、元数据显示、版本管理、批量操作、搜索筛选

## 4. 用户交互流程

### 4.1 完整创作流程
1. **需求输入**：自然语言描述 + 参考文件上传 + 配置选择
2. **AI生成**：跳转画布页面，实时显示Agent工作进度
3. **预览测试**：画布实时预览，支持游戏交互测试
4. **迭代优化**：AI对话界面修改，实时查看效果
5. **导出发布**：选择平台格式，自动优化打包

### 4.2 人机协作方式
- **AI主导**：负责90%重复性工作
- **人工辅助**：负责10%创意调优和质量把控
- **协作界面**：实时对话系统 + 可视化编辑器 + 版本控制

### 4.3 错误处理机制
- **错误预防**：输入验证、状态监控、资源限制
- **错误恢复**：自动重试、人工介入、状态回滚
- **用户反馈**：清晰错误信息、解决方案建议、技术支持

## 5. 技术集成要求

### 5.1 Babylon.js集成
- React组件封装画布，支持热重载
- 集成Inspector调试工具
- 鼠标触摸事件处理，键盘快捷键支持
- 场景对象选择操作，相机控制切换

### 5.2 WebSocket实时通信
- 自动连接重连机制，连接状态可视化
- 实时进度更新，Agent状态同步
- 资产生成通知，错误警告推送

### 5.3 文件管理系统
- 拖拽上传支持，批量上传，进度显示
- 单文件批量下载，压缩包生成
- 云存储集成，本地缓存，版本控制

## 6. 用户体验考虑

### 6.1 目标用户特点
- **非技术背景**：市场人员、运营人员
- **效率导向**：希望快速完成任务
- **学习成本敏感**：希望界面简单易懂

### 6.2 设计原则
- **简洁性**：界面元素精简，避免信息过载
- **一致性**：统一设计语言和交互模式
- **反馈性**：及时操作反馈和状态提示
- **容错性**：友好错误处理和恢复机制

这个设计方案为PlayableGen平台提供了完整的前端界面框架，确保用户能够高效、直观地使用AI驱动的互动广告制作功能。
