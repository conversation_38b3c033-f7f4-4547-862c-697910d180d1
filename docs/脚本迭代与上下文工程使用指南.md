# 🧠 脚本迭代与上下文工程使用指南

## 📖 概述

基于最新的Context Engineering技术和LangGraph持久化状态管理，我们实现了一套完整的脚本持续迭代优化系统。该系统能够记住之前的脚本状态、学习用户偏好，并避免重复性错误，实现真正的"AI记忆"。

## 🎯 核心特性

### 1. 三层记忆架构
- **语义记忆（Semantic Memory）**：记住脚本的事实和功能
- **情节记忆（Episodic Memory）**：记住过去的决策和经验
- **程序记忆（Procedural Memory）**：记住编码规范和最佳实践

### 2. 持久化上下文
- 基于LangGraph的状态管理
- 跨会话的脚本上下文保持
- 自动版本控制和迭代历史

### 3. 智能增量迭代
- 避免重写整个脚本
- 基于现有代码进行最小化修改
- 防止AI撤销之前的成功修改

## 🚀 快速开始

### 1. 初始化脚本上下文

首次使用时，需要为脚本初始化上下文：

```javascript
// API调用示例
const response = await fetch('/api/script-iteration', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    action: 'initialize',
    scriptId: 'script_1234567890',
    scriptContent: '// 你的脚本代码',
    metadata: {
      name: '玩家旋转脚本',
      description: '实现玩家方块的旋转功能',
      dependencies: ['@babylonjs/core'],
      functionType: 'animation'
    }
  })
});
```

### 2. 执行脚本迭代

在初始化后，可以进行持续的脚本迭代：

```javascript
const response = await fetch('/api/script-iteration', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    action: 'iterate',
    userRequest: '按下空格键让方块跳跃',
    scriptId: 'script_1234567890',
    iterationType: 'extend', // enhance | fix | refactor | extend
    priority: 'medium',
    expectedBehavior: '按空格键时方块向上跳跃'
  })
});
```

### 3. 获取脚本上下文

查看当前脚本的完整上下文信息：

```javascript
const response = await fetch('/api/script-iteration', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    action: 'getContext',
    scriptId: 'script_1234567890'
  })
});
```

## 📋 迭代类型说明

### enhance（增强）
- 添加新功能或改进现有功能
- 保持原有逻辑完整性
- 适用于功能扩展

### fix（修复）
- 修复bug或错误
- 基于错误报告进行针对性修复
- 参考历史失败记录避免重复问题

### refactor（重构）
- 改进代码结构和性能
- 保持功能不变但优化实现
- 风险级别较高，需要谨慎处理

### extend（扩展）
- 添加全新的功能模块
- 与现有功能并行工作
- 适用于添加独立的新特性

## 🧠 Context Engineering 原理

### 1. 长期记忆管理

系统会自动维护三种类型的记忆：

```typescript
interface ContextMemory {
  // 语义记忆：关于脚本的事实
  semanticMemory: {
    scriptPurpose: string;        // 脚本目的
    coreFeatures: string[];       // 核心功能列表
    dependencies: string[];       // 依赖项
    constraints: string[];        // 约束条件
    userPreferences: string[];    // 用户偏好
  };
  
  // 情节记忆：过去的经验
  episodicMemory: Array<{
    decision: string;             // 决策内容
    context: string;              // 决策上下文
    outcome: string;              // 结果
    lessons: string[];            // 学到的教训
  }>;
  
  // 程序记忆：编码规范
  proceduralMemory: {
    codingStandards: string[];    // 编码标准
    commonPatterns: string[];     // 常用模式
    avoidPatterns: string[];      // 避免的模式
    debuggingMethods: string[];   // 调试方法
  };
}
```

### 2. 状态持久化

使用LangGraph的MemorySaver实现状态持久化：

```typescript
// 工作流状态包含完整的上下文信息
interface WorkflowState {
  scriptId: string;
  currentScript: {
    content: string;
    version: number;
    functionality: string[];
  };
  iterationHistory: IterationRecord[];
  contextMemory: ContextMemory;
  // ... 其他状态
}
```

### 3. 增量迭代流程

```mermaid
graph TD
    A[接收迭代请求] --> B[检查脚本上下文]
    B --> C[分析用户需求]
    C --> D[执行增量迭代]
    D --> E[验证迭代结果]
    E --> F[更新上下文记忆]
    F --> G[保存脚本版本]
```

## 🔧 使用示例

### 场景1：从基础脚本开始

```javascript
// 1. 初始化一个简单的旋转脚本
await initializeScript('rotation_script', `
function executeScript(scene) {
  const nodeRegistry = NodeRegistry.getInstance();
  const player = nodeRegistry.getNode('player');
  
  if (player) {
    scene.registerBeforeRender(() => {
      player.rotation.y += 0.01;
    });
  }
}
`);

// 2. 添加键盘控制
await iterateScript({
  userRequest: '添加键盘控制，WASD控制旋转方向',
  iterationType: 'extend'
});

// 3. 添加跳跃功能
await iterateScript({
  userRequest: '按空格键让方块跳跃',
  iterationType: 'extend'
});

// 4. 优化性能
await iterateScript({
  userRequest: '优化性能，避免每帧都检查键盘输入',
  iterationType: 'refactor'
});
```

### 场景2：错误修复

```javascript
// 如果脚本报错
await iterateScript({
  userRequest: '修复"Cannot read property rotation of null"错误',
  iterationType: 'fix',
  expectedBehavior: '脚本应该正常运行不报错'
});
```

### 场景3：功能增强

```javascript
// 基于现有功能进行增强
await iterateScript({
  userRequest: '让旋转速度根据键盘按压时间变化',
  iterationType: 'enhance',
  expectedBehavior: '按键时间越长，旋转越快'
});
```

## 📊 上下文记忆示例

### 语义记忆记录

```json
{
  "scriptPurpose": "实现玩家方块的旋转和跳跃功能",
  "coreFeatures": [
    "Y轴旋转动画",
    "键盘WASD控制",
    "空格键跳跃",
    "性能优化渲染"
  ],
  "dependencies": ["@babylonjs/core", "NodeRegistry"],
  "constraints": ["避免每帧键盘检查", "确保节点存在性验证"],
  "userPreferences": ["简洁的代码结构", "详细的错误处理"]
}
```

### 情节记忆记录

```json
[
  {
    "decision": "添加键盘事件监听器",
    "context": "用户要求WASD控制",
    "outcome": "成功实现但性能有问题",
    "lessons": ["应该缓存键盘状态而不是每帧检查"]
  },
  {
    "decision": "实现跳跃功能",
    "context": "扩展现有旋转功能",
    "outcome": "成功添加跳跃逻辑",
    "lessons": ["新功能应该与现有功能模块化分离"]
  }
]
```

### 程序记忆记录

```json
{
  "codingStandards": [
    "使用Babylon.js 8.17.2 API",
    "通过NodeRegistry获取节点",
    "包含try-catch错误处理"
  ],
  "commonPatterns": [
    "scene.registerBeforeRender()模式",
    "nodeRegistry.getNode()模式",
    "键盘状态缓存模式"
  ],
  "avoidPatterns": [
    "避免每帧进行DOM查询",
    "避免全局变量污染",
    "避免硬编码的魔法数字"
  ]
}
```

## 🎛️ 高级特性

### 1. LangGraph工作流集成

使用LangGraph工作流进行复杂的脚本迭代：

```javascript
// 通过LangGraph工作流执行迭代
const workflowResult = await scriptIterationGraph.invoke({
  scriptId: 'my_script',
  userRequest: '添加粒子效果',
  iterationType: 'extend'
}, {
  configurable: { thread_id: 'script_session_123' }
});
```

### 2. 并行迭代处理

系统支持多个脚本的并行迭代处理：

```javascript
// 为不同脚本维护独立的上下文
const script1Context = await getScriptContext('script_1');
const script2Context = await getScriptContext('script_2');

// 并行执行迭代
const [result1, result2] = await Promise.all([
  iterateScript({ scriptId: 'script_1', userRequest: '优化性能' }),
  iterateScript({ scriptId: 'script_2', userRequest: '添加音效' })
]);
```

### 3. 版本管理和回滚

每次迭代都会创建新版本，支持版本追踪：

```javascript
// 查看迭代历史
const context = await getScriptContext('my_script');
console.log('迭代历史:', context.iterationHistory);

// 版本信息包含在文件中
/*
 * 脚本版本: v3
 * 最后更新: 2024-01-15 10:30:00
 * 变更说明: 添加跳跃功能
 * 修改原因: 用户要求空格键控制跳跃
 */
```

## 🔍 故障排除

### 常见问题

1. **上下文未初始化**
   ```
   错误：脚本上下文不存在
   解决：先调用initialize action初始化脚本
   ```

2. **迭代失败**
   ```
   错误：AI生成的代码语法错误
   解决：系统会自动记录失败原因，下次迭代时避免重复错误
   ```

3. **记忆混乱**
   ```
   错误：AI产生了与之前相矛盾的修改
   解决：检查情节记忆中的lessons，确保AI学习了正确的模式
   ```

### 调试技巧

1. **查看完整上下文**
   ```javascript
   const context = await getScriptContext('my_script');
   console.log('完整上下文:', JSON.stringify(context, null, 2));
   ```

2. **检查迭代历史**
   ```javascript
   context.iterationHistory.forEach((iteration, index) => {
     console.log(`版本 ${iteration.version}: ${iteration.changes}`);
   });
   ```

3. **分析记忆模式**
   ```javascript
   console.log('学到的经验:', context.contextMemory.episodicMemory);
   console.log('编码模式:', context.contextMemory.proceduralMemory);
   ```

## 🚀 最佳实践

### 1. 渐进式迭代
- 每次只添加一个功能
- 先测试基础功能再添加复杂特性
- 保持迭代步骤的原子性

### 2. 明确的需求描述
```javascript
// ❌ 模糊的需求
userRequest: "让它更好"

// ✅ 具体的需求
userRequest: "当按下空格键时，方块向上跳跃0.5个单位高度，持续0.5秒"
```

### 3. 合理的迭代类型选择
- **新功能** → 使用 `extend`
- **改进现有功能** → 使用 `enhance`
- **修复问题** → 使用 `fix`
- **代码重构** → 使用 `refactor`

### 4. 利用记忆系统
- 让AI学习你的编码偏好
- 建立项目特定的编码模式
- 积累调试和优化经验

## 📈 性能优化

### 1. 记忆管理
- 系统自动限制记忆大小（最近10条迭代历史）
- 定期清理过时的上下文信息
- 优先保留成功的迭代经验

### 2. 并发控制
- 每个脚本独立的上下文管理
- 支持多用户并行使用
- 智能的资源分配

### 3. 缓存策略
- 脚本上下文缓存在内存中
- 文件系统持久化备份
- 懒加载和按需初始化

## 🎯 未来扩展

### 1. 多模态支持
- 支持图像输入的脚本生成
- 语音交互的脚本迭代
- 可视化的代码编辑

### 2. 团队协作
- 多用户共享的脚本上下文
- 迭代权限管理
- 版本冲突解决

### 3. 智能推荐
- 基于使用模式的功能推荐
- 性能优化建议
- 代码质量评估

---

## 📞 技术支持

如果遇到问题，请检查：

1. LangGraph服务器是否正常运行（`http://localhost:2024`）
2. 脚本上下文是否已正确初始化
3. API请求格式是否正确
4. 控制台是否有相关错误信息

通过这套系统，您可以实现真正的"AI编程助手"体验，让AI记住您的偏好，学习您的模式，并持续改进脚本质量。 