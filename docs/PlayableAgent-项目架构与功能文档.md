# PlayableAgent 项目架构与功能文档

## 🎯 项目概述

PlayableAgent 是一个基于 **Next.js + Three.js + LangGraph** 的AI驱动游戏开发平台，通过自然语言描述实现游戏的快速生成和迭代开发。项目采用多Agent协作架构，实现了从创意到可玩游戏的完整自动化流程。

### 核心理念
- **VibeCoding**: 通过自然语言描述直接生成游戏代码
- **AI驱动**: 多专业AI Agent协作完成游戏制作流程
- **模块化**: 组件化设计，每个文件控制在500行内
- **实时迭代**: 支持实时预览和迭代优化

## 🏗️ 技术架构

### 核心技术栈

| 层级 | 技术选型 | 版本 | 作用 |
|------|----------|------|------|
| **前端框架** | Next.js | 15.2.4 | 全栈React框架 |
| **3D引擎** | Three.js | 0.178.0 | WebGL 3D渲染引擎 |
| **AI编排** | LangGraph | 最新 | AI Agent工作流管理 |
| **AI模型** | Claude 4 Sonnet | - | 代码生成和设计 |
| **状态管理** | React Context | - | 全局状态管理 |
| **类型系统** | TypeScript | 5+ | 类型安全 |
| **样式系统** | TailwindCSS | 4+ | 原子化CSS |

### 系统架构图

```mermaid
graph TB
    A[用户界面] --> B[Next.js前端]
    B --> C[API路由层]
    C --> D[AI Agent系统]
    C --> E[Three.js 3D引擎]
    C --> F[文件系统]
    
    D --> D1[游戏设计Agent]
    D --> D2[代码生成Agent]
    D --> D3[脚本迭代Agent]
    
    E --> E1[场景管理]
    E --> E2[模型加载]
    E --> E3[动画系统]
    
    F --> F1[项目文件]
    F --> F2[生成代码]
    F --> F3[检查点系统]
```

## 🎮 核心功能模块

### 1. 节点系统演示 (`app/test/node-system-demo`)

这是项目的核心演示页面，展示了完整的AI驱动3D游戏开发工作流。

#### 1.1 架构组成

```
node-system-demo/
├── page.tsx                     # 主入口页面
├── contexts/
│   └── NodeSystemContext.tsx    # 全局状态管理中心
└── components/
    ├── AgentChatPanel.tsx        # AI Agent对话界面
    ├── ThreeSceneViewer.tsx      # Three.js 3D场景查看器
    ├── NodePropertiesPanel.tsx   # 节点属性编辑面板
    ├── ScriptManager.tsx         # 脚本管理组件
    ├── AnimationManager.tsx      # 动画管理器
    ├── MaterialManager.tsx       # 材质管理器
    ├── ModelManager.tsx          # 模型管理器
    └── CheckpointIndicator.tsx   # 版本管理指示器
```

#### 1.2 状态管理架构

**NodeSystemContext** 是整个系统的状态管理中心，管理以下状态：

```typescript
interface NodeSystemState {
  // 3D场景状态
  nodes: GameNodeProperties[]           // 场景节点列表
  selectedNode: GameNodeProperties      // 当前选中节点
  threeContext: ThreeCanvasContext      // Three.js上下文
  
  // AI Agent状态
  agentMessages: AgentMessage[]         // 对话消息历史
  isAgentProcessing: boolean            // Agent处理状态
  currentSessionId: string              // 当前会话ID
  
  // 资源管理状态
  uploadedModels: Map<string, ModelInfo[]>      // 模型资源
  uploadedAnimations: Map<string, AnimationInfo[]>  // 动画资源
  uploadedMaterials: Map<string, MaterialInfo[]>    // 材质资源
  
  // 脚本管理状态
  scriptFiles: ScriptFile[]             // 脚本文件列表
  selectedScript: ScriptFile            // 当前选中脚本
  
  // 版本控制状态
  checkpoints: Checkpoint[]             // 检查点列表
  currentCheckpointId: string           // 当前检查点
}
```

#### 1.3 核心组件功能

**AgentChatPanel** - AI对话界面
- 支持流式对话输出
- 集成多个专业Agent
- 自动保存对话历史
- 支持代码块高亮显示

**ThreeSceneViewer** - 3D场景查看器
- 基于Three.js 0.178.0构建
- 支持FBX模型加载
- 实时场景编辑
- 相机控制和交互

**NodePropertiesPanel** - 属性编辑面板
- 节点基础属性编辑（位置、旋转、缩放）
- 材质和纹理管理
- 动画播放控制
- 模型替换功能

### 2. AI Agent系统 (`src/agents`)

这是项目的AI大脑，负责游戏设计和代码生成的核心逻辑。

#### 2.1 Agent架构

```
src/agents/
├── BaseAgent.ts                 # Agent基类
├── GameDesignAgent.ts           # 游戏设计专家
├── CodeGenerationAgent.ts      # 代码生成专家
├── ScriptIterationAgent.ts     # 脚本迭代专家
├── AgentCoordinator.ts          # Agent协调器
├── OptimizationManager.ts       # 优化管理器
├── CustomClaude4Client.ts       # Claude4客户端
└── prompts/                     # 提示词模板
    ├── CodeGenerationPrompts.ts
    └── ThreeJSBestPractices.ts
```

#### 2.2 核心Agent功能

**BaseAgent** - 所有Agent的基类
```typescript
export abstract class BaseAgent {
  protected model: CustomClaude4Client;
  protected graph: LangGraphWorkflow;
  
  // 核心方法
  public async run(input: string): Promise<AgentState>
  public async runStream(input: string, callback: StreamCallback): Promise<AgentState>
  protected abstract executeNode(state: AgentState): Promise<Partial<AgentState>>
}
```

**GameDesignAgent** - 游戏设计专家
- 分析用户需求生成游戏设计方案
- 选择合适的Three.js组件和预设
- 输出结构化的游戏设计JSON

**CodeGenerationAgent** - 代码生成专家
- 基于游戏设计生成Three.js代码
- 支持HTML5游戏完整代码输出
- 集成质量检查和代码优化

**ScriptIterationAgent** - 脚本迭代专家
- 根据用户反馈迭代优化脚本
- 支持增量修改和功能扩展
- 维护代码一致性和质量

#### 2.3 工作流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant C as AgentCoordinator
    participant D as GameDesignAgent
    participant G as CodeGenerationAgent
    
    U->>C: 发送游戏需求
    C->>D: 分析设计需求
    D->>D: 生成游戏设计方案
    D->>C: 返回设计结果
    C->>G: 基于设计生成代码
    G->>G: 生成Three.js代码
    G->>C: 返回生成的代码
    C->>U: 返回完整游戏
```

### 3. Three.js 3D引擎 (`src/three`)

项目已从Babylon.js迁移到Three.js 0.178.0，提供了完整的3D渲染基础设施。

#### 3.1 架构组成

```
src/three/
├── core/
│   ├── ThreeEngine.ts           # 引擎管理器
│   ├── ThreeUtils.ts            # 工具函数库
│   └── BaseThreeScene.ts        # 基础场景类
├── components/
│   └── ThreeCanvas.tsx          # React Canvas组件
└── loaders/
    └── FBXModelLoader.ts        # FBX模型加载器
```

#### 3.2 核心功能

**ThreeEngine** - 引擎管理器
- 标准化的渲染器创建和配置
- 多实例管理和生命周期控制
- 性能优化和资源管理

**ThreeCanvas** - React集成组件
- 提供声明式的Three.js使用方式
- 自动处理渲染循环和资源清理
- 支持多种配置选项

**FBXModelLoader** - 模型加载器
- 支持FBX模型和动画加载
- 模型缓存和资源管理
- 动画播放控制

### 4. API路由系统 (`app/api`)

提供了丰富的后端API支持前端功能。

#### 4.1 主要API端点

| 端点 | 功能 | 方法 |
|------|------|------|
| `/api/generate-game` | 生成完整游戏 | POST |
| `/api/generate-game-stream` | 流式游戏生成 | POST |
| `/api/script-generation` | 脚本生成 | POST |
| `/api/script-iteration` | 脚本迭代 | POST |
| `/api/checkpoints` | 版本管理 | GET/POST |
| `/api/projects` | 项目管理 | GET/POST |
| `/api/upload-model` | 模型上传 | POST |
| `/api/node-properties` | 节点属性 | GET/POST |

#### 4.2 API特性

- **流式响应**: 支持Server-Sent Events实时输出
- **错误处理**: 统一的错误处理和日志记录
- **类型安全**: 完整的TypeScript类型定义
- **状态持久化**: 自动保存到JSON文件

## 🎨 用户界面设计

### 1. 主页面布局

项目采用三栏式布局设计：

```
┌─────────────────────────────────────────────────────────┐
│                     导航栏                               │
├───────────────┬─────────────────────┬───────────────────┤
│               │                     │                   │
│   AI Agent    │     3D场景视图       │   节点属性面板      │
│   对话面板     │   (ThreeSceneViewer) │ (NodeProperties)   │
│               │                     │                   │
│               ├─────────────────────┤                   │
│               │    脚本管理区域      │                   │
│               │  (ScriptManager)    │                   │
└───────────────┴─────────────────────┴───────────────────┘
```

### 2. 组件设计原则

- **响应式设计**: 支持不同屏幕尺寸
- **实时反馈**: 所有操作都有即时视觉反馈
- **模块化**: 每个功能区域独立组件化
- **可访问性**: 遵循Web可访问性标准

## 🔧 开发工作流

### 1. 项目启动

```bash
# 安装依赖
npm install

# 启动LangGraph服务
npx @langchain/langgraph-cli dev

# 启动Next.js开发服务器
npm run dev

# 访问演示页面
http://localhost:3000/test/node-system-demo
```

### 2. 开发规范

- **代码规范**: 每个文件不超过500行
- **命名规范**: 使用中文注释，英文变量名
- **组件设计**: 单一职责原则，高内聚低耦合
- **API设计**: RESTful设计，统一错误处理

### 3. 文件组织

- **组件按功能分组**: 相关组件放在同一目录
- **类型定义集中**: `src/types/` 目录统一管理类型
- **工具函数模块化**: `src/utils/` 目录提供通用工具
- **配置文件集中**: `src/config/` 目录管理配置

## 🚀 核心特性

### 1. VibeCoding体验

- **自然语言输入**: 用户只需描述想要的游戏功能
- **实时生成**: AI Agent实时生成代码并应用到场景
- **可视化反馈**: 代码变更立即在3D场景中体现

### 2. 模块化架构

- **组件化设计**: 每个功能独立组件，便于维护和扩展
- **状态管理**: 统一的状态管理确保数据一致性
- **类型安全**: 完整的TypeScript支持

### 3. AI驱动开发

- **多Agent协作**: 不同专业Agent各司其职
- **工作流管理**: LangGraph管理复杂的AI工作流
- **质量保证**: 自动代码检查和优化

### 4. 版本控制系统

- **检查点机制**: 自动创建代码生成检查点
- **回滚功能**: 支持一键回滚到历史版本
- **增量更新**: 支持基于历史版本的增量修改

## 📈 性能优化

### 1. 渲染性能

- **Three.js优化**: 使用高效的渲染管线
- **资源管理**: 自动缓存和清理3D资源
- **LOD系统**: 支持多级细节优化

### 2. 内存管理

- **组件懒加载**: 按需加载大型组件
- **资源释放**: 自动清理未使用的资源
- **状态优化**: 优化状态更新减少重渲染

### 3. 代码分割

- **路由分割**: 不同页面独立打包
- **组件分割**: 大型组件按需加载
- **API分割**: API路由独立部署

## 🎯 使用场景

### 1. 快速原型开发

- 游戏设计师可以快速验证创意
- 无需编程基础即可创建可玩原型
- 支持快速迭代和修改

### 2. 教育和学习

- 学习Three.js和游戏开发
- 理解AI驱动的开发流程
- 掌握现代Web技术栈

### 3. 创意实验

- 探索新的游戏机制
- 测试用户交互设计
- 验证技术可行性

## 🔮 未来规划

### 1. 功能扩展

- [ ] 物理引擎集成 (Cannon.js)
- [ ] 音效系统支持
- [ ] 网络多人功能
- [ ] VR/AR支持

### 2. AI能力提升

- [ ] 更多专业Agent (音效设计师、UI设计师)
- [ ] 多模态输入支持 (图片、语音)
- [ ] 智能优化建议

### 3. 平台化发展

- [ ] 用户系统和权限管理
- [ ] 云端项目存储
- [ ] 社区分享功能
- [ ] 商业化游戏发布

## 🔍 关键目录详细说明

### `/app/test/node-system-demo` - 核心演示系统

这是项目最重要的演示页面，集成了所有核心功能：

**特点**:
- 完整的AI驱动3D游戏开发体验
- 实时的Agent对话和代码生成
- 直观的3D场景编辑和预览
- 完善的资源管理系统

**技术亮点**:
- 基于React Context的复杂状态管理
- Three.js与React的深度集成
- 流式AI响应处理
- 检查点版本控制系统

### `/src/agents` - AI智能体系统

这是项目的AI大脑，实现了多专业Agent协作：

**核心Agent**:
- `BaseAgent`: 提供统一的Agent基础架构
- `GameDesignAgent`: 专业游戏设计专家
- `CodeGenerationAgent`: 代码生成专家
- `ScriptIterationAgent`: 脚本迭代优化专家

**技术特色**:
- 基于LangGraph的工作流管理
- Claude 4深度集成
- 流式响应处理
- 质量检查和优化机制

## 📚 开发最佳实践

### 1. 组件开发

```typescript
// 组件模板示例
export interface ComponentProps {
  // 明确的属性定义
}

export const Component: React.FC<ComponentProps> = ({ 
  // 解构props
}) => {
  // hooks 使用
  // 事件处理函数
  // 渲染逻辑
  
  return (
    // JSX 结构
  );
};
```

### 2. API开发

```typescript
// API路由模板
export async function POST(request: NextRequest) {
  try {
    // 参数验证
    // 业务逻辑
    // 返回结果
    
    return NextResponse.json({
      success: true,
      data: result
    });
  } catch (error) {
    // 统一错误处理
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}
```

### 3. Agent开发

```typescript
// Agent类模板
export class CustomAgent extends BaseAgent {
  constructor() {
    super({
      name: "Agent名称",
      role: "Agent角色",
      systemPrompt: "系统提示词"
    });
  }
  
  protected async executeNode(state: AgentState): Promise<Partial<AgentState>> {
    // Agent执行逻辑
  }
}
```

---

**PlayableAgent** 代表了AI驱动游戏开发的未来方向，通过将复杂的技术抽象为自然语言交互，让游戏创作变得更加直观和高效。项目的模块化架构和完整的工具链为后续的扩展和优化奠定了坚实的基础。

## 📖 相关文档

- [Node System Demo使用指南](./node-system-demo使用指南.md)
- [Three.js迁移完成报告](./Three.js-迁移完成报告.md)
- [Checkpoint系统实施方案](./checkpoint-system-implementation.md)
- [脚本迭代与上下文工程使用指南](./脚本迭代与上下文工程使用指南.md)

---

*最后更新: 2025年1月25日*
*文档版本: v1.0* 