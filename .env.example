# PlayableGen 环境变量配置模板
# 复制此文件为 .env.local 并填入实际值

# ===========================================
# Claude API 配置
# ===========================================
CLAUDE_API_KEY=sk-your-claude-api-key-here
CLAUDE_BASE_URL=http://oneapi.funplus.com/v1/
CLAUDE_MODEL=claude-4-sonnet

# ===========================================
# Token 配置
# ===========================================
# 默认最大token数
DEFAULT_MAX_TOKENS=20000

# 游戏设计Agent的最大token数
DESIGN_MAX_TOKENS=20000

# 代码生成Agent的最大token数
CODE_MAX_TOKENS=20000

# 小任务的最大token数
SMALL_TASK_MAX_TOKENS=8000

# 大任务的最大token数
LARGE_TASK_MAX_TOKENS=30000

# ===========================================
# Temperature 配置
# ===========================================
# 默认温度
DEFAULT_TEMPERATURE=0.7

# 创意任务温度（游戏设计）
CREATIVE_TEMPERATURE=0.9

# 精确任务温度（代码生成）
PRECISE_TEMPERATURE=0.2

# 平衡模式温度
BALANCED_TEMPERATURE=0.5

# ===========================================
# 超时配置（毫秒）
# ===========================================
# 默认超时时间（2分钟）
DEFAULT_TIMEOUT=120000

# 短任务超时时间（1分钟）
SHORT_TIMEOUT=60000

# 长任务超时时间（5分钟）
LONG_TIMEOUT=300000

# 超长任务超时时间（10分钟）
EXTRA_LONG_TIMEOUT=600000

# ===========================================
# 应用配置
# ===========================================
# 游戏输出目录
GAMES_OUTPUT_DIR=./public/games

# 是否启用调试日志
DEBUG_LOGGING=true

# 是否启用Token统计
ENABLE_TOKEN_TRACKING=true
