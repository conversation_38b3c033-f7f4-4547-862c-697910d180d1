/**
 * LangGraph工具函数
 * 用于检查和管理LangGraph服务器状态
 */

// LangGraph服务器配置
export const LANGGRAPH_CONFIG = {
  API_URL: 'http://localhost:2024',
  STUDIO_URL: 'https://smith.langchain.com/studio/?baseUrl=http://127.0.0.1:2024',
  PORT: 2024,
  RECONNECT_INTERVAL: 3000,
  MAX_RECONNECT_ATTEMPTS: 10,
  HEARTBEAT_INTERVAL: 30000,
  SERVER_START_DELAY: 2000, // 服务器启动延迟
}

/**
 * 检查LangGraph服务器是否运行
 */
export async function checkLangGraphServer(): Promise<boolean> {
  try {
    const response = await fetch(`${LANGGRAPH_CONFIG.API_URL}/info`)
    return response.ok
  } catch (error) {
    console.error('[LangGraph Utils] 检查服务器状态失败:', error)
    return false
  }
}

/**
 * 获取LangGraph服务器信息
 */
export async function getLangGraphServerInfo(): Promise<any> {
  try {
    const response = await fetch(`${LANGGRAPH_CONFIG.API_URL}/info`)
    if (response.ok) {
      return await response.json()
    }
    return null
  } catch (error) {
    console.error('[LangGraph Utils] 获取服务器信息失败:', error)
    return null
  }
}

/**
 * 测试LangGraph API连接
 */
export async function testLangGraphConnection(): Promise<boolean> {
  try {
    const response = await fetch(`${LANGGRAPH_CONFIG.API_URL}/info`, {
      method: 'GET',
      timeout: 5000
    })
    return response.ok
  } catch (error) {
    console.error('[LangGraph Utils] 连接测试失败:', error)
    return false
  }
}

/**
 * 确保LangGraph服务器运行
 * 检查服务器状态并测试连接
 */
export async function ensureLangGraphServer(): Promise<boolean> {
  console.log('[LangGraph Utils] 检查LangGraph服务器状态...')
  
  // 检查服务器是否运行
  const isRunning = await checkLangGraphServer()
  if (isRunning) {
    console.log('[LangGraph Utils] LangGraph服务器已运行')
    
    // 测试连接
    const canConnect = await testLangGraphConnection()
    if (canConnect) {
      return true
    } else {
      console.log('[LangGraph Utils] 服务器运行但无法连接')
      return false
    }
  } else {
    console.log('[LangGraph Utils] LangGraph服务器未运行，请启动服务器: npx @langchain/langgraph-cli dev')
    return false
  }
}

/**
 * LangGraph连接状态类型
 */
export type LangGraphConnectionStatus = 'connecting' | 'connected' | 'disconnected' | 'error'

/**
 * 获取连接状态的显示文本
 */
export function getConnectionStatusText(status: LangGraphConnectionStatus): string {
  switch (status) {
    case 'connecting':
      return '连接中'
    case 'connected':
      return '已连接'
    case 'disconnected':
      return '已断开'
    case 'error':
      return '连接错误'
    default:
      return '未知状态'
  }
}

/**
 * 获取连接状态的颜色类名
 */
export function getConnectionStatusColor(status: LangGraphConnectionStatus): string {
  switch (status) {
    case 'connecting':
      return 'bg-blue-500'
    case 'connected':
      return 'bg-green-500'
    case 'disconnected':
      return 'bg-gray-500'
    case 'error':
      return 'bg-red-500'
    default:
      return 'bg-gray-500'
  }
}
