{"graphs": {"playable_gen_workflow": "./src/langgraph/workflow.ts:playableGenWorkflow", "game_design_agent": "./src/langgraph/agents/game-design.ts:gameDesignGraph", "code_generation_agent": "./src/langgraph/agents/code-generation.ts:codeGenerationGraph", "script_generation_workflow": "./src/langgraph/agents/script-generation.ts:scriptGenerationGraph", "script_iteration_workflow": "./src/langgraph/agents/script-iteration-workflow.ts:scriptIterationGraph"}, "node_version": "20", "env": ".env.local", "dockerfile_lines": ["RUN apt-get update && apt-get install -y curl"]}