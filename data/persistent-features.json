[{"id": "script_1753603324516", "name": "增加通过wasd可以控制主角上下左右移动的逻辑", "description": "增加通过wasd可以控制主角上下左右移动的逻辑", "scriptContent": "function executeScript(scene, camera, renderer) {\n  // 获取主角对象\n  const player = scene.getObjectByName('box_1753425549287');\n  if (!player) {\n    console.warn('Player object not found');\n    return;\n  }\n\n  // 键盘状态追踪\n  const keys = {\n    w: false,\n    a: false,\n    s: false,\n    d: false\n  };\n\n  // 移动速度（单位/秒）\n  const moveSpeed = 5;\n  let lastTime = performance.now();\n  let animationId;\n\n  // 键盘事件处理\n  function onKeyDown(event) {\n    event.preventDefault();\n    const key = event.key.toLowerCase();\n    if (keys.hasOwnProperty(key)) {\n      keys[key] = true;\n    }\n  }\n\n  function onKeyUp(event) {\n    event.preventDefault();\n    const key = event.key.toLowerCase();\n    if (keys.hasOwnProperty(key)) {\n      keys[key] = false;\n    }\n  }\n\n  // 更新主角位置\n  function updatePlayer(deltaTime) {\n    const moveDistance = moveSpeed * deltaTime;\n    \n    if (keys.w) player.position.z -= moveDistance; // 向前\n    if (keys.s) player.position.z += moveDistance; // 向后\n    if (keys.a) player.position.x -= moveDistance; // 向左\n    if (keys.d) player.position.x += moveDistance; // 向右\n  }\n\n  // 动画循环\n  function animate() {\n    const currentTime = performance.now();\n    const deltaTime = (currentTime - lastTime) / 1000;\n    lastTime = currentTime;\n\n    updatePlayer(deltaTime);\n    \n    animationId = requestAnimationFrame(animate);\n  }\n\n  // 添加事件监听器\n  document.addEventListener('keydown', onKeyDown);\n  document.addEventListener('keyup', onKeyUp);\n\n  // 开始动画\n  animate();\n\n  return function cleanup() {\n    // 清理事件监听器\n    document.removeEventListener('keydown', onKeyDown);\n    document.removeEventListener('keyup', onKeyUp);\n    \n    // 停止动画循环\n    if (animationId) {\n      cancelAnimationFrame(animationId);\n    }\n  };\n}", "addedAt": "2025-07-27T08:02:05.794Z", "isActive": true}]