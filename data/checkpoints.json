{"checkpoints": [{"id": "checkpoint_1753603315186_yvrh8ehk9", "version": "v1.0", "messageId": "msg_1753603314927_pauz2gwc9", "timestamp": "2025-07-27T08:01:55.186Z", "description": "增加通过wasd可以控制主角上下左右移动的逻辑", "state": {"scripts": [], "scene": {"nodes": [{"id": "ground", "name": "地面", "type": "mesh", "visible": true, "enabled": true, "position": {"x": 0, "y": 0, "z": 0}, "rotation": {"x": -1.5707963267948966, "y": 0, "z": 0}, "scaling": {"x": 1, "y": 1, "z": 1}, "children": [], "geometry": {"type": "ground", "parameters": {"width": 20, "height": 20}}, "material": {"type": "standard", "diffuseColor": 8421504}}, {"id": "player_box", "name": "熊", "type": "mesh", "visible": true, "enabled": true, "position": {"x": 2, "y": 0, "z": 0}, "rotation": {"x": 0, "y": 0, "z": 0}, "scaling": {"x": 0.015, "y": 0.015, "z": 0.015}, "children": [], "geometry": {"type": "box", "parameters": {"size": 2}}, "material": {"type": "standard", "diffuseColor": 3368703}}, {"id": "directional_light", "name": "方向光源", "type": "light", "visible": true, "enabled": true, "position": {"x": 1, "y": 2, "z": 3}, "rotation": {"x": 0, "y": 0, "z": 0}, "scaling": {"x": 1, "y": 1, "z": 1}, "children": [], "lightType": "directional", "intensity": 1, "color": 16777215}, {"id": "main_camera", "name": "主摄像机", "type": "camera", "visible": true, "enabled": true, "position": {"x": 0, "y": 15, "z": 10}, "rotation": {"x": -0.4636476090008063, "y": 0, "z": 0}, "scaling": {"x": 1, "y": 1, "z": 1}, "children": [], "cameraType": "free", "target": {"x": 0, "y": 0, "z": 0}, "fov": 75, "minZ": 0.1, "maxZ": 1000, "viewLocked": false}, {"id": "box_1753425549287", "name": "主角", "type": "mesh", "visible": true, "enabled": true, "position": {"x": 0, "y": 0, "z": 0}, "rotation": {"x": 0, "y": 0, "z": 0}, "scaling": {"x": 0.02, "y": 0.02, "z": 0.02}, "children": [], "geometry": {"type": "box", "parameters": {"size": 1}}, "material": {"type": "standard", "diffuseColor": 13560318}}], "selectedNode": null, "nodeStats": {"total": 5, "byType": {"mesh": 3, "light": 1, "camera": 1}}}, "messages": [], "session": {"sessionId": "session_2025-07-27", "messageCount": 0}}, "metadata": {"userAction": "增加通过wasd可以控制主角上下左右移动的逻辑", "generatedScripts": [], "appliedChanges": [], "creationType": "auto", "isImportant": false, "tags": []}}], "currentCheckpointId": "checkpoint_1753603315186_yvrh8ehk9", "lastUpdated": "2025-07-27T08:01:55.189Z", "version": "1.0.0", "metadata": {"totalCount": 1, "maxCount": 50, "autoCleanup": true}}