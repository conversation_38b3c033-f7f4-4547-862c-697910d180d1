# PlayableGen：AI驱动的互动广告制作平台

## 项目概述

PlayableGen是一个革命性的AI驱动平台，旨在将互动广告（Playable Ads）的生产流程从传统的手工作坊模式转变为自动化的"创意到广告"流水线。通过多个专业AI Agent的协作，平台能够从单一的自然语言指令自动生成完整的可投放HTML游戏广告。

### 核心愿景
- **从数周到数小时**：将广告制作周期从数周缩短至数小时或一两天
- **规模化生产**：实现大规模A/B测试和创意迭代的可能性
- **营销速度优势**：构建难以被竞争对手逾越的核心竞争力

### 关键绩效指标（KPI）
- 🚀 **生产速度**：从数周缩短至数小时/天
- 📈 **生产吞吐量**：提升至少10倍
- 💰 **单位成本**：降低70%以上
- 🎯 **广告性能**：通过AI优化超越手动制作

## 技术架构

### 核心技术栈

| 组件 | 技术选型 | 选择理由 |
|------|----------|----------|
| **3D引擎** | Babylon.js | 原生TypeScript支持、内置Inspector、API稳定性 |
| **AI编排** | LangGraph | 显式工作流定义、状态持久化、可观测性 |
| **前端框架** | React + Vite + TypeScript | 现代开发体验、类型安全 |
| **后端服务** | Node.js + TypeScript | 统一技术栈、高性能 |
| **云存储** | Amazon S3 | 资产存储和管理 |
| **数据库** | PostgreSQL + 向量数据库 | 结构化数据 + AI知识库 |

### AI模型工具包

- **3D模型生成**：Meshy API、Tripo AI
- **2D资产生成**：DALL-E 3、Midjourney、Stable Diffusion
- **动画制作**：Meshy Animation
- **音效生成**：ElevenLabs、Suno AI
- **代码生成**：GPT-4.1、Claude 3.7 Sonnet

## AI Agent工作小组

### 核心Agent角色

| Agent | 职责 | 主要技术 |
|-------|------|----------|
| **项目经理Agent** | 需求解析与任务编排 | LLM + 任务分解 |
| **资产设计师Agent** | 2D/3D美术资产生成 | Meshy、DALL-E API |
| **动画师Agent** | 3D模型动画制作 | Meshy Animation |
| **场景导演Agent** | 3D场景构建与布局 | Babylon.js API |
| **音效设计师Agent** | 游戏音效与音乐 | ElevenLabs、Suno |
| **程序员Agent** | 游戏逻辑编程 | Code LLM + TypeScript |
| **QA Agent** | 自动化测试与验证 | 多模态模型 |
| **优化师Agent** | 性能优化与打包 | 压缩工具 |

### 协作机制

**通用资产清单（UAM）**作为Agent间通信的核心协议：
- 结构化JSON对象，定义所有资产和配置
- 作为系统的唯一真实数据源（Single Source of Truth）
- 支持异步任务处理和状态追踪

## 工作流程

```mermaid
graph TD
    A[用户输入需求] --> B[项目经理Agent解析]
    B --> C[创建UAM和任务DAG]
    C --> D[资产设计师Agent]
    C --> E[音效设计师Agent]
    D --> F[动画师Agent]
    F --> G[场景导演Agent]
    E --> G
    G --> H[程序员Agent]
    H --> I[QA Agent]
    I --> J[优化师Agent]
    J --> K[输出HTML文件]
    K --> L[用户迭代反馈]
    L --> B
```

### 典型使用场景

1. **输入**：用户输入"制作一款类似'弓箭传说'的竖屏游戏，主角使用'暗夜骑士'模型"
2. **处理**：AI Agent工作小组自动分工协作
3. **输出**：生成可立即投放的HTML互动广告
4. **迭代**：用户通过自然语言进行微调优化

## 功能特性

### 🎨 "可玩画布"
- 实时交互式3D场景环境
- 集成Babylon.js Inspector调试工具
- 支持人机协作的微调功能
- 代码驱动的场景配置

### 🧠 智能知识库
- 向量数据库存储成功案例
- 支持检索增强生成（RAG）
- 自动学习和优化提示策略
- 版本控制和变更追踪

### 🔄 迭代优化
- 自然语言修改指令
- 实时预览和调试
- A/B测试支持
- 性能指标追踪

## 实施路线图

### 第一阶段：核心验证（1-3个月）⭐⭐⭐
**目标**：验证CoderAgent生成可执行Babylon.js代码的可行性

- [ ] 搭建Babylon.js + LangGraph基础框架
- [ ] 开发"可玩画布"核心组件
- [ ] 实现程序员Agent v1.0
- [ ] 端到端技术原型验证

### 第二阶段：多Agent协作（4-7个月）⭐⭐
**目标**：实现从文本提示到静态场景的自动化生成

- [ ] 开发多个专业Agent
- [ ] 实现UAM Schema和协作协议
- [ ] 集成外部AI API
- [ ] 构建资产生成流水线

### 第三阶段：平台整合（8-12个月）⭐
**目标**：交付功能完整的V1版本

- [ ] 完善所有Agent功能
- [ ] 构建用户友好界面
- [ ] 实现完整端到端流程
- [ ] 内部团队试用和优化

## 主要挑战与解决方案

| 挑战 | 解决方案 |
|------|----------|
| **模型可靠性** | 错误处理、重试机制、输出验证、人工兜底 |
| **风格一致性** | 统一风格描述符、艺术总监Agent审核 |
| **提示复杂性** | 预制模板库、结构化提示、参数化配置 |
| **最后10%打磨** | 人机协作模式、专家微调界面 |

## 项目价值

PlayableGen不仅是一个效率工具，更是一个战略性投资：

- **营销速度**：快速测试和迭代，找到最优广告创意
- **规模优势**：大规模实验能力，数据驱动决策
- **成本效益**：显著降低单位创意成本
- **竞争壁垒**：构建难以复制的技术护城河

## 快速开始

### 环境要求
- Node.js 18+
- TypeScript 5+
- 现代浏览器（支持WebGL 2.0）

### 安装步骤
```bash
# 克隆项目
git clone https://gitlab-ee.funplus.io/playable/ignis.git
cd ignis

# 安装依赖
npm install

# 配置环境变量
cp .env.example .env.local
# 编辑 .env.local 文件，填入你的API密钥和配置

# 启动开发服务器
npm run dev
```

### 环境变量配置

PlayableGen使用环境变量进行配置管理。请复制`.env.example`文件为`.env.local`并填入实际值：

```bash
# Claude API 配置
CLAUDE_API_KEY=your-claude-api-key-here
CLAUDE_BASE_URL=http://oneapi.funplus.com/v1/
CLAUDE_MODEL=claude-4-sonnet

# Token 配置
DEFAULT_MAX_TOKENS=20000
DESIGN_MAX_TOKENS=20000
CODE_MAX_TOKENS=20000

# Temperature 配置
DEFAULT_TEMPERATURE=0.7
CREATIVE_TEMPERATURE=0.9
PRECISE_TEMPERATURE=0.2

# 超时配置（毫秒）
DEFAULT_TIMEOUT=120000
LONG_TIMEOUT=300000

# 应用配置
GAMES_OUTPUT_DIR=./public/games
DEBUG_LOGGING=true
ENABLE_TOKEN_TRACKING=true
```

### 配置测试

访问 `http://localhost:3000/test-env-config` 来验证环境变量配置是否正确。

### 基础使用
```typescript
import { createPlayableGenAPI } from './agents';

const api = createPlayableGenAPI({
  outputDir: './games',
  enableLogging: true
});

const result = await api.generateGame({
  description: "制作一个3D跳跃游戏",
  preferences: {
    gameType: "action",
    difficulty: "easy",
    theme: "卡通风格"
  }
});
```

---

*本项目基于LoveArt.ai等多智能体协作模型的成功经验，结合互动广告行业的特定需求，旨在构建下一代AI驱动的创意生产平台。*
