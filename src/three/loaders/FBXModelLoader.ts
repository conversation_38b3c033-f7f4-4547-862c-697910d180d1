/**
 * FBXModelLoader - Three.js FBX模型加载器
 * 提供FBX模型、动画、材质的完整加载和管理功能
 * 基于Three.js 0.178.0 FBXLoader实现
 */

import * as THREE from 'three';
import { FBXLoader } from 'three/examples/jsm/loaders/FBXLoader.js';

export interface FBXLoadOptions {
  /** 模型路径 */
  url: string;
  /** 是否自动播放动画 */
  autoPlayAnimation?: boolean;
  /** 默认动画索引 */
  defaultAnimationIndex?: number;
  /** 是否启用阴影 */
  enableShadows?: boolean;
  /** 模型缩放 */
  scale?: number | THREE.Vector3;
  /** 模型位置 */
  position?: THREE.Vector3;
  /** 模型旋转 */
  rotation?: THREE.Euler;
  /** 加载进度回调 */
  onProgress?: (progress: ProgressEvent) => void;
  /** 加载完成回调 */
  onLoad?: (model: FBXModel) => void;
  /** 加载错误回调 */
  onError?: (error: ErrorEvent) => void;
}

export interface AnimationClipInfo {
  name: string;
  duration: number;
  tracks: number;
  clip: THREE.AnimationClip;
}

export interface FBXModel {
  /** 模型对象 */
  object: THREE.Group;
  /** 动画混合器 */
  mixer?: THREE.AnimationMixer;
  /** 动画剪辑信息 */
  animations: AnimationClipInfo[];
  /** 当前播放的动画 */
  currentAnimation?: THREE.AnimationAction;
  /** 模型材质 */
  materials: THREE.Material[];
  /** 模型网格 */
  meshes: THREE.Mesh[];
  /** 骨骼 */
  skeleton?: THREE.Skeleton;
  /** 加载选项 */
  options: FBXLoadOptions;
}

/**
 * FBX模型加载器类
 * 提供FBX模型的加载、动画控制和资源管理功能
 */
export class FBXModelLoader {
  private loadingManager: THREE.LoadingManager;
  private loadedModels: Map<string, FBXModel> = new Map();

  constructor() {
    this.loadingManager = new THREE.LoadingManager();
    
    // 设置加载管理器事件
    this.setupLoadingManager();
  }

  /**
   * 设置加载管理器事件
   */
  private setupLoadingManager(): void {
    this.loadingManager.onStart = (url, itemsLoaded, itemsTotal) => {
      console.log(`[FBXLoader] 开始加载: ${url} (${itemsLoaded}/${itemsTotal})`);
    };

    this.loadingManager.onProgress = (url, itemsLoaded, itemsTotal) => {
      console.log(`[FBXLoader] 加载进度: ${url} (${itemsLoaded}/${itemsTotal})`);
    };

    this.loadingManager.onLoad = () => {
      console.log('[FBXLoader] 所有资源加载完成');
    };

    this.loadingManager.onError = (url) => {
      console.error(`[FBXLoader] 加载失败: ${url}`);
    };
  }

  /**
   * 加载FBX模型
   */
  public async loadModel(options: FBXLoadOptions): Promise<FBXModel> {
    return new Promise((resolve, reject) => {
      const {
        url,
        autoPlayAnimation = false,
        defaultAnimationIndex = 0,
        enableShadows = true,
        scale,
        position,
        rotation,
        onProgress,
        onLoad,
        onError
      } = options;

      // 检查是否已加载
      if (this.loadedModels.has(url)) {
        const existingModel = this.loadedModels.get(url)!;
        const clonedModel = this.cloneModel(existingModel);
        resolve(clonedModel);
        return;
      }

      // 使用FBXLoader加载真实的FBX文件
      const fbxLoader = new FBXLoader(this.loadingManager);

      fbxLoader.load(
        url,
        (object) => {
          try {
            console.log(`[FBXLoader] FBX文件加载成功: ${url}`);

            const model = this.processLoadedModel(object, options);

            // 缓存模型
            this.loadedModels.set(url, model);

            // 调用回调
            if (onLoad) onLoad(model);

            resolve(model);
          } catch (error) {
            console.error(`[FBXLoader] FBX模型处理失败: ${url}`, error);
            reject(error);
          }
        },
        (progress) => {
          if (onProgress) onProgress(progress);
        },
        (error) => {
          console.error(`[FBXLoader] FBX文件加载失败: ${url}`, error);
          const errorEvent = new ErrorEvent('FBX Load Error', { error });
          if (onError) onError(errorEvent);
          reject(error);
        }
      );
    });
  }

  /**
   * 处理加载的模型
   */
  private processLoadedModel(object: THREE.Group, options: FBXLoadOptions): FBXModel {
    const {
      enableShadows = true,
      scale,
      position,
      rotation
    } = options;

    // 收集材质和网格
    const materials: THREE.Material[] = [];
    const meshes: THREE.Mesh[] = [];
    let skeleton: THREE.Skeleton | undefined;

    object.traverse((child) => {
      if (child instanceof THREE.Mesh) {
        meshes.push(child);
        
        // 启用阴影
        if (enableShadows) {
          child.castShadow = true;
          child.receiveShadow = true;
        }

        // 收集材质
        if (child.material) {
          if (Array.isArray(child.material)) {
            materials.push(...child.material);
          } else {
            materials.push(child.material);
          }
        }

        // 获取骨骼
        if (child instanceof THREE.SkinnedMesh && child.skeleton) {
          skeleton = child.skeleton;
        }
      }
    });

    // 应用变换
    if (scale) {
      if (typeof scale === 'number') {
        object.scale.setScalar(scale);
      } else {
        object.scale.copy(scale);
      }
    }

    if (position) {
      object.position.copy(position);
    }

    if (rotation) {
      object.rotation.copy(rotation);
    }

    // 处理动画（模拟）
    const animations: AnimationClipInfo[] = [];
    let mixer: THREE.AnimationMixer | undefined;
    let currentAnimation: THREE.AnimationAction | undefined;

    const model: FBXModel = {
      object,
      mixer,
      animations,
      currentAnimation,
      materials,
      meshes,
      skeleton,
      options
    };

    console.log(`[FBXLoader] FBX模型处理完成:`, {
      url: options.url,
      meshes: meshes.length,
      materials: materials.length,
      animations: animations.length,
      hasSkeleton: !!skeleton
    });

    return model;
  }

  /**
   * 克隆模型
   */
  public cloneModel(originalModel: FBXModel): FBXModel {
    const clonedObject = originalModel.object.clone(true);
    
    // 重新收集克隆后的材质和网格
    const materials: THREE.Material[] = [];
    const meshes: THREE.Mesh[] = [];
    let skeleton: THREE.Skeleton | undefined;

    clonedObject.traverse((child) => {
      if (child instanceof THREE.Mesh) {
        meshes.push(child);
        
        if (child.material) {
          if (Array.isArray(child.material)) {
            materials.push(...child.material);
          } else {
            materials.push(child.material);
          }
        }

        if (child instanceof THREE.SkinnedMesh && child.skeleton) {
          skeleton = child.skeleton;
        }
      }
    });

    return {
      object: clonedObject,
      mixer: undefined,
      animations: originalModel.animations,
      currentAnimation: undefined,
      materials,
      meshes,
      skeleton,
      options: originalModel.options
    };
  }

  /**
   * 销毁模型资源
   */
  public disposeModel(model: FBXModel): void {
    // 销毁几何体和材质
    model.meshes.forEach(mesh => {
      if (mesh.geometry) {
        mesh.geometry.dispose();
      }
    });

    model.materials.forEach(material => {
      material.dispose();
    });

    console.log(`[FBXLoader] 模型资源已销毁: ${model.options.url}`);
  }

  /**
   * 清理所有缓存的模型
   */
  public clearCache(): void {
    this.loadedModels.forEach(model => {
      this.disposeModel(model);
    });
    this.loadedModels.clear();
    console.log('[FBXLoader] 模型缓存已清理');
  }
}

// 导出单例实例
export const fbxModelLoader = new FBXModelLoader();
