/**
 * ThreeEngineManager - Three.js 0.178.0 引擎管理器
 * 提供标准化的引擎创建、配置和生命周期管理
 * 基于最新Three.js 0.178.0 API实现
 */

import * as THREE from 'three';

export interface ThreeEngineConfig {
  /** 是否启用抗锯齿 */
  antialias?: boolean;
  /** 是否启用alpha通道 */
  alpha?: boolean;
  /** 是否保留绘图缓冲区 */
  preserveDrawingBuffer?: boolean;
  /** 功率偏好设置 */
  powerPreference?: 'default' | 'high-performance' | 'low-power';
  /** 是否启用深度缓冲 */
  depth?: boolean;
  /** 是否启用模板缓冲 */
  stencil?: boolean;
  /** 是否启用调试模式 */
  debugMode?: boolean;
  /** 像素比率 */
  pixelRatio?: number;
  /** 色调映射 */
  toneMapping?: THREE.ToneMapping;
  /** 曝光度 */
  toneMappingExposure?: number;
}

export interface ThreeEngineInstance {
  id: string;
  renderer: THREE.WebGLRenderer;
  canvas: HTMLCanvasElement;
  config: ThreeEngineConfig;
  isRunning: boolean;
  animationId?: number;
}

/**
 * Three.js引擎管理器单例类
 * 负责管理多个Three.js渲染器实例的创建、配置和销毁
 */
export class ThreeEngineManager {
  private static instance: ThreeEngineManager;
  private engines: Map<string, ThreeEngineInstance> = new Map();
  private defaultConfig: ThreeEngineConfig;

  private constructor() {
    this.defaultConfig = {
      antialias: true,
      alpha: false,
      preserveDrawingBuffer: false,
      powerPreference: 'high-performance',
      depth: true,
      stencil: false,
      debugMode: process.env.NODE_ENV === 'development',
      pixelRatio: typeof window !== 'undefined' ? Math.min(window.devicePixelRatio, 2) : 1,
      toneMapping: THREE.ACESFilmicToneMapping,
      toneMappingExposure: 1.0
    };
  }

  /**
   * 获取引擎管理器单例实例
   */
  public static getInstance(): ThreeEngineManager {
    if (!ThreeEngineManager.instance) {
      ThreeEngineManager.instance = new ThreeEngineManager();
    }
    return ThreeEngineManager.instance;
  }

  /**
   * 创建标准化的Three.js渲染器
   * @param canvas HTML Canvas元素
   * @param engineId 引擎唯一标识符
   * @param config 引擎配置选项
   * @returns 创建的渲染器实例
   */
  public createEngine(
    canvas: HTMLCanvasElement,
    engineId: string = 'default',
    config: Partial<ThreeEngineConfig> = {}
  ): THREE.WebGLRenderer {
    // 如果引擎已存在，先销毁
    if (this.engines.has(engineId)) {
      this.destroyEngine(engineId);
    }

    // 合并配置
    const finalConfig: ThreeEngineConfig = {
      ...this.defaultConfig,
      ...config
    };

    // 创建渲染器
    const renderer = new THREE.WebGLRenderer({
      canvas,
      antialias: finalConfig.antialias,
      alpha: finalConfig.alpha,
      preserveDrawingBuffer: finalConfig.preserveDrawingBuffer,
      powerPreference: finalConfig.powerPreference,
      depth: finalConfig.depth,
      stencil: finalConfig.stencil
    });

    // 配置渲染器
    renderer.setPixelRatio(finalConfig.pixelRatio || (typeof window !== 'undefined' ? window.devicePixelRatio : 1));
    renderer.setSize(canvas.clientWidth, canvas.clientHeight);
    renderer.toneMapping = finalConfig.toneMapping || THREE.ACESFilmicToneMapping;
    renderer.toneMappingExposure = finalConfig.toneMappingExposure || 1.0;
    
    // 启用阴影
    renderer.shadowMap.enabled = true;
    renderer.shadowMap.type = THREE.PCFSoftShadowMap;

    // 启用物理正确的光照
    renderer.useLegacyLights = false;

    // 创建引擎实例
    const engineInstance: ThreeEngineInstance = {
      id: engineId,
      renderer,
      canvas,
      config: finalConfig,
      isRunning: false
    };

    // 存储引擎实例
    this.engines.set(engineId, engineInstance);

    // 调试信息
    if (finalConfig.debugMode) {
      console.log(`[ThreeEngine] 引擎创建成功: ${engineId}`, {
        canvas: canvas.id || 'unnamed',
        size: `${canvas.clientWidth}x${canvas.clientHeight}`,
        pixelRatio: renderer.getPixelRatio(),
        config: finalConfig
      });
    }

    return renderer;
  }

  /**
   * 获取指定引擎实例
   */
  public getEngine(engineId: string = 'default'): ThreeEngineInstance | undefined {
    return this.engines.get(engineId);
  }

  /**
   * 获取指定引擎的渲染器
   */
  public getRenderer(engineId: string = 'default'): THREE.WebGLRenderer | undefined {
    const engine = this.engines.get(engineId);
    return engine?.renderer;
  }

  /**
   * 启动引擎渲染循环
   */
  public startEngine(engineId: string = 'default'): boolean {
    const engine = this.engines.get(engineId);
    if (!engine) {
      console.error(`[ThreeEngine] 引擎不存在: ${engineId}`);
      return false;
    }

    if (engine.isRunning) {
      console.warn(`[ThreeEngine] 引擎已在运行: ${engineId}`);
      return true;
    }

    engine.isRunning = true;
    
    if (engine.config.debugMode) {
      console.log(`[ThreeEngine] 引擎启动: ${engineId}`);
    }

    return true;
  }

  /**
   * 停止引擎渲染循环
   */
  public stopEngine(engineId: string = 'default'): boolean {
    const engine = this.engines.get(engineId);
    if (!engine) {
      console.error(`[ThreeEngine] 引擎不存在: ${engineId}`);
      return false;
    }

    if (!engine.isRunning) {
      console.warn(`[ThreeEngine] 引擎未在运行: ${engineId}`);
      return true;
    }

    engine.isRunning = false;
    
    if (engine.animationId) {
      cancelAnimationFrame(engine.animationId);
      engine.animationId = undefined;
    }

    if (engine.config.debugMode) {
      console.log(`[ThreeEngine] 引擎停止: ${engineId}`);
    }

    return true;
  }

  /**
   * 调整引擎渲染尺寸
   */
  public resizeEngine(engineId: string = 'default', width?: number, height?: number): boolean {
    const engine = this.engines.get(engineId);
    if (!engine) {
      console.error(`[ThreeEngine] 引擎不存在: ${engineId}`);
      return false;
    }

    const canvas = engine.canvas;
    const newWidth = width || canvas.clientWidth;
    const newHeight = height || canvas.clientHeight;

    engine.renderer.setSize(newWidth, newHeight);

    if (engine.config.debugMode) {
      console.log(`[ThreeEngine] 引擎尺寸调整: ${engineId}`, `${newWidth}x${newHeight}`);
    }

    return true;
  }

  /**
   * 销毁指定引擎
   */
  public destroyEngine(engineId: string = 'default'): boolean {
    const engine = this.engines.get(engineId);
    if (!engine) {
      console.warn(`[ThreeEngine] 引擎不存在，无需销毁: ${engineId}`);
      return true;
    }

    // 停止渲染循环
    this.stopEngine(engineId);

    // 销毁渲染器
    engine.renderer.dispose();

    // 从映射中移除
    this.engines.delete(engineId);

    if (engine.config.debugMode) {
      console.log(`[ThreeEngine] 引擎销毁: ${engineId}`);
    }

    return true;
  }

  /**
   * 销毁所有引擎
   */
  public destroyAllEngines(): void {
    const engineIds = Array.from(this.engines.keys());
    engineIds.forEach(id => this.destroyEngine(id));
    
    console.log(`[ThreeEngine] 所有引擎已销毁，共 ${engineIds.length} 个`);
  }

  /**
   * 获取引擎统计信息
   */
  public getEngineStats(): {
    totalEngines: number;
    runningEngines: number;
    engines: Array<{
      id: string;
      isRunning: boolean;
      canvasSize: string;
      pixelRatio: number;
    }>;
  } {
    const engines = Array.from(this.engines.values());
    
    return {
      totalEngines: engines.length,
      runningEngines: engines.filter(e => e.isRunning).length,
      engines: engines.map(e => ({
        id: e.id,
        isRunning: e.isRunning,
        canvasSize: `${e.canvas.clientWidth}x${e.canvas.clientHeight}`,
        pixelRatio: e.renderer.getPixelRatio()
      }))
    };
  }
}

// 导出单例实例
export const threeEngineManager = ThreeEngineManager.getInstance();
