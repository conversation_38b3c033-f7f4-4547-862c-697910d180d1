/**
 * BaseThreeScene - Three.js 基础游戏场景类
 * 提供标准化的场景初始化、生命周期管理和通用功能
 * 基于Three.js 0.178.0 API实现
 */

import * as THREE from 'three';
import { ThreeUtils, CameraOptions, LightingOptions } from './ThreeUtils';
import { threeEngineManager } from './ThreeEngine';

export interface SceneConfig {
  name: string;
  engineId?: string;
  backgroundColor?: number;
  enableFog?: boolean;
  fogColor?: number;
  fogNear?: number;
  fogFar?: number;
  cameraConfig?: CameraOptions;
  lightingConfig?: LightingOptions;
  enableGrid?: boolean;
  enableAxes?: boolean;
  debugMode?: boolean;
}

export interface SceneState {
  initialized: boolean;
  running: boolean;
  paused: boolean;
  lastUpdateTime: number;
  frameCount: number;
}

/**
 * Three.js基础游戏场景抽象类
 * 提供场景的标准化初始化流程和生命周期管理
 */
export abstract class BaseThreeScene {
  protected scene: THREE.Scene;
  protected camera: THREE.PerspectiveCamera;
  protected renderer: THREE.WebGLRenderer;
  protected clock: THREE.Clock;
  
  protected config: SceneConfig;
  protected state: SceneState;
  
  // 光照系统
  protected lights: {
    ambientLight?: THREE.AmbientLight;
    directionalLight?: THREE.DirectionalLight;
  } = {};
  
  // 辅助对象
  protected helpers: {
    gridHelper?: THREE.GridHelper;
    axesHelper?: THREE.AxesHelper;
  } = {};

  // 事件监听器
  protected eventListeners: Map<string, EventListener> = new Map();

  constructor(canvas: HTMLCanvasElement, config: SceneConfig) {
    this.config = {
      enableFog: true,
      fogColor: 0xa0a0a0,
      fogNear: 200,
      fogFar: 1000,
      enableGrid: true,
      enableAxes: false,
      debugMode: process.env.NODE_ENV === 'development',
      ...config
    };

    this.state = {
      initialized: false,
      running: false,
      paused: false,
      lastUpdateTime: 0,
      frameCount: 0
    };

    // 初始化基础组件
    this.initializeCore(canvas);
  }

  /**
   * 初始化核心组件
   */
  private initializeCore(canvas: HTMLCanvasElement): void {
    // 创建场景
    this.scene = new THREE.Scene();
    this.scene.name = this.config.name;

    // 设置背景色
    if (this.config.backgroundColor !== undefined) {
      this.scene.background = new THREE.Color(this.config.backgroundColor);
    }

    // 设置雾效
    if (this.config.enableFog) {
      this.scene.fog = new THREE.Fog(
        this.config.fogColor || 0xa0a0a0,
        this.config.fogNear || 200,
        this.config.fogFar || 1000
      );
    }

    // 创建渲染器
    this.renderer = threeEngineManager.createEngine(
      canvas,
      this.config.engineId || this.config.name,
      {
        debugMode: this.config.debugMode
      }
    );

    // 创建时钟
    this.clock = new THREE.Clock();

    // 设置窗口调整监听
    this.setupWindowResize();
  }

  /**
   * 异步初始化场景
   */
  public async initializeScene(): Promise<void> {
    if (this.state.initialized) {
      console.warn(`[BaseThreeScene] 场景已初始化: ${this.config.name}`);
      return;
    }

    try {
      console.log(`[BaseThreeScene] 开始初始化场景: ${this.config.name}`);

      // 初始化基础组件
      await this.initializeBaseComponents();

      // 调用子类的初始化方法
      await this.initialize();

      // 创建游戏对象
      await this.createGameObjects();

      // 设置游戏逻辑
      await this.setupGameLogic();

      // 标记为已初始化
      this.state.initialized = true;

      console.log(`[BaseThreeScene] 场景初始化完成: ${this.config.name}`);
    } catch (error) {
      console.error(`[BaseThreeScene] 场景初始化失败: ${this.config.name}`, error);
      throw error;
    }
  }

  /**
   * 初始化基础组件（相机、光照等）
   */
  private async initializeBaseComponents(): Promise<void> {
    // 创建相机
    this.camera = ThreeUtils.createPerspectiveCamera(
      this.scene,
      this.config.cameraConfig
    );

    // 创建光照
    this.lights = ThreeUtils.createStandardLighting(
      this.scene,
      this.config.lightingConfig
    );

    // 创建辅助对象
    if (this.config.enableGrid) {
      this.helpers.gridHelper = ThreeUtils.createGridHelper();
      this.scene.add(this.helpers.gridHelper);
    }

    if (this.config.enableAxes) {
      this.helpers.axesHelper = ThreeUtils.createAxesHelper();
      this.scene.add(this.helpers.axesHelper);
    }
  }

  /**
   * 启动场景渲染
   */
  public start(): void {
    if (!this.state.initialized) {
      throw new Error(`场景未初始化: ${this.config.name}`);
    }

    if (this.state.running) {
      console.warn(`[BaseThreeScene] 场景已在运行: ${this.config.name}`);
      return;
    }

    this.state.running = true;
    this.state.paused = false;
    
    // 启动引擎
    threeEngineManager.startEngine(this.config.engineId || this.config.name);
    
    // 开始渲染循环
    this.startRenderLoop();

    if (this.config.debugMode) {
      console.log(`[BaseThreeScene] 场景启动: ${this.config.name}`);
    }
  }

  /**
   * 停止场景渲染
   */
  public stop(): void {
    if (!this.state.running) {
      console.warn(`[BaseThreeScene] 场景未在运行: ${this.config.name}`);
      return;
    }

    this.state.running = false;
    
    // 停止引擎
    threeEngineManager.stopEngine(this.config.engineId || this.config.name);

    if (this.config.debugMode) {
      console.log(`[BaseThreeScene] 场景停止: ${this.config.name}`);
    }
  }

  /**
   * 暂停/恢复场景
   */
  public pause(paused: boolean = true): void {
    this.state.paused = paused;
    
    if (paused) {
      this.clock.stop();
    } else {
      this.clock.start();
    }

    if (this.config.debugMode) {
      console.log(`[BaseThreeScene] 场景${paused ? '暂停' : '恢复'}: ${this.config.name}`);
    }
  }

  /**
   * 渲染循环
   */
  private startRenderLoop(): void {
    const animate = () => {
      if (!this.state.running) return;

      // 更新状态
      const currentTime = performance.now();
      const deltaTime = this.clock.getDelta();
      this.state.lastUpdateTime = currentTime;
      this.state.frameCount++;

      if (!this.state.paused) {
        // 更新场景
        this.update(deltaTime);
      }

      // 渲染场景
      this.render();

      // 继续下一帧
      requestAnimationFrame(animate);
    };

    animate();
  }

  /**
   * 渲染场景
   */
  protected render(): void {
    this.renderer.render(this.scene, this.camera);
  }

  /**
   * 设置窗口调整监听
   */
  private setupWindowResize(): void {
    const handleResize = () => {
      const canvas = this.renderer.domElement;
      const width = canvas.clientWidth;
      const height = canvas.clientHeight;

      // 更新相机宽高比
      this.camera.aspect = width / height;
      this.camera.updateProjectionMatrix();

      // 更新渲染器尺寸
      threeEngineManager.resizeEngine(
        this.config.engineId || this.config.name,
        width,
        height
      );
    };

    this.eventListeners.set('resize', handleResize);
    if (typeof window !== 'undefined') {
      window.addEventListener('resize', handleResize);
    }
  }

  /**
   * 销毁场景
   */
  public dispose(): void {
    // 停止场景
    this.stop();

    // 移除事件监听器
    this.eventListeners.forEach((listener, event) => {
      if (typeof window !== 'undefined') {
        window.removeEventListener(event, listener);
      }
    });
    this.eventListeners.clear();

    // 销毁场景对象
    ThreeUtils.disposeObject(this.scene);

    // 销毁引擎
    threeEngineManager.destroyEngine(this.config.engineId || this.config.name);

    if (this.config.debugMode) {
      console.log(`[BaseThreeScene] 场景销毁: ${this.config.name}`);
    }
  }

  // 抽象方法，子类必须实现
  protected abstract initialize(): Promise<void>;
  protected abstract createGameObjects(): Promise<void>;
  protected abstract setupGameLogic(): Promise<void>;
  protected abstract update(deltaTime: number): void;

  // Getter方法
  public getScene(): THREE.Scene { return this.scene; }
  public getCamera(): THREE.PerspectiveCamera { return this.camera; }
  public getRenderer(): THREE.WebGLRenderer { return this.renderer; }
  public getConfig(): SceneConfig { return this.config; }
  public getState(): SceneState { return this.state; }
}

export default BaseThreeScene;
