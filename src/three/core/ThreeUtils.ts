/**
 * ThreeUtils - Three.js 通用工具函数库
 * 提供标准化的相机、光照、材质、几何体等创建和管理功能
 * 基于Three.js 0.178.0 API实现
 */

import * as THREE from 'three';

export interface CameraOptions {
  name?: string;
  fov?: number;
  aspect?: number;
  near?: number;
  far?: number;
  position?: THREE.Vector3;
  target?: THREE.Vector3;
  enableControls?: boolean;
}

export interface LightingOptions {
  ambientColor?: number;
  ambientIntensity?: number;
  directionalColor?: number;
  directionalIntensity?: number;
  directionalPosition?: THREE.Vector3;
  enableShadows?: boolean;
}

export interface MaterialOptions {
  color?: number;
  metalness?: number;
  roughness?: number;
  transparent?: boolean;
  opacity?: number;
  map?: THREE.Texture;
  normalMap?: THREE.Texture;
  roughnessMap?: THREE.Texture;
  metalnessMap?: THREE.Texture;
}

/**
 * Three.js工具函数类
 * 提供常用的3D对象创建和管理功能
 */
export class ThreeUtils {
  
  /**
   * 创建标准透视相机
   */
  static createPerspectiveCamera(
    scene: THREE.Scene,
    options: CameraOptions = {}
  ): THREE.PerspectiveCamera {
    const {
      name = 'MainCamera',
      fov = 75,
      aspect = typeof window !== 'undefined' ? window.innerWidth / window.innerHeight : 16/9,
      near = 0.1,
      far = 1000,
      position = new THREE.Vector3(0, 5, 10),
      target = new THREE.Vector3(0, 0, 0)
    } = options;

    const camera = new THREE.PerspectiveCamera(fov, aspect, near, far);
    camera.name = name;
    camera.position.copy(position);
    camera.lookAt(target);

    return camera;
  }

  /**
   * 创建标准光照系统
   */
  static createStandardLighting(
    scene: THREE.Scene,
    options: LightingOptions = {}
  ): {
    ambientLight: THREE.AmbientLight;
    directionalLight: THREE.DirectionalLight;
  } {
    const {
      ambientColor = 0x404040,
      ambientIntensity = 0.4,
      directionalColor = 0xffffff,
      directionalIntensity = 1,
      directionalPosition = new THREE.Vector3(10, 10, 5),
      enableShadows = true
    } = options;

    // 环境光
    const ambientLight = new THREE.AmbientLight(ambientColor, ambientIntensity);
    ambientLight.name = 'AmbientLight';
    scene.add(ambientLight);

    // 方向光
    const directionalLight = new THREE.DirectionalLight(directionalColor, directionalIntensity);
    directionalLight.name = 'DirectionalLight';
    directionalLight.position.copy(directionalPosition);
    directionalLight.castShadow = enableShadows;

    if (enableShadows) {
      // 配置阴影
      directionalLight.shadow.mapSize.width = 2048;
      directionalLight.shadow.mapSize.height = 2048;
      directionalLight.shadow.camera.near = 0.5;
      directionalLight.shadow.camera.far = 500;
      directionalLight.shadow.camera.left = -50;
      directionalLight.shadow.camera.right = 50;
      directionalLight.shadow.camera.top = 50;
      directionalLight.shadow.camera.bottom = -50;
    }

    scene.add(directionalLight);

    return {
      ambientLight,
      directionalLight
    };
  }

  /**
   * 创建标准PBR材质
   */
  static createStandardMaterial(options: MaterialOptions = {}): THREE.MeshStandardMaterial {
    const {
      color = 0xffffff,
      metalness = 0.0,
      roughness = 0.5,
      transparent = false,
      opacity = 1.0,
      map,
      normalMap,
      roughnessMap,
      metalnessMap
    } = options;

    const material = new THREE.MeshStandardMaterial({
      color,
      metalness,
      roughness,
      transparent,
      opacity,
      map,
      normalMap,
      roughnessMap,
      metalnessMap
    });

    return material;
  }

  /**
   * 创建基础几何体
   */
  static createBasicGeometries() {
    return {
      box: (width = 1, height = 1, depth = 1) => 
        new THREE.BoxGeometry(width, height, depth),
      
      sphere: (radius = 1, widthSegments = 32, heightSegments = 16) => 
        new THREE.SphereGeometry(radius, widthSegments, heightSegments),
      
      plane: (width = 1, height = 1, widthSegments = 1, heightSegments = 1) => 
        new THREE.PlaneGeometry(width, height, widthSegments, heightSegments),
      
      cylinder: (radiusTop = 1, radiusBottom = 1, height = 1, radialSegments = 8) => 
        new THREE.CylinderGeometry(radiusTop, radiusBottom, height, radialSegments),
      
      cone: (radius = 1, height = 1, radialSegments = 8) => 
        new THREE.ConeGeometry(radius, height, radialSegments)
    };
  }

  /**
   * 创建网格对象
   */
  static createMesh(
    geometry: THREE.BufferGeometry,
    material: THREE.Material,
    options: {
      name?: string;
      position?: THREE.Vector3;
      rotation?: THREE.Euler;
      scale?: THREE.Vector3;
      castShadow?: boolean;
      receiveShadow?: boolean;
    } = {}
  ): THREE.Mesh {
    const {
      name = 'Mesh',
      position = new THREE.Vector3(0, 0, 0),
      rotation = new THREE.Euler(0, 0, 0),
      scale = new THREE.Vector3(1, 1, 1),
      castShadow = true,
      receiveShadow = true
    } = options;

    const mesh = new THREE.Mesh(geometry, material);
    mesh.name = name;
    mesh.position.copy(position);
    mesh.rotation.copy(rotation);
    mesh.scale.copy(scale);
    mesh.castShadow = castShadow;
    mesh.receiveShadow = receiveShadow;

    return mesh;
  }

  /**
   * 创建地面
   */
  static createGround(
    size: number = 100,
    color: number = 0x808080,
    receiveShadow: boolean = true
  ): THREE.Mesh {
    const geometry = new THREE.PlaneGeometry(size, size);
    const material = new THREE.MeshStandardMaterial({ color });
    
    const ground = new THREE.Mesh(geometry, material);
    ground.name = 'Ground';
    ground.rotation.x = -Math.PI / 2;
    ground.receiveShadow = receiveShadow;

    return ground;
  }

  /**
   * 创建天空盒
   */
  static createSkybox(
    scene: THREE.Scene,
    color: number = 0x87CEEB
  ): void {
    scene.background = new THREE.Color(color);
    scene.fog = new THREE.Fog(color, 100, 1000);
  }

  /**
   * 创建网格辅助线
   */
  static createGridHelper(
    size: number = 100,
    divisions: number = 10,
    colorCenterLine: number = 0x444444,
    colorGrid: number = 0x888888
  ): THREE.GridHelper {
    return new THREE.GridHelper(size, divisions, colorCenterLine, colorGrid);
  }

  /**
   * 创建坐标轴辅助线
   */
  static createAxesHelper(size: number = 5): THREE.AxesHelper {
    return new THREE.AxesHelper(size);
  }

  /**
   * 计算对象边界盒
   */
  static getBoundingBox(object: THREE.Object3D): THREE.Box3 {
    const box = new THREE.Box3();
    box.setFromObject(object);
    return box;
  }

  /**
   * 计算对象中心点
   */
  static getCenter(object: THREE.Object3D): THREE.Vector3 {
    const box = this.getBoundingBox(object);
    const center = new THREE.Vector3();
    box.getCenter(center);
    return center;
  }

  /**
   * 计算对象尺寸
   */
  static getSize(object: THREE.Object3D): THREE.Vector3 {
    const box = this.getBoundingBox(object);
    const size = new THREE.Vector3();
    box.getSize(size);
    return size;
  }

  /**
   * 将对象居中
   */
  static centerObject(object: THREE.Object3D): void {
    const center = this.getCenter(object);
    object.position.sub(center);
  }

  /**
   * 缩放对象到指定尺寸
   */
  static scaleToSize(object: THREE.Object3D, targetSize: number): void {
    const size = this.getSize(object);
    const maxDimension = Math.max(size.x, size.y, size.z);
    const scale = targetSize / maxDimension;
    object.scale.multiplyScalar(scale);
  }

  /**
   * 创建粒子系统
   */
  static createParticleSystem(
    count: number = 1000,
    options: {
      color?: number;
      size?: number;
      transparent?: boolean;
      opacity?: number;
    } = {}
  ): THREE.Points {
    const {
      color = 0xffffff,
      size = 1,
      transparent = true,
      opacity = 0.8
    } = options;

    const geometry = new THREE.BufferGeometry();
    const positions = new Float32Array(count * 3);

    for (let i = 0; i < count * 3; i++) {
      positions[i] = (Math.random() - 0.5) * 100;
    }

    geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));

    const material = new THREE.PointsMaterial({
      color,
      size,
      transparent,
      opacity
    });

    return new THREE.Points(geometry, material);
  }

  /**
   * 销毁对象及其资源
   */
  static disposeObject(object: THREE.Object3D): void {
    object.traverse((child) => {
      if (child instanceof THREE.Mesh) {
        if (child.geometry) {
          child.geometry.dispose();
        }
        if (child.material) {
          if (Array.isArray(child.material)) {
            child.material.forEach(material => material.dispose());
          } else {
            child.material.dispose();
          }
        }
      }
    });
  }

  /**
   * 深度克隆对象
   */
  static cloneObject(object: THREE.Object3D): THREE.Object3D {
    return object.clone(true);
  }
}

export default ThreeUtils;
