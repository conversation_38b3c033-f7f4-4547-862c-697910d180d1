/**
 * ThreeCanvas - Three.js React Canvas组件
 * 提供标准化的Three.js渲染画布和生命周期管理
 * 基于Three.js 0.178.0和React 19实现
 */

'use client';

import React, { useRef, useEffect, useState, useCallback } from 'react';
import * as THREE from 'three';
import { threeEngineManager } from '../core/ThreeEngine';
import { ThreeUtils } from '../core/ThreeUtils';

export interface ThreeCanvasProps {
  /** Canvas样式类名 */
  className?: string;
  /** Canvas样式 */
  style?: React.CSSProperties;
  /** 引擎ID */
  engineId?: string;
  /** 是否启用调试模式 */
  debugMode?: boolean;
  /** 是否自动启动 */
  autoStart?: boolean;
  /** 背景颜色 */
  backgroundColor?: number;
  /** 是否启用阴影 */
  enableShadows?: boolean;
  /** 是否启用雾效 */
  enableFog?: boolean;
  /** 初始化完成回调 */
  onInitialized?: (context: ThreeCanvasContext) => void;
  /** 渲染循环回调 */
  onRender?: (context: ThreeCanvasContext, deltaTime: number) => void;
  /** 错误回调 */
  onError?: (error: Error) => void;
  /** 子组件 */
  children?: React.ReactNode;
}

export interface ThreeCanvasContext {
  scene: THREE.Scene;
  camera: THREE.PerspectiveCamera;
  renderer: THREE.WebGLRenderer;
  clock: THREE.Clock;
  engineId: string;
}

/**
 * Three.js Canvas组件
 * 提供标准化的Three.js渲染环境
 */
export const ThreeCanvas: React.FC<ThreeCanvasProps> = ({
  className = '',
  style = {},
  engineId = 'default',
  debugMode = process.env.NODE_ENV === 'development',
  autoStart = true,
  backgroundColor = 0xa0a0a0,
  enableShadows = true,
  enableFog = true,
  onInitialized,
  onRender,
  onError,
  children
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const contextRef = useRef<ThreeCanvasContext | null>(null);
  const animationIdRef = useRef<number | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  /**
   * 初始化Three.js场景
   */
  const initializeThreeJS = useCallback(async () => {
    if (!canvasRef.current) return;

    try {
      const canvas = canvasRef.current;

      // 创建渲染器
      const renderer = threeEngineManager.createEngine(canvas, engineId, {
        debugMode,
        antialias: true,
        alpha: false
      });

      // 创建场景
      const scene = new THREE.Scene();
      scene.background = new THREE.Color(backgroundColor);

      // 设置雾效
      if (enableFog) {
        scene.fog = new THREE.Fog(backgroundColor, 200, 1000);
      }

      // 创建相机
      const camera = ThreeUtils.createPerspectiveCamera(scene, {
        fov: 75,
        aspect: canvas.clientWidth / canvas.clientHeight,
        near: 0.1,
        far: 1000,
        position: new THREE.Vector3(0, 5, 10)
      });

      // 创建基础光照
      ThreeUtils.createStandardLighting(scene, {
        ambientIntensity: 0.4,
        directionalIntensity: 1,
        enableShadows
      });

      // 创建时钟
      const clock = new THREE.Clock();

      // 创建上下文
      const context: ThreeCanvasContext = {
        scene,
        camera,
        renderer,
        clock,
        engineId
      };

      contextRef.current = context;
      setIsInitialized(true);

      // 调用初始化回调
      if (onInitialized) {
        onInitialized(context);
      }

      if (debugMode) {
        console.log(`[ThreeCanvas] 初始化完成: ${engineId}`);
      }

    } catch (err) {
      const error = err instanceof Error ? err : new Error('Three.js初始化失败');
      setError(error);
      if (onError) {
        onError(error);
      }
      console.error('[ThreeCanvas] 初始化失败:', error);
    }
  }, [engineId, debugMode, backgroundColor, enableFog, enableShadows, onInitialized, onError]);

  /**
   * 渲染循环
   */
  const startRenderLoop = useCallback(() => {
    if (!contextRef.current) return;

    const animate = () => {
      if (!contextRef.current) return;

      const { scene, camera, renderer, clock } = contextRef.current;
      const deltaTime = clock.getDelta();

      // 调用渲染回调
      if (onRender) {
        onRender(contextRef.current, deltaTime);
      }

      // 渲染场景
      renderer.render(scene, camera);

      // 继续下一帧
      animationIdRef.current = requestAnimationFrame(animate);
    };

    animate();
  }, [onRender]);

  /**
   * 停止渲染循环
   */
  const stopRenderLoop = useCallback(() => {
    if (animationIdRef.current) {
      cancelAnimationFrame(animationIdRef.current);
      animationIdRef.current = null;
    }
  }, []);

  /**
   * 处理窗口大小调整
   */
  const handleResize = useCallback(() => {
    if (!contextRef.current || !canvasRef.current) return;

    const { camera, renderer } = contextRef.current;
    const canvas = canvasRef.current;

    const width = canvas.clientWidth;
    const height = canvas.clientHeight;

    // 更新相机宽高比
    camera.aspect = width / height;
    camera.updateProjectionMatrix();

    // 更新渲染器尺寸
    renderer.setSize(width, height);

    if (debugMode) {
      console.log(`[ThreeCanvas] 尺寸调整: ${width}x${height}`);
    }
  }, [debugMode]);

  /**
   * 组件挂载时初始化
   */
  useEffect(() => {
    initializeThreeJS();

    return () => {
      // 清理资源
      stopRenderLoop();
      if (contextRef.current) {
        threeEngineManager.destroyEngine(engineId);
        contextRef.current = null;
      }
    };
  }, [initializeThreeJS, stopRenderLoop, engineId]);

  /**
   * 自动启动渲染循环
   */
  useEffect(() => {
    if (isInitialized && autoStart) {
      startRenderLoop();
    }

    return () => {
      if (!autoStart) {
        stopRenderLoop();
      }
    };
  }, [isInitialized, autoStart, startRenderLoop, stopRenderLoop]);

  /**
   * 监听窗口大小变化
   */
  useEffect(() => {
    if (typeof window !== 'undefined') {
      window.addEventListener('resize', handleResize);
      return () => {
        window.removeEventListener('resize', handleResize);
      };
    }
  }, [handleResize]);

  /**
   * 获取Three.js上下文
   */
  const getContext = useCallback((): ThreeCanvasContext | null => {
    return contextRef.current;
  }, []);

  /**
   * 手动启动渲染
   */
  const startRendering = useCallback(() => {
    if (isInitialized) {
      startRenderLoop();
    }
  }, [isInitialized, startRenderLoop]);

  /**
   * 手动停止渲染
   */
  const stopRendering = useCallback(() => {
    stopRenderLoop();
  }, [stopRenderLoop]);

  // 如果有错误，显示错误信息
  if (error) {
    return (
      <div 
        className={`three-canvas-error ${className}`}
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: '#f5f5f5',
          color: '#d32f2f',
          padding: '20px',
          ...style
        }}
      >
        <div>
          <h3>Three.js 初始化失败</h3>
          <p>{error.message}</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`three-canvas-container ${className}`} style={style}>
      <canvas
        ref={canvasRef}
        className="three-canvas"
        style={{
          width: '100%',
          height: '100%',
          display: 'block'
        }}
      />
      {children && (
        <div className="three-canvas-overlay">
          {React.Children.map(children, child => {
            if (React.isValidElement(child)) {
              return React.cloneElement(child, {
                // @ts-ignore
                threeContext: contextRef.current,
                getContext,
                startRendering,
                stopRendering
              });
            }
            return child;
          })}
        </div>
      )}
    </div>
  );
};

export default ThreeCanvas;
