import { StateGraph, Annotation, START, END, MemorySaver } from "@langchain/langgraph";
import { BaseMessage, HumanMessage, AIMessage } from "@langchain/core/messages";
// import { GameDesignAgent, GameDesign } from "./GameDesignAgent"; // 移除设计师Agent
import { CodeGenerationAgent, GeneratedCode } from "./CodeGenerationAgent";

// 协调器状态
export const CoordinatorStateAnnotation = Annotation.Root({
  messages: Annotation<BaseMessage[]>({
    reducer: (x, y) => x.concat(y),
    default: () => []
  }),
  currentPhase: Annotation<"code" | "review" | "complete" | "error">({ // 移除design阶段
    reducer: (x, y) => y ?? x,
    default: () => "code"
  }),
  // gameDesign: Annotation<GameDesign | null>({ // 移除游戏设计状态
  //   reducer: (x, y) => y ?? x,
  //   default: () => null
  // }),
  generatedCode: Annotation<GeneratedCode | null>({
    reducer: (x, y) => y ?? x,
    default: () => null
  }),
  userRequirement: Annotation<string>({
    reducer: (x, y) => y ?? x,
    default: () => ""
  }),
  progress: Annotation<number>({
    reducer: (x, y) => y ?? x,
    default: () => 0
  }),
  errors: Annotation<string[]>({
    reducer: (x, y) => [...x, ...y],
    default: () => []
  })
});

export type CoordinatorState = typeof CoordinatorStateAnnotation.State;

// 工作流程结果
export interface WorkflowResult {
  success: boolean;
  // gameDesign?: GameDesign; // 移除游戏设计
  generatedCode?: GeneratedCode;
  errors: string[];
  progress: number;
}

export class AgentCoordinator {
  private workflow: any;
  private memory: MemorySaver;
  // private gameDesignAgent: GameDesignAgent; // 移除设计师Agent
  private codeGenerationAgent: CodeGenerationAgent;

  constructor() {
    this.memory = new MemorySaver();
    // this.gameDesignAgent = new GameDesignAgent(); // 移除设计师Agent初始化
    this.codeGenerationAgent = new CodeGenerationAgent();
    this.initializeWorkflow();
  }

  // 初始化工作流程
  private initializeWorkflow(): void {
    const workflow = new StateGraph(CoordinatorStateAnnotation)
      .addNode("start", this.startNode.bind(this))
      // .addNode("design", this.designNode.bind(this)) // 移除设计节点
      .addNode("code", this.codeNode.bind(this))
      .addNode("review", this.reviewNode.bind(this))
      .addNode("complete", this.completeNode.bind(this))
      .addEdge(START, "start")
      .addConditionalEdges("start", this.routeFromStart.bind(this))
      // .addConditionalEdges("design", this.routeFromDesign.bind(this)) // 移除设计路由
      .addConditionalEdges("code", this.routeFromCode.bind(this))
      .addConditionalEdges("review", this.routeFromReview.bind(this))
      .addEdge("complete", END);

    this.workflow = workflow.compile({
      checkpointer: this.memory
    });
  }

  // 开始节点
  private async startNode(state: CoordinatorState): Promise<Partial<CoordinatorState>> {
    const lastMessage = state.messages[state.messages.length - 1];
    const userInput = lastMessage?.content as string || "";

    return {
      userRequirement: userInput,
      currentPhase: "code", // 直接进入代码生成阶段
      progress: 10,
      messages: [new AIMessage({ 
        content: "开始游戏制作流程...\n直接生成游戏代码" 
      })]
    };
  }

  // 移除设计节点
  // private async designNode(state: CoordinatorState): Promise<Partial<CoordinatorState>> {
  //   // 移除设计逻辑
  // }

  // 代码生成节点 - 直接处理用户需求
  private async codeNode(state: CoordinatorState): Promise<Partial<CoordinatorState>> {
    try {
      const threadId = `code_${Date.now()}`;
      
      // 直接使用用户需求作为输入
      const result = await this.codeGenerationAgent.run(state.userRequirement, { threadId });
      
      if (result.status === "completed" && result.context.generatedCode) {
        return {
          generatedCode: result.context.generatedCode,
          progress: 80,
          currentPhase: "review",
          messages: [new AIMessage({ 
            content: "代码生成完成！\n检查和优化中..." 
          })]
        };
      } else {
        throw new Error("代码生成失败");
      }
    } catch (error) {
      return {
        currentPhase: "error",
        errors: [error instanceof Error ? error.message : "代码生成阶段未知错误"],
        messages: [new AIMessage({ 
          content: `代码生成阶段出错: ${error instanceof Error ? error.message : "未知错误"}` 
        })]
      };
    }
  }

  // 审查节点
  private async reviewNode(state: CoordinatorState): Promise<Partial<CoordinatorState>> {
    try {
      // 简单的代码审查逻辑
      const issues = this.performCodeReview(state.generatedCode);
      
      if (issues.length === 0) {
        return {
          currentPhase: "complete",
          progress: 100,
          messages: [new AIMessage({ 
            content: "代码审查通过！游戏制作完成。" 
          })]
        };
      } else {
        return {
          progress: 90,
          messages: [new AIMessage({ 
            content: `发现 ${issues.length} 个问题，但游戏基本可用：\n${issues.join('\n')}` 
          })],
          currentPhase: "complete"
        };
      }
    } catch (error) {
      return {
        currentPhase: "error",
        errors: [error instanceof Error ? error.message : "审查阶段未知错误"],
        messages: [new AIMessage({ 
          content: `审查阶段出错: ${error instanceof Error ? error.message : "未知错误"}` 
        })]
      };
    }
  }

  // 完成节点
  private async completeNode(_state: CoordinatorState): Promise<Partial<CoordinatorState>> {
    return {
      progress: 100,
      messages: [new AIMessage({ 
        content: "🎉 游戏制作完成！\n\n您的游戏已经准备就绪，可以开始游玩了！" 
      })]
    };
  }

  // 路由决策方法
  private routeFromStart(state: CoordinatorState): "code" | typeof END {
    return state.currentPhase === "error" ? END : "code";
  }

  // private routeFromDesign(state: CoordinatorState): "code" | typeof END { // 移除设计路由
  //   if (state.currentPhase === "error" || !state.gameDesign) {
  //     return END;
  //   }
  //   return "code";
  // }

  private routeFromCode(state: CoordinatorState): "review" | typeof END {
    if (state.currentPhase === "error" || !state.generatedCode) {
      return END;
    }
    return "review";
  }

  private routeFromReview(state: CoordinatorState): "complete" | typeof END {
    return state.currentPhase === "complete" ? "complete" : END;
  }

  // 执行代码审查
  private performCodeReview(code: GeneratedCode | null): string[] {
    const issues: string[] = [];
    
    if (!code) {
      issues.push("缺少生成的代码");
      return issues;
    }

    if (!code.component || code.component.length < 100) {
      issues.push("组件代码过短或缺失");
    }

    if (!code.fileName) {
      issues.push("缺少文件名");
    }

    if (code.dependencies.length === 0) {
      issues.push("缺少必要的依赖项");
    }

    return issues;
  }

  // 公共方法：运行完整工作流程
  public async runWorkflow(userInput: string): Promise<WorkflowResult> {
    try {
      const threadId = `workflow_${Date.now()}`;
      
      const initialState: Partial<CoordinatorState> = {
        messages: [new HumanMessage({ content: userInput })]
      };

      const result = await this.workflow.invoke(initialState, {
        configurable: { thread_id: threadId }
      });

      return {
        success: result.currentPhase === "complete",
        // gameDesign: result.gameDesign, // 移除游戏设计
        generatedCode: result.generatedCode,
        errors: result.errors || [],
        progress: result.progress || 0
      };
    } catch (error) {
      return {
        success: false,
        errors: [error instanceof Error ? error.message : "工作流程执行失败"],
        progress: 0
      };
    }
  }

  // 获取工作流程状态
  public async getWorkflowState(threadId: string): Promise<CoordinatorState | null> {
    try {
      const state = await this.workflow.getState({
        configurable: { thread_id: threadId }
      });
      return state?.values || null;
    } catch (error) {
      console.error("获取工作流程状态失败:", error);
      return null;
    }
  }
}
