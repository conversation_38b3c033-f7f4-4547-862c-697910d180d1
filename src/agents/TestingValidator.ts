/**
 * TestingValidator - 测试与验证系统
 * 创建自动化测试系统，验证AI生成优化的效果，包括准确性测试和性能基准测试
 */

import { GameDesign } from './GameDesignAgent';
import { GeneratedCode } from './CodeGenerationAgent';
import { AccuracyOptimizer, ValidationResult } from './AccuracyOptimizer';
import { EfficiencyOptimizer, PerformanceMetrics } from './EfficiencyOptimizer';

// 测试用例接口
export interface TestCase {
  id: string;
  name: string;
  description: string;
  input: string;
  expectedOutput?: Partial<GameDesign>;
  category: 'accuracy' | 'performance' | 'integration' | 'regression';
  priority: 'low' | 'medium' | 'high' | 'critical';
}

// 测试结果接口
export interface TestResult {
  testId: string;
  passed: boolean;
  score: number;
  executionTime: number;
  errors: string[];
  warnings: string[];
  metrics: {
    tokensUsed: number;
    cacheHit: boolean;
    responseTime: number;
    qualityScore: number;
  };
}

// 基准测试结果接口
export interface BenchmarkResult {
  testSuite: string;
  totalTests: number;
  passedTests: number;
  failedTests: number;
  averageScore: number;
  averageExecutionTime: number;
  totalTokensUsed: number;
  cacheHitRate: number;
  performanceMetrics: PerformanceMetrics;
  regressionDetected: boolean;
}

/**
 * 测试验证器类
 */
export class TestingValidator {
  private static readonly PERFORMANCE_THRESHOLD = 5000; // 5秒
  private static readonly QUALITY_THRESHOLD = 75; // 75分
  private static readonly TOKEN_LIMIT = 20000; // Token限制

  // 预定义测试用例
  private static readonly TEST_CASES: TestCase[] = [
    {
      id: 'accuracy_001',
      name: '基础动作游戏设计',
      description: '测试基础动作游戏的设计生成准确性',
      input: '创建一个简单的3D动作游戏，玩家控制角色在场景中移动和攻击敌人',
      expectedOutput: {
        gameType: 'action',
        technicalComponents: {
          camera: { type: 'THIRD_PERSON' }
        }
      },
      category: 'accuracy',
      priority: 'high'
    },
    {
      id: 'accuracy_002',
      name: '策略游戏设计',
      description: '测试策略游戏的设计生成准确性',
      input: '设计一个塔防游戏，玩家需要建造防御塔来阻止敌人',
      expectedOutput: {
        gameType: 'strategy',
        technicalComponents: {
          camera: { type: 'TOP_DOWN' }
        }
      },
      category: 'accuracy',
      priority: 'high'
    },
    {
      id: 'performance_001',
      name: '响应时间测试',
      description: '测试AI生成的响应时间是否在可接受范围内',
      input: '创建一个休闲益智游戏',
      category: 'performance',
      priority: 'medium'
    },
    {
      id: 'performance_002',
      name: 'Token使用效率测试',
      description: '测试Token使用是否高效',
      input: '生成一个简单的跑酷游戏',
      category: 'performance',
      priority: 'medium'
    },
    {
      id: 'integration_001',
      name: '端到端集成测试',
      description: '测试从设计到代码生成的完整流程',
      input: '创建一个第一人称射击游戏',
      category: 'integration',
      priority: 'critical'
    }
  ];

  /**
   * 运行单个测试用例
   */
  public static async runTestCase(testCase: TestCase): Promise<TestResult> {
    const startTime = Date.now();
    const errors: string[] = [];
    const warnings: string[] = [];
    let score = 0;
    let tokensUsed = 0;
    let cacheHit = false;
    let qualityScore = 0;

    try {
      // 检查缓存
      const cachedDesign = EfficiencyOptimizer.checkDesignCache(testCase.input);
      cacheHit = cachedDesign !== null;

      let design: GameDesign;
      if (cachedDesign) {
        design = cachedDesign;
      } else {
        // 模拟AI生成（实际应该调用GameDesignAgent）
        design = await this.simulateDesignGeneration(testCase.input);
        tokensUsed = 5000; // 模拟token使用
        EfficiencyOptimizer.cacheDesign(testCase.input, design, tokensUsed);
      }

      // 验证设计准确性
      const validation = AccuracyOptimizer.validateGameDesign(design);
      qualityScore = validation.score;

      // 检查预期输出
      if (testCase.expectedOutput) {
        const matches = this.compareDesignWithExpected(design, testCase.expectedOutput);
        if (!matches) {
          errors.push('生成的设计与预期输出不匹配');
        }
      }

      // 计算测试分数
      score = this.calculateTestScore(validation, cacheHit, tokensUsed, testCase.category);

      // 记录警告
      if (validation.warnings.length > 0) {
        warnings.push(...validation.warnings.map(w => w.message));
      }

    } catch (error) {
      errors.push(`测试执行失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }

    const executionTime = Date.now() - startTime;

    return {
      testId: testCase.id,
      passed: errors.length === 0 && score >= this.QUALITY_THRESHOLD,
      score,
      executionTime,
      errors,
      warnings,
      metrics: {
        tokensUsed,
        cacheHit,
        responseTime: executionTime,
        qualityScore
      }
    };
  }

  /**
   * 运行测试套件
   */
  public static async runTestSuite(category?: TestCase['category']): Promise<BenchmarkResult> {
    const testCases = category 
      ? this.TEST_CASES.filter(tc => tc.category === category)
      : this.TEST_CASES;

    const results: TestResult[] = [];
    let totalTokensUsed = 0;
    let cacheHits = 0;

    console.log(`[TestingValidator] 开始运行测试套件，共${testCases.length}个测试用例`);

    for (const testCase of testCases) {
      console.log(`[TestingValidator] 运行测试: ${testCase.name}`);
      const result = await this.runTestCase(testCase);
      results.push(result);
      
      totalTokensUsed += result.metrics.tokensUsed;
      if (result.metrics.cacheHit) {
        cacheHits++;
      }
    }

    const passedTests = results.filter(r => r.passed).length;
    const failedTests = results.length - passedTests;
    const averageScore = results.reduce((sum, r) => sum + r.score, 0) / results.length;
    const averageExecutionTime = results.reduce((sum, r) => sum + r.executionTime, 0) / results.length;
    const cacheHitRate = (cacheHits / results.length) * 100;

    // 获取性能指标
    const performanceMetrics = EfficiencyOptimizer.getPerformanceMetrics();

    // 检测回归
    const regressionDetected = this.detectRegression(results);

    const benchmarkResult: BenchmarkResult = {
      testSuite: category || 'all',
      totalTests: results.length,
      passedTests,
      failedTests,
      averageScore,
      averageExecutionTime,
      totalTokensUsed,
      cacheHitRate,
      performanceMetrics,
      regressionDetected
    };

    console.log(`[TestingValidator] 测试套件完成:`, benchmarkResult);
    return benchmarkResult;
  }

  /**
   * 模拟设计生成（实际应该调用真实的Agent）
   */
  private static async simulateDesignGeneration(input: string): Promise<GameDesign> {
    // 模拟延迟
    await new Promise(resolve => setTimeout(resolve, 1000));

    // 基于输入生成模拟设计
    const gameType = this.inferGameType(input);
    
    return {
      gameType,
      theme: '模拟主题',
      mechanics: ['移动', '交互'],
      objectives: ['完成任务'],
      difficulty: 'medium',
      estimatedPlayTime: '10-15分钟',
      targetAudience: '一般用户',
      visualStyle: '现代风格',
      controls: {
        primary: ['触屏'],
        secondary: ['键盘']
      },
      features: ['基础功能']
    };
  }

  /**
   * 推断游戏类型
   */
  private static inferGameType(input: string): string {
    const lowerInput = input.toLowerCase();
    
    if (lowerInput.includes('动作') || lowerInput.includes('action')) return 'action';
    if (lowerInput.includes('策略') || lowerInput.includes('塔防')) return 'strategy';
    if (lowerInput.includes('益智') || lowerInput.includes('puzzle')) return 'puzzle';
    if (lowerInput.includes('射击') || lowerInput.includes('fps')) return 'shooter';
    if (lowerInput.includes('跑酷') || lowerInput.includes('platformer')) return 'platformer';
    
    return 'casual';
  }

  /**
   * 比较设计与预期输出
   */
  private static compareDesignWithExpected(design: GameDesign, expected: Partial<GameDesign>): boolean {
    for (const [key, value] of Object.entries(expected)) {
      if (key === 'technicalComponents') {
        const techComponents = (design as any).technicalComponents;
        const expectedTech = value as any;
        
        if (expectedTech.camera && techComponents?.camera?.type !== expectedTech.camera.type) {
          return false;
        }
      } else if ((design as any)[key] !== value) {
        return false;
      }
    }
    return true;
  }

  /**
   * 计算测试分数
   */
  private static calculateTestScore(
    validation: ValidationResult,
    cacheHit: boolean,
    tokensUsed: number,
    category: TestCase['category']
  ): number {
    let score = validation.score;

    // 性能加分
    if (cacheHit) {
      score += 5; // 缓存命中加分
    }

    if (tokensUsed < this.TOKEN_LIMIT * 0.5) {
      score += 5; // Token使用效率加分
    }

    // 根据测试类别调整分数
    switch (category) {
      case 'performance':
        if (tokensUsed > this.TOKEN_LIMIT) {
          score -= 20; // 性能测试中Token超限扣分
        }
        break;
      case 'accuracy':
        if (validation.errors.length > 0) {
          score -= validation.errors.length * 10; // 准确性测试中错误扣分更多
        }
        break;
    }

    return Math.max(0, Math.min(100, score));
  }

  /**
   * 检测回归
   */
  private static detectRegression(results: TestResult[]): boolean {
    // 简单的回归检测：如果有超过30%的测试失败，认为存在回归
    const failureRate = results.filter(r => !r.passed).length / results.length;
    return failureRate > 0.3;
  }

  /**
   * 生成测试报告
   */
  public static generateTestReport(benchmark: BenchmarkResult): string {
    const report = `
# AI生成优化测试报告

## 测试概览
- 测试套件: ${benchmark.testSuite}
- 总测试数: ${benchmark.totalTests}
- 通过测试: ${benchmark.passedTests}
- 失败测试: ${benchmark.failedTests}
- 通过率: ${((benchmark.passedTests / benchmark.totalTests) * 100).toFixed(1)}%

## 性能指标
- 平均分数: ${benchmark.averageScore.toFixed(1)}
- 平均执行时间: ${benchmark.averageExecutionTime.toFixed(0)}ms
- 总Token使用: ${benchmark.totalTokensUsed}
- 缓存命中率: ${benchmark.cacheHitRate.toFixed(1)}%

## 效率指标
- 平均响应时间: ${benchmark.performanceMetrics.averageResponseTime}ms
- 成功率: ${benchmark.performanceMetrics.successRate}%
- Token效率: ${benchmark.performanceMetrics.tokenEfficiency}
- 单次生成成本: $${benchmark.performanceMetrics.costPerGeneration.toFixed(4)}

## 回归检测
${benchmark.regressionDetected ? '⚠️ 检测到性能回归' : '✅ 未检测到回归'}

## 建议
${benchmark.averageScore < 80 ? '- 建议优化AI生成准确性' : ''}
${benchmark.cacheHitRate < 20 ? '- 建议优化缓存策略' : ''}
${benchmark.averageExecutionTime > 5000 ? '- 建议优化响应时间' : ''}
`;

    return report;
  }

  /**
   * 获取所有测试用例
   */
  public static getTestCases(): TestCase[] {
    return [...this.TEST_CASES];
  }

  /**
   * 添加自定义测试用例
   */
  public static addTestCase(testCase: TestCase): void {
    this.TEST_CASES.push(testCase);
  }
}
