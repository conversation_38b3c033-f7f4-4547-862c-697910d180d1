/**
 * Three.js 0.178.0 最佳实践和API指南
 * 基于最新的Three.js文档和社区最佳实践
 */

export class ThreeJSBestPractices {
  /**
   * 获取Three.js最佳实践指南
   */
  static getBestPractices(topics: string[]): string {
    const practices: Record<string, string> = {
      basic: `
## Three.js 基础设置

\`\`\`typescript
import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';

// 创建场景、相机和渲染器
const scene = new THREE.Scene();
const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
const renderer = new THREE.WebGLRenderer({ antialias: true });
renderer.setSize(window.innerWidth, window.innerHeight);
renderer.setPixelRatio(window.devicePixelRatio);
document.body.appendChild(renderer.domElement);


\`\`\``,

      scene: `
## Three.js 场景管理

\`\`\`typescript
const scene = new THREE.Scene();
scene.background = new THREE.Color(0x87CEEB); // 天空蓝背景

// 添加环境光
const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
scene.add(ambientLight);

// 添加方向光
const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
directionalLight.position.set(-1, 2, 4);
directionalLight.castShadow = true;
scene.add(directionalLight);

// 启用阴影
renderer.shadowMap.enabled = true;
renderer.shadowMap.type = THREE.PCFSoftShadowMap;
\`\`\``,

      camera: `
## Three.js 相机控制

\`\`\`typescript
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';

const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
camera.position.set(0, 5, 10);

// 添加轨道控制器
const controls = new OrbitControls(camera, renderer.domElement);
controls.enableDamping = true;
controls.dampingFactor = 0.05;
controls.minDistance = 1;
controls.maxDistance = 100;

\`\`\``,

      materials: `
## Three.js 材质系统

\`\`\`typescript
// 基础材质（不受光照影响）
const basicMaterial = new THREE.MeshBasicMaterial({ color: 0x00ff00 });

// 标准材质（支持PBR光照）
const standardMaterial = new THREE.MeshStandardMaterial({
  color: 0x0077ff,
  metalness: 0.3,
  roughness: 0.4,
  envMapIntensity: 1.0
});

// 物理材质（高级PBR）
const physicalMaterial = new THREE.MeshPhysicalMaterial({
  color: 0xff0077,
  metalness: 0.0,
  roughness: 0.1,
  transmission: 0.9,
  transparent: true,
  thickness: 0.5
});

// Phong材质（经典光照模型）
const phongMaterial = new THREE.MeshPhongMaterial({
  color: 0x00ff00,
  specular: 0x111111,
  shininess: 100
});
\`\`\``,

      geometry: `
## Three.js 几何体

\`\`\`typescript
// 基础几何体
const boxGeometry = new THREE.BoxGeometry(1, 1, 1);
const sphereGeometry = new THREE.SphereGeometry(0.5, 32, 32);
const planeGeometry = new THREE.PlaneGeometry(10, 10);
const cylinderGeometry = new THREE.CylinderGeometry(1, 1, 2, 32);

// 创建网格
const cube = new THREE.Mesh(boxGeometry, standardMaterial);
const sphere = new THREE.Mesh(sphereGeometry, standardMaterial);
const plane = new THREE.Mesh(planeGeometry, standardMaterial);

// 设置位置
cube.position.set(0, 0, 0);
sphere.position.set(2, 0, 0);
plane.position.set(-2, 0, 0);
plane.rotation.x = -Math.PI / 2;

// 添加到场景
scene.add(cube);
scene.add(sphere);
scene.add(plane);
\`\`\``,

      lighting: `
## Three.js 光照系统

\`\`\`typescript
// 环境光（全局照明）
const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
scene.add(ambientLight);

// 方向光（太阳光）
const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
directionalLight.position.set(-1, 2, 4);
directionalLight.castShadow = true;
directionalLight.shadow.mapSize.width = 2048;
directionalLight.shadow.mapSize.height = 2048;
scene.add(directionalLight);

// 点光源
const pointLight = new THREE.PointLight(0xff0000, 1, 100);
pointLight.position.set(10, 10, 10);
pointLight.castShadow = true;
scene.add(pointLight);

// 聚光灯
const spotLight = new THREE.SpotLight(0xffffff, 1);
spotLight.position.set(0, 10, 0);
spotLight.angle = Math.PI / 4;
spotLight.penumbra = 0.1;
spotLight.decay = 2;
spotLight.distance = 200;
spotLight.castShadow = true;
scene.add(spotLight);
\`\`\``,

      animation: `
## Three.js 渲染和动画系统

\`\`\`typescript
import { Clock } from 'three';

const clock = new THREE.Clock();

// 🎯 根据需求选择渲染模式

// ✅ 连续渲染循环 - 用于需要持续更新的场景
function continuousRender() {
  const deltaTime = clock.getDelta();
  const elapsedTime = clock.getElapsedTime();
  
  // 旋转动画
  cube.rotation.x += deltaTime;
  cube.rotation.y += deltaTime * 0.5;
  
  // 位置动画
  sphere.position.y = Math.sin(elapsedTime) * 2;
  
  // 缩放动画
  plane.scale.setScalar(1 + Math.sin(elapsedTime * 2) * 0.1);
  
  controls.update();
  renderer.render(scene, camera);
  requestAnimationFrame(continuousRender);
}

// ✅ 按需渲染 - 用于静态场景或事件驱动更新
function renderOnDemand() {
  controls.update();
  renderer.render(scene, camera);
}

// 🎮 使用场景示例：
// 连续动画、移动控制、粒子效果 → 使用 continuousRender()
// 一次性操作、静态展示、点击响应 → 使用 renderOnDemand()

// FBX动画（主要使用）
import { FBXLoader } from 'three/examples/jsm/loaders/FBXLoader.js';

const fbxLoader = new FBXLoader();
let mixer: THREE.AnimationMixer;

fbxLoader.load('character.fbx', (fbx) => {
  scene.add(fbx);

  // FBX模型通常包含动画
  if (fbx.animations.length > 0) {
    mixer = new THREE.AnimationMixer(fbx);

    // 播放第一个动画（如idle、walk等）
    const action = mixer.clipAction(fbx.animations[0]);
    action.play();

    // 可以同时管理多个动画
    const walkAction = mixer.clipAction(fbx.animations.find(clip => clip.name === 'walk'));
    const runAction = mixer.clipAction(fbx.animations.find(clip => clip.name === 'run'));
  }
});

// GLTF动画（备用）
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js';

const gltfLoader = new GLTFLoader();
gltfLoader.load('model.glb', (gltf) => {
  scene.add(gltf.scene);

  if (gltf.animations.length > 0) {
    if (!mixer) mixer = new THREE.AnimationMixer(gltf.scene);
    const action = mixer.clipAction(gltf.animations[0]);
    action.play();
  }
});

// 在连续渲染循环中更新mixer
function animateWithMixer() {
  const deltaTime = clock.getDelta();
  if (mixer) mixer.update(deltaTime);

  renderer.render(scene, camera);
  requestAnimationFrame(animateWithMixer);
}
\`\`\``,

      performance: `
## Three.js 性能优化

\`\`\`typescript
// 渲染器优化
const renderer = new THREE.WebGLRenderer({
  antialias: true,
  alpha: false,
  powerPreference: 'high-performance'
});
renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
renderer.shadowMap.enabled = true;
renderer.shadowMap.type = THREE.PCFSoftShadowMap;

// 几何体优化
// 使用BufferGeometry而不是Geometry
const geometry = new THREE.BufferGeometry();

// 材质优化
// 共享材质实例
const sharedMaterial = new THREE.MeshStandardMaterial({ color: 0x00ff00 });
const mesh1 = new THREE.Mesh(geometry1, sharedMaterial);
const mesh2 = new THREE.Mesh(geometry2, sharedMaterial);

// 实例化渲染（大量相同对象）
const instancedMesh = new THREE.InstancedMesh(geometry, material, 1000);
const matrix = new THREE.Matrix4();
for (let i = 0; i < 1000; i++) {
  matrix.setPosition(Math.random() * 100, Math.random() * 100, Math.random() * 100);
  instancedMesh.setMatrixAt(i, matrix);
}
scene.add(instancedMesh);

// 视锥体剔除
mesh.frustumCulled = true;

// LOD（细节层次）
const lod = new THREE.LOD();
lod.addLevel(highDetailMesh, 0);
lod.addLevel(mediumDetailMesh, 50);
lod.addLevel(lowDetailMesh, 100);
scene.add(lod);
\`\`\``,

      controls: `
## Three.js 控制器

\`\`\`typescript
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';
import { FlyControls } from 'three/examples/jsm/controls/FlyControls.js';
import { PointerLockControls } from 'three/examples/jsm/controls/PointerLockControls.js';

// 轨道控制器（最常用）
const orbitControls = new OrbitControls(camera, renderer.domElement);
orbitControls.enableDamping = true;
orbitControls.dampingFactor = 0.05;
orbitControls.screenSpacePanning = false;
orbitControls.minDistance = 1;
orbitControls.maxDistance = 100;
orbitControls.maxPolarAngle = Math.PI / 2;

// 飞行控制器
const flyControls = new FlyControls(camera, renderer.domElement);
flyControls.movementSpeed = 10;
flyControls.rollSpeed = Math.PI / 24;
flyControls.autoForward = false;
flyControls.dragToLook = false;

// 指针锁定控制器（第一人称）
const pointerLockControls = new PointerLockControls(camera, renderer.domElement);
scene.add(pointerLockControls.getObject());

// 键盘控制
const keys = { w: false, a: false, s: false, d: false };
document.addEventListener('keydown', (event) => {
  switch (event.code) {
    case 'KeyW': keys.w = true; break;
    case 'KeyA': keys.a = true; break;
    case 'KeyS': keys.s = true; break;
    case 'KeyD': keys.d = true; break;
  }
});
\`\`\``,

      loaders: `
## Three.js 资源加载

\`\`\`typescript
import { FBXLoader } from 'three/examples/jsm/loaders/FBXLoader.js';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js';
import { TextureLoader } from 'three';
import { FontLoader } from 'three/examples/jsm/loaders/FontLoader.js';
import { AudioLoader } from 'three';

// FBX模型加载（主要使用）
const fbxLoader = new FBXLoader();
fbxLoader.load(
  'model.fbx',
  (fbx) => {
    scene.add(fbx);

    }

    console.log('FBX模型加载成功');
  },
  (progress) => {
    console.log('FBX加载进度:', (progress.loaded / progress.total * 100) + '%');
  },
  (error) => {
    console.error('FBX加载失败:', error);
  }
);

// GLTF模型加载（备用）
const gltfLoader = new GLTFLoader();
gltfLoader.load(
  'model.glb',
  (gltf) => {
    scene.add(gltf.scene);
    console.log('GLTF模型加载成功');
  },
  (progress) => {
    console.log('GLTF加载进度:', (progress.loaded / progress.total * 100) + '%');
  },
  (error) => {
    console.error('GLTF加载失败:', error);
  }
);

// 纹理加载
const textureLoader = new TextureLoader();
const texture = textureLoader.load('texture.jpg');
texture.wrapS = THREE.RepeatWrapping;
texture.wrapT = THREE.RepeatWrapping;
texture.repeat.set(2, 2);

// 字体加载
const fontLoader = new FontLoader();
fontLoader.load('font.json', (font) => {
  const textGeometry = new THREE.TextGeometry('Hello Three.js', {
    font: font,
    size: 1,
    height: 0.1
  });
  const textMaterial = new THREE.MeshStandardMaterial({ color: 0x00ff00 });
  const textMesh = new THREE.Mesh(textGeometry, textMaterial);
  scene.add(textMesh);
});
\`\`\``,

      fbx: `
## Three.js FBX模型和动画最佳实践

\`\`\`typescript
import { FBXLoader } from 'three/examples/jsm/loaders/FBXLoader.js';

// FBX加载器设置
const fbxLoader = new FBXLoader();
const clock = new THREE.Clock();
let mixer: THREE.AnimationMixer;
let currentAction: THREE.AnimationAction;

// 加载FBX模型
function loadFBXModel(path: string): Promise<THREE.Group> {
  return new Promise((resolve, reject) => {
    fbxLoader.load(
      path,
      (fbx) => {
        // 设置模型缩放和位置
        fbx.scale.setScalar(0.01); // FBX模型通常需要缩放
        fbx.position.set(0, 0, 0);

        // 遍历模型的所有子对象
        fbx.traverse((child) => {
          if (child.isMesh) {
            child.castShadow = true;
            child.receiveShadow = true;

            // 确保材质正确
            if (child.material) {
              child.material.needsUpdate = true;
            }
          }
        });

        scene.add(fbx);
        resolve(fbx);
      },
      (progress) => {
        console.log('FBX加载进度:', (progress.loaded / progress.total * 100) + '%');
      },
      (error) => {
        console.error('FBX加载失败:', error);
        reject(error);
      }
    );
  });
}

// FBX动画管理
function setupFBXAnimation(fbx: THREE.Group) {
  if (fbx.animations && fbx.animations.length > 0) {
    mixer = new THREE.AnimationMixer(fbx);

    // 创建动画动作
    const animations: Record<string, THREE.AnimationAction> = {};
    fbx.animations.forEach((clip) => {
      animations[clip.name] = mixer.clipAction(clip);
    });

    // 播放默认动画（通常是idle）
    const idleAnimation = animations['idle'] || animations[Object.keys(animations)[0]];
    if (idleAnimation) {
      currentAction = idleAnimation;
      currentAction.play();
    }

    return animations;
  }
  return {};
}

// 动画切换函数
function switchAnimation(newAction: THREE.AnimationAction, duration: number = 0.5) {
  if (currentAction && currentAction !== newAction) {
    currentAction.fadeOut(duration);
  }

  newAction.reset().fadeIn(duration).play();
  currentAction = newAction;
}

\`\`\`

## 🚨 FBX使用注意事项
- FBX模型通常需要缩放调整（scale.setScalar(0.01)）
- 确保材质和纹理路径正确
- 动画名称通常为：idle, walk, run, jump等
- 使用fadeIn/fadeOut实现平滑动画过渡
- 在组件卸载时记得dispose mixer和模型`,

      default: `
## Three.js 完整示例

\`\`\`typescript
import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';

// 基础设置
const scene = new THREE.Scene();
const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
const renderer = new THREE.WebGLRenderer({ antialias: true });

// 配置渲染器
renderer.setSize(window.innerWidth, window.innerHeight);
renderer.setPixelRatio(window.devicePixelRatio);
renderer.shadowMap.enabled = true;
renderer.shadowMap.type = THREE.PCFSoftShadowMap;
document.body.appendChild(renderer.domElement);

// 添加控制器
const controls = new OrbitControls(camera, renderer.domElement);
controls.enableDamping = true;
controls.dampingFactor = 0.05;

// 添加光照
const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
scene.add(ambientLight);

const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
directionalLight.position.set(-1, 2, 4);
directionalLight.castShadow = true;
scene.add(directionalLight);

// 创建几何体
const geometry = new THREE.BoxGeometry(1, 1, 1);
const material = new THREE.MeshStandardMaterial({ color: 0x00ff00 });
const cube = new THREE.Mesh(geometry, material);
cube.castShadow = true;
scene.add(cube);

// 相机位置
camera.position.set(0, 2, 5);

// 响应式处理
window.addEventListener('resize', () => {
  camera.aspect = window.innerWidth / window.innerHeight;
  camera.updateProjectionMatrix();
  renderer.setSize(window.innerWidth, window.innerHeight);
});
render(); // 初始渲染

// 在需要时调用render()，例如：
// controls.addEventListener('change', render);
// window.addEventListener('resize', render);
\`\`\`
`
    };

    const selectedPractices = topics
      .map(topic => practices[topic] || practices.default)
      .join('\n\n');

    return `# Three.js 0.178.0 最佳实践指南

${selectedPractices}

## 🚨 关键注意事项
- 使用 ES6 模块导入 Three.js
- 启用抗锯齿和阴影映射
- 实现响应式设计
- 使用 OrbitControls 进行相机控制
- 在渲染循环中更新控制器
- 正确处理资源清理和内存管理
- 优先使用 BufferGeometry 和实例化渲染

## 📋 完整导入示例
\`\`\`typescript
import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js';
import { TextureLoader } from 'three';
\`\`\``;
  }

  /**
   * 分析需要查询的API主题
   */
  static analyzeRequiredTopics(gameDesign: Record<string, unknown>, initialResponse: string): string[] {
    const topics: string[] = [];

    // 基础主题总是包含
    topics.push('basic', 'scene', 'camera', 'materials', 'lighting');

    // 根据游戏设计添加特定主题
    const mechanics = gameDesign.mechanics as string[] | undefined;
    const features = gameDesign.features as string[] | undefined;
    if (mechanics?.includes('animation') || features?.includes('animation')) {
      topics.push('animation');
    }

    const graphics = gameDesign.graphics as string[] | undefined;
    const gameType = gameDesign.gameType as string | undefined;
    if (graphics?.includes('3d') || gameType === '3d') {
      topics.push('geometry', 'performance');
    }

    const controls = gameDesign.controls as string[] | undefined;
    if (controls?.includes('orbit') || controls?.includes('camera')) {
      topics.push('controls');
    }

    const assets = gameDesign.assets as { models?: unknown[] } | undefined;
    if (assets?.models?.length || initialResponse.includes('FBXLoader') || initialResponse.includes('GLTFLoader')) {
      topics.push('loaders');
      // 如果有模型资源，很可能需要FBX支持
      topics.push('fbx');
    }

    return [...new Set(topics)]; // 去重
  }
}
