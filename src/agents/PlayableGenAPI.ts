import { AgentCoordinator, WorkflowResult } from "./AgentCoordinator";
import { GameDesignAgent, GameDesign } from "./GameDesignAgent";
import { CodeGenerationAgent, GeneratedCode, CodeGenerationConfig } from "./CodeGenerationAgent";
import { StreamCallback } from "./CustomClaude4Client";
import { AGENT_CONFIG } from '../config/agent-config';
import { OptimizationManager, OptimizationResult, OptimizationConfig } from './OptimizationManager';
import fs from 'fs/promises';
import path from 'path';

// API配置
export interface PlayableGenConfig {
  outputDir?: string;
  codeGeneration?: Partial<CodeGenerationConfig>;
  enableLogging?: boolean;
  optimization?: Partial<OptimizationConfig>;
}

// 扩展的工作流程结果（包含gameId）
interface ExtendedWorkflowResult extends WorkflowResult {
  gameId: string;
  startTime: number;
  result?: GameGenerationResponse;
}

// 游戏生成请求
export interface GameGenerationRequest {
  description: string;
  preferences?: {
    gameType?: string;
    difficulty?: string;
    theme?: string;
    controls?: string[];
  };
  outputOptions?: {
    format?: "html" | "module";
    includeAssets?: boolean;
    minify?: boolean;
  };
}

// 游戏生成响应
export interface GameGenerationResponse {
  success: boolean;
  gameId: string;
  gameDesign?: GameDesign;
  files?: {
    html?: string;
    css?: string;
    javascript?: string;
  };
  downloadUrl?: string;
  errors: string[];
  progress: number;
  estimatedTime?: string;
}

// PlayableGen主API类
export class PlayableGenAPI {
  private coordinator: AgentCoordinator;
  private gameDesignAgent: GameDesignAgent;
  private codeGenerationAgent: CodeGenerationAgent;
  private optimizationManager: OptimizationManager;
  private config: PlayableGenConfig;
  private activeJobs: Map<string, ExtendedWorkflowResult> = new Map();

  constructor(config: PlayableGenConfig = {}) {
    this.config = {
      outputDir: config.outputDir || AGENT_CONFIG.APP.OUTPUT_DIR,
      enableLogging: config.enableLogging ?? AGENT_CONFIG.APP.DEBUG_LOGGING,
      ...config
    };

    this.coordinator = new AgentCoordinator();
    this.gameDesignAgent = new GameDesignAgent();
    this.codeGenerationAgent = new CodeGenerationAgent({
      generationConfig: config.codeGeneration
    });
    this.optimizationManager = new OptimizationManager(config.optimization);

    this.initializeOutputDirectory();
  }

  // 初始化输出目录
  private async initializeOutputDirectory(): Promise<void> {
    try {
      if (this.config.outputDir) {
        await fs.mkdir(this.config.outputDir, { recursive: true });
      }
    } catch (error) {
      console.error("创建输出目录失败:", error);
    }
  }

  // 优化的游戏生成方法（推荐使用）
  public async generateGameOptimized(request: GameGenerationRequest, providedGameId?: string): Promise<GameGenerationResponse> {
    const gameId = providedGameId || this.generateGameId();

    try {
      this.log(`开始优化游戏生成: ${gameId}: ${request.description}`);

      // 使用优化管理器生成游戏
      const optimizationResult = await this.optimizationManager.optimizedGeneration(request.description);

      if (optimizationResult.success && optimizationResult.gameDesign && optimizationResult.generatedCode) {
        // 保存生成的文件
        const files = await this.saveGameFiles(gameId, optimizationResult.generatedCode);

        const response: GameGenerationResponse = {
          success: true,
          gameId,
          gameDesign: optimizationResult.gameDesign,
          files,
          downloadUrl: `/api/download/${gameId}`,
          errors: optimizationResult.errors,
          progress: 100,
          estimatedTime: `${optimizationResult.performance.totalTime}ms`
        };

        this.log(`优化游戏生成成功: ${gameId}`, {
          quality: optimizationResult.quality,
          performance: optimizationResult.performance
        });

        return response;
      } else {
        return {
          success: false,
          gameId,
          errors: optimizationResult.errors.length > 0 ? optimizationResult.errors : ['游戏生成失败'],
          progress: 0
        };
      }
    } catch (error) {
      console.error(`[PlayableGenAPI] 优化游戏生成失败: ${gameId}`, error);

      return {
        success: false,
        gameId,
        errors: [error instanceof Error ? error.message : '未知错误'],
        progress: 0
      };
    }
  }

  // 生成游戏 - 主要API方法
  public async generateGame(request: GameGenerationRequest): Promise<GameGenerationResponse> {
    const gameId = this.generateGameId();
    
    try {
      this.log(`开始生成游戏 ${gameId}: ${request.description}`);
      
      // 构建完整的用户输入
      const fullInput = this.buildFullInput(request);
      
      // 运行工作流程
      const result = await this.coordinator.runWorkflow(fullInput);
      
      // 保存结果
      this.activeJobs.set(gameId, {
        ...result,
        gameId,
        startTime: Date.now()
      });
      
      if (result.success && result.generatedCode) {
        // 保存文件
        const files = await this.saveGameFiles(gameId, result.generatedCode);
        
        return {
          success: true,
          gameId,
          gameDesign: result.gameDesign,
          files,
          downloadUrl: this.getDownloadUrl(gameId),
          errors: result.errors,
          progress: result.progress,
          estimatedTime: this.estimatePlayTime(result.gameDesign)
        };
      } else {
        return {
          success: false,
          gameId,
          errors: result.errors,
          progress: result.progress
        };
      }
    } catch (error) {
      this.log(`游戏生成失败 ${gameId}: ${error}`);
      
      return {
        success: false,
        gameId,
        errors: [error instanceof Error ? error.message : "未知错误"],
        progress: 0
      };
    }
  }

  // 仅设计游戏（不生成代码）
  public async designGame(description: string): Promise<{
    success: boolean;
    gameDesign?: GameDesign;
    errors: string[];
  }> {
    try {
      const threadId = `design_only_${Date.now()}`;
      const result = await this.gameDesignAgent.run(description, { threadId });
      
      if (result.status === "completed" && result.context.gameDesign) {
        return {
          success: true,
          gameDesign: result.context.gameDesign,
          errors: []
        };
      } else {
        return {
          success: false,
          errors: ["游戏设计失败"]
        };
      }
    } catch (error) {
      return {
        success: false,
        errors: [error instanceof Error ? error.message : "设计过程出错"]
      };
    }
  }

  // 根据设计生成代码
  public async generateCodeFromDesign(gameDesign: GameDesign): Promise<{
    success: boolean;
    generatedCode?: GeneratedCode;
    errors: string[];
  }> {
    try {
      const threadId = `code_only_${Date.now()}`;
      const input = `根据以下游戏设计生成代码：\n${JSON.stringify(gameDesign, null, 2)}`;
      
      const result = await this.codeGenerationAgent.run(input, { threadId });
      
      if (result.status === "completed" && result.context.generatedCode) {
        return {
          success: true,
          generatedCode: result.context.generatedCode,
          errors: []
        };
      } else {
        return {
          success: false,
          errors: ["代码生成失败"]
        };
      }
    } catch (error) {
      return {
        success: false,
        errors: [error instanceof Error ? error.message : "代码生成过程出错"]
      };
    }
  }

  // 获取游戏状态
  public getGameStatus(gameId: string): GameGenerationResponse | null {
    const result = this.activeJobs.get(gameId);
    
    if (!result) {
      return null;
    }

    return {
      success: result.success,
      gameId,
      gameDesign: result.gameDesign,
      errors: result.errors,
      progress: result.progress,
      downloadUrl: result.success ? this.getDownloadUrl(gameId) : undefined
    };
  }

  // 获取所有活跃的游戏任务
  public getActiveGames(): string[] {
    return Array.from(this.activeJobs.keys());
  }

  // 删除游戏
  public async deleteGame(gameId: string): Promise<boolean> {
    try {
      // 从内存中删除
      this.activeJobs.delete(gameId);
      
      // 删除文件
      if (this.config.outputDir) {
        const gameDir = path.join(this.config.outputDir, gameId);
        await fs.rm(gameDir, { recursive: true, force: true });
      }
      
      return true;
    } catch (error) {
      this.log(`删除游戏失败 ${gameId}: ${error}`);
      return false;
    }
  }

  // 私有辅助方法
  private generateGameId(): string {
    return `game_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  private buildFullInput(request: GameGenerationRequest): string {
    let input = request.description;
    
    if (request.preferences) {
      const prefs = [];
      if (request.preferences.gameType) prefs.push(`游戏类型: ${request.preferences.gameType}`);
      if (request.preferences.difficulty) prefs.push(`难度: ${request.preferences.difficulty}`);
      if (request.preferences.theme) prefs.push(`主题: ${request.preferences.theme}`);
      if (request.preferences.controls) prefs.push(`控制方式: ${request.preferences.controls.join(', ')}`);
      
      if (prefs.length > 0) {
        input += `\n\n用户偏好:\n${prefs.join('\n')}`;
      }
    }
    
    return input;
  }

  private async saveGameFiles(gameId: string, code: GeneratedCode): Promise<{
    html?: string;
    css?: string;
    javascript?: string;
    tsx?: string;
  }> {
    if (!this.config.outputDir) {
      return {};
    }

    const gameDir = path.join(this.config.outputDir, gameId);
    await fs.mkdir(gameDir, { recursive: true });

    const files: any = {};

    // 保存TSX组件文件（主要的生成内容）
    if (code.component) {
      // 保存到游戏目录
      const tsxPath = path.join(gameDir, code.fileName || 'GameComponent.tsx');
      await fs.writeFile(tsxPath, code.component, 'utf-8');
      files.tsx = tsxPath;

      // 同时保存到项目的components/generated目录，便于Canvas页面动态导入
      const projectComponentsDir = path.join(process.cwd(), 'components', 'generated');
      await fs.mkdir(projectComponentsDir, { recursive: true });
      const projectTsxPath = path.join(projectComponentsDir, `${gameId}.tsx`);
      await fs.writeFile(projectTsxPath, code.component, 'utf-8');

      console.log(`[PlayableGenAPI] 保存TSX文件: ${tsxPath} (${code.component.length} 字符)`);
      console.log(`[PlayableGenAPI] 保存项目组件: ${projectTsxPath}`);
    }

    // 保存HTML文件
    if (code.html) {
      const htmlPath = path.join(gameDir, 'index.html');
      await fs.writeFile(htmlPath, code.html, 'utf-8');
      files.html = htmlPath;
    }

    // 保存CSS文件
    if (code.css) {
      const cssPath = path.join(gameDir, 'style.css');
      await fs.writeFile(cssPath, code.css, 'utf-8');
      files.css = cssPath;
    }

    // 保存JavaScript文件
    if (code.javascript) {
      const jsPath = path.join(gameDir, 'game.js');
      await fs.writeFile(jsPath, code.javascript, 'utf-8');
      files.javascript = jsPath;
    }

    // 保存依赖项信息
    if (code.dependencies.length > 0) {
      const depsPath = path.join(gameDir, 'dependencies.json');
      await fs.writeFile(depsPath, JSON.stringify(code.dependencies, null, 2), 'utf-8');
    }

    // 保存组件元数据
    if (code.metadata) {
      const metadataPath = path.join(gameDir, 'metadata.json');
      await fs.writeFile(metadataPath, JSON.stringify(code.metadata, null, 2), 'utf-8');
    }

    return files;
  }

  private getDownloadUrl(gameId: string): string {
    return `/api/games/${gameId}/download`;
  }

  private estimatePlayTime(gameDesign?: GameDesign): string {
    if (!gameDesign) return "未知";
    return gameDesign.estimatedPlayTime || "5-10分钟";
  }

  private estimatePlayTimeFromCode(generatedCode?: GeneratedCode): string {
    if (!generatedCode) return "未知";
    return generatedCode.performance?.totalTime ? `${generatedCode.performance.totalTime}ms` : "5-10分钟";
  }

  private log(message: string): void {
    if (this.config.enableLogging) {
      console.log(`[PlayableGen] ${new Date().toISOString()}: ${message}`);
    }
  }

  // 获取API统计信息
  public getStats(): {
    totalGames: number;
    successfulGames: number;
    failedGames: number;
    averageProgress: number;
  } {
    const results = Array.from(this.activeJobs.values());
    const successful = results.filter(r => r.success).length;
    const failed = results.filter(r => !r.success).length;
    const avgProgress = results.length > 0 
      ? results.reduce((sum, r) => sum + r.progress, 0) / results.length 
      : 0;

    return {
      totalGames: results.length,
      successfulGames: successful,
      failedGames: failed,
      averageProgress: Math.round(avgProgress)
    };
  }

  // 流式生成游戏
  async generateGameStream(
    request: GameGenerationRequest,
    streamCallback: StreamCallback,
    providedGameId?: string
  ): Promise<GameGenerationResponse> {
    const gameId = providedGameId || this.generateGameId();
    const startTime = Date.now();

    try {
      streamCallback.onStatus?.('开始游戏生成流程...');

      // 记录开始状态
      this.activeJobs.set(gameId, {
        gameId,
        success: false,
        progress: 0,
        errors: [],
        startTime
      });

      // 直接进行代码生成（跳过设计环节）
      streamCallback.onStatus?.('正在生成游戏代码...');
      streamCallback.onProgress?.({ current: 1, total: 2 });

      const userInput = this.buildFullInput(request);
      console.log('[PlayableGen] 用户输入:', userInput);
      
      // 直接使用用户需求进行代码生成
      const codeResult = await this.codeGenerationAgent.runStream(
        userInput, // 直接传递用户需求
        {
          onToken: streamCallback.onToken,
          onStatus: (status) => streamCallback.onStatus?.(`[代码生成] ${status}`),
          onProgress: streamCallback.onProgress
        },
        { threadId: `${gameId}_code` }
      );

      if (codeResult.status !== "completed" || !codeResult.context?.generatedCode) {
        throw new Error("代码生成失败");
      }

      const generatedCode = codeResult.context.generatedCode as GeneratedCode;

      // 保存和完成
      streamCallback.onStatus?.('正在保存游戏文件...');
      streamCallback.onProgress?.({ current: 2, total: 2 });

      const files = await this.saveGameFiles(gameId, generatedCode);

      const result: GameGenerationResponse = {
        success: true,
        gameId,
        // gameDesign, // 移除游戏设计返回
        files,
        downloadUrl: this.getDownloadUrl(gameId),
        errors: [],
        progress: 100,
        estimatedTime: this.estimatePlayTimeFromCode(generatedCode) // 修改评估方法
      };

      // 更新job状态
      this.activeJobs.set(gameId, {
        ...result,
        startTime
      });

      streamCallback.onStatus?.('游戏生成完成！');
      return result;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      console.error(`[PlayableGen] 流式游戏生成失败 ${gameId}:`, error);

      const result: GameGenerationResponse = {
        success: false,
        gameId,
        errors: [errorMessage],
        progress: 50
      };

      this.activeJobs.set(gameId, {
        gameId,
        success: false,
        progress: 50,
        errors: [errorMessage],
        startTime
      });

      streamCallback.onStatus?.(`游戏生成失败: ${errorMessage}`);
      return result;
    }
  }

  // 获取优化性能统计
  public getOptimizationStats() {
    return this.optimizationManager.getPerformanceStats();
  }

  // 运行基准测试
  public async runBenchmark() {
    return await this.optimizationManager.runBenchmark();
  }

  // 重置优化统计
  public resetOptimizationStats() {
    this.optimizationManager.resetStats();
  }

  // 更新优化配置
  public updateOptimizationConfig(config: Partial<OptimizationConfig>) {
    this.optimizationManager.updateConfig(config);
  }

  // 获取当前优化配置
  public getOptimizationConfig() {
    return this.optimizationManager.getConfig();
  }
}
