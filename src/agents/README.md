# PlayableGen Agent系统 - Claude4集成版

## 概述

PlayableGen Agent系统是一个基于Claude4的AI驱动游戏制作平台，通过多Agent协作实现从需求分析到HTML游戏文件输出的完整自动化流程。

## 🚀 核心特性

### 1. Claude4深度集成
- **自定义Claude4客户端**: 专门优化的API客户端，支持完整的Claude4功能
- **智能错误处理**: 自动重试、超时控制、详细错误日志
- **性能优化**: 连接池、请求缓存、响应时间监控
- **配置灵活**: 支持多种API端点和模型配置

### 2. 多Agent协作架构
- **GameDesignAgent**: 游戏设计专家，负责需求分析和游戏设计
- **CodeGenerationAgent**: 代码生成专家，负责HTML5游戏代码生成
- **AgentCoordinator**: 工作流协调器，管理Agent间的协作流程
- **PlayableGenAPI**: 统一API接口，提供简洁的外部调用方式

### 3. 完整的游戏制作流程
```
用户需求 → 游戏设计 → 代码生成 → 质量审查 → 最终输出
```

## 📦 安装和配置

### 依赖安装
```bash
npm install @langchain/langgraph @langchain/core zod
```

### 环境配置
```typescript
// 配置Claude4客户端（现在使用环境变量）
// 请在 .env.local 文件中设置以下环境变量：
// CLAUDE_API_KEY=your-api-key
// CLAUDE_BASE_URL=https://api.anthropic.com/v1/
// CLAUDE_MODEL=claude-4-sonnet
// DEFAULT_MAX_TOKENS=20000
// DEFAULT_TEMPERATURE=0.7
// DEFAULT_TIMEOUT=120000

// 客户端会自动从环境变量加载配置
const claude4Client = new CustomClaude4Client({
  // 可以覆盖环境变量中的默认值（但推荐使用环境变量配置）
  temperature: AGENT_CONFIG.TEMPERATURE.CREATIVE // 使用环境变量配置
});
```

## 🎯 快速开始

### 1. 基础使用
```typescript
import { createPlayableGenAPI } from './agents';

const api = createPlayableGenAPI({
  outputDir: './games',
  enableLogging: true
});

const result = await api.generateGame({
  description: "制作一个3D跳跃游戏",
  preferences: {
    gameType: "action",
    difficulty: "easy",
    theme: "卡通风格"
  }
});

if (result.success) {
  console.log(`游戏生成成功! ID: ${result.gameId}`);
  console.log(`下载地址: ${result.downloadUrl}`);
} else {
  console.log("生成失败:", result.errors);
}
```

### 分步骤使用

```typescript
import { GameDesignAgent, CodeGenerationAgent } from './agents';

// 1. 仅设计游戏
const designAgent = new GameDesignAgent();
const designResult = await designAgent.run("制作一个益智解谜游戏");

if (designResult.status === "completed") {
  const gameDesign = designResult.context.gameDesign;
  
  // 2. 根据设计生成代码
  const codeAgent = new CodeGenerationAgent();
  const codeInput = `根据设计生成代码：${JSON.stringify(gameDesign)}`;
  const codeResult = await codeAgent.run(codeInput);
  
  if (codeResult.status === "completed") {
    const generatedCode = codeResult.context.generatedCode;
    console.log("代码生成完成!", generatedCode);
  }
}
```

## API参考

### PlayableGenAPI

主要的API接口类，提供完整的游戏生成服务。

#### 构造函数

```typescript
new PlayableGenAPI(config?: PlayableGenConfig)
```

#### 主要方法

- `generateGame(request: GameGenerationRequest): Promise<GameGenerationResponse>`
- `designGame(description: string): Promise<{success: boolean, gameDesign?: GameDesign}>`
- `generateCodeFromDesign(gameDesign: GameDesign): Promise<{success: boolean, generatedCode?: GeneratedCode}>`
- `getGameStatus(gameId: string): GameGenerationResponse | null`
- `deleteGame(gameId: string): Promise<boolean>`

### GameDesignAgent

游戏设计专家Agent。

```typescript
const agent = new GameDesignAgent({
  name: "MyDesigner",
  systemPrompt: "自定义系统提示..."
  // maxTokens不再支持传入 - 统一使用环境变量配置
});

const result = await agent.run("游戏需求描述");
```

### CodeGenerationAgent

代码生成专家Agent。

```typescript
const agent = new CodeGenerationAgent({
  generationConfig: {
    framework: "babylon.js",
    outputFormat: "html",
    includeAssets: true
  }
});
```

## 配置选项

### PlayableGenConfig

```typescript
interface PlayableGenConfig {
  outputDir?: string;           // 输出目录
  codeGeneration?: {
    framework: "babylon.js" | "three.js";  // 推荐使用babylon.js，支持Inspector调试
    outputFormat: "html" | "module";
    includeAssets: boolean;
    minify: boolean;
  };
  enableLogging?: boolean;      // 启用日志
}
```

### GameGenerationRequest

```typescript
interface GameGenerationRequest {
  description: string;          // 游戏描述
  preferences?: {
    gameType?: string;          // 游戏类型
    difficulty?: string;        // 难度
    theme?: string;            // 主题
    controls?: string[];       // 控制方式
  };
  outputOptions?: {
    format?: "html" | "module";
    includeAssets?: boolean;
    minify?: boolean;
  };
}
```

## 工作流程

1. **需求分析**: 解析用户输入，提取游戏需求
2. **游戏设计**: GameDesignAgent生成详细的游戏设计方案
3. **代码生成**: CodeGenerationAgent将设计转换为HTML5代码
4. **质量检查**: 验证生成的代码和资源
5. **文件输出**: 保存完整的游戏文件

## 支持的游戏类型

- **Action**: 动作游戏（跳跃、射击等）
- **Puzzle**: 益智游戏（解谜、匹配等）
- **Adventure**: 冒险游戏（探索、收集等）
- **Strategy**: 策略游戏（塔防、经营等）
- **Casual**: 休闲游戏（点击、放置等）

## 技术栈

- **LangGraph.js**: Agent协作框架
- **LangChain**: AI模型集成
- **Anthropic Claude**: 语言模型
- **Three.js/Babylon.js**: 3D渲染引擎
- **TypeScript**: 类型安全的开发
- **Zod**: 数据验证

## 测试

运行测试套件：

```bash
npm run test:agents
```

或者直接运行测试文件：

```typescript
import { runTests } from './agents/test-agents';
await runTests();
```

## 扩展开发

### 创建自定义Agent

```typescript
import { BaseAgent, AgentConfig, AgentState } from './agents';

class CustomAgent extends BaseAgent {
  constructor(config: AgentConfig) {
    super(config);
  }

  protected async executeNode(state: AgentState): Promise<Partial<AgentState>> {
    // 实现自定义逻辑
    return {
      status: "completed",
      messages: [/* 结果消息 */]
    };
  }
}
```

### 添加新的游戏类型

在GameDesignAgent中扩展GameDesignSchema：

```typescript
const GameDesignSchema = z.object({
  gameType: z.enum(["action", "puzzle", "adventure", "strategy", "casual", "newType"]),
  // ... 其他字段
});
```

## 注意事项

1. **API密钥**: 确保设置了Anthropic API密钥
2. **内存使用**: 大型游戏生成可能消耗较多内存
3. **生成时间**: 复杂游戏的生成时间可能较长
4. **文件权限**: 确保输出目录有写入权限

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。
