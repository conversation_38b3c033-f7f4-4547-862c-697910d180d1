/**
 * AccuracyOptimizer - 生成准确性优化系统
 * 优化Agent间的协作流程，改进错误处理机制，提升代码生成的准确性和一致性
 */

import { GameDesign } from './GameDesignAgent';
import { GeneratedCode } from './CodeGenerationAgent';

// 验证结果接口
export interface ValidationResult {
  isValid: boolean;
  score: number; // 0-100的质量分数
  errors: ValidationError[];
  warnings: ValidationWarning[];
  suggestions: ValidationSuggestion[];
}

// 验证错误接口
export interface ValidationError {
  type: 'syntax' | 'logic' | 'compatibility' | 'performance' | 'security';
  severity: 'critical' | 'high' | 'medium' | 'low';
  message: string;
  location?: string;
  fix?: string;
}

// 验证警告接口
export interface ValidationWarning {
  type: 'performance' | 'compatibility' | 'best_practice' | 'accessibility';
  message: string;
  location?: string;
  suggestion?: string;
}

// 验证建议接口
export interface ValidationSuggestion {
  type: 'optimization' | 'enhancement' | 'alternative';
  message: string;
  impact: 'low' | 'medium' | 'high';
  implementation?: string;
}

// 协作流程状态
export interface CollaborationState {
  currentPhase: 'design' | 'code_generation' | 'review' | 'optimization';
  designComplete: boolean;
  codeComplete: boolean;
  validationPassed: boolean;
  iterationCount: number;
  maxIterations: number;
  errors: string[];
  warnings: string[];
}

/**
 * 生成准确性优化器类
 */
export class AccuracyOptimizer {
  private static readonly MAX_ITERATIONS = 3;
  private static readonly MIN_QUALITY_SCORE = 75;

  /**
   * 验证游戏设计的准确性和完整性
   */
  public static validateGameDesign(design: GameDesign): ValidationResult {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];
    const suggestions: ValidationSuggestion[] = [];
    let score = 100;

    // 检查必需字段
    if (!design.gameType || !design.theme) {
      errors.push({
        type: 'logic',
        severity: 'critical',
        message: '缺少必需的游戏类型或主题信息',
        fix: '确保gameType和theme字段都有有效值'
      });
      score -= 20;
    }

    // 检查技术组件配置
    const techComponents = (design as any).technicalComponents;
    if (!techComponents) {
      errors.push({
        type: 'logic',
        severity: 'high',
        message: '缺少技术组件配置',
        fix: '添加technicalComponents字段，包含相机、光照、材质等配置'
      });
      score -= 15;
    } else {
      // 验证相机配置
      if (!techComponents.camera || !techComponents.camera.type) {
        errors.push({
          type: 'logic',
          severity: 'medium',
          message: '缺少相机配置',
          fix: '添加camera配置，指定相机类型'
        });
        score -= 10;
      }

      // 验证光照配置
      if (!techComponents.lighting || !techComponents.lighting.type) {
        warnings.push({
          type: 'best_practice',
          message: '建议添加光照配置以获得更好的视觉效果',
          suggestion: '添加lighting配置，选择合适的光照预设'
        });
        score -= 5;
      }
    }

    // 检查控制方式配置
    if (!design.controls || !design.controls.primary || design.controls.primary.length === 0) {
      errors.push({
        type: 'logic',
        severity: 'medium',
        message: '缺少控制方式配置',
        fix: '添加controls.primary字段，指定主要控制方式'
      });
      score -= 10;
    }

    // 检查移动端适配
    if (design.controls?.primary.includes('触屏')) {
      if (!techComponents?.camera?.type.includes('THIRD_PERSON') && 
          !techComponents?.camera?.type.includes('TOP_DOWN')) {
        warnings.push({
          type: 'compatibility',
          message: '触屏控制建议使用第三人称或俯视角相机',
          suggestion: '考虑使用THIRD_PERSON或TOP_DOWN相机预设'
        });
        score -= 5;
      }
    }

    // 性能优化建议
    if (design.gameType === 'action' || design.gameType === 'shooter') {
      suggestions.push({
        type: 'optimization',
        message: '动作游戏建议优化渲染性能',
        impact: 'high',
        implementation: '使用LOD技术和对象池管理'
      });
    }

    return {
      isValid: errors.filter(e => e.severity === 'critical').length === 0,
      score: Math.max(0, score),
      errors,
      warnings,
      suggestions
    };
  }

  /**
   * 验证生成代码的准确性和质量
   */
  public static validateGeneratedCode(code: GeneratedCode, design: GameDesign): ValidationResult {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];
    const suggestions: ValidationSuggestion[] = [];
    let score = 100;

    // 检查代码完整性
    if (!code.html || code.html.trim().length === 0) {
      errors.push({
        type: 'syntax',
        severity: 'critical',
        message: 'HTML代码为空或缺失',
        fix: '生成完整的HTML结构'
      });
      score -= 25;
    }

    if (!code.javascript || code.javascript.trim().length === 0) {
      errors.push({
        type: 'syntax',
        severity: 'critical',
        message: 'JavaScript代码为空或缺失',
        fix: '生成完整的游戏逻辑代码'
      });
      score -= 25;
    }

    // 检查HTML结构
    if (code.html) {
      if (!code.html.includes('<canvas')) {
        errors.push({
          type: 'logic',
          severity: 'high',
          message: 'HTML中缺少canvas元素',
          fix: '添加<canvas>元素作为游戏渲染容器'
        });
        score -= 15;
      }

      if (!code.html.includes('viewport')) {
        warnings.push({
          type: 'compatibility',
          message: '建议添加viewport meta标签以支持移动端',
          suggestion: '添加<meta name="viewport" content="width=device-width, initial-scale=1">'
        });
        score -= 5;
      }
    }

    // 检查JavaScript代码质量
    if (code.javascript) {
      // 检查Babylon.js引用
      if (!code.javascript.includes('BABYLON') && !code.javascript.includes('@babylonjs')) {
        errors.push({
          type: 'logic',
          severity: 'high',
          message: '代码中缺少Babylon.js引用',
          fix: '添加Babylon.js库的引用和使用'
        });
        score -= 15;
      }

      // 检查标准化组件使用
      const techComponents = (design as any).technicalComponents;
      if (techComponents) {
        if (techComponents.camera && !code.javascript.includes('CameraPresets')) {
          warnings.push({
            type: 'best_practice',
            message: '建议使用CameraPresets标准化相机组件',
            suggestion: '导入并使用CameraPresets.createCamera()方法'
          });
          score -= 5;
        }

        if (techComponents.lighting && !code.javascript.includes('LightingPresets')) {
          warnings.push({
            type: 'best_practice',
            message: '建议使用LightingPresets标准化光照组件',
            suggestion: '导入并使用LightingPresets.createLighting()方法'
          });
          score -= 5;
        }
      }

      // 检查错误处理
      if (!code.javascript.includes('try') && !code.javascript.includes('catch')) {
        warnings.push({
          type: 'best_practice',
          message: '建议添加错误处理机制',
          suggestion: '使用try-catch块处理可能的运行时错误'
        });
        score -= 5;
      }

      // 检查性能优化
      if (!code.javascript.includes('dispose') && !code.javascript.includes('cleanup')) {
        suggestions.push({
          type: 'optimization',
          message: '建议添加资源清理机制',
          impact: 'medium',
          implementation: '在适当位置调用dispose()方法清理资源'
        });
      }
    }

    // 检查CSS响应式设计
    if (code.css) {
      if (!code.css.includes('@media')) {
        warnings.push({
          type: 'compatibility',
          message: '建议添加响应式CSS以支持不同屏幕尺寸',
          suggestion: '使用@media查询适配移动端和桌面端'
        });
        score -= 5;
      }
    }

    return {
      isValid: errors.filter(e => e.severity === 'critical').length === 0,
      score: Math.max(0, score),
      errors,
      warnings,
      suggestions
    };
  }

  /**
   * 优化协作流程
   */
  public static optimizeCollaboration(state: CollaborationState): {
    shouldContinue: boolean;
    nextPhase: CollaborationState['currentPhase'];
    recommendations: string[];
  } {
    const recommendations: string[] = [];
    let shouldContinue = true;
    let nextPhase = state.currentPhase;

    // 检查迭代次数
    if (state.iterationCount >= state.maxIterations) {
      shouldContinue = false;
      recommendations.push('已达到最大迭代次数，建议人工介入检查');
      return { shouldContinue, nextPhase, recommendations };
    }

    // 根据当前阶段决定下一步
    switch (state.currentPhase) {
      case 'design':
        if (state.designComplete && state.errors.length === 0) {
          nextPhase = 'code_generation';
          recommendations.push('设计阶段完成，开始代码生成');
        } else if (state.errors.length > 0) {
          recommendations.push('设计存在错误，需要重新设计');
          recommendations.push(...state.errors.map(error => `修复错误: ${error}`));
        }
        break;

      case 'code_generation':
        if (state.codeComplete && state.errors.length === 0) {
          nextPhase = 'review';
          recommendations.push('代码生成完成，开始质量审查');
        } else if (state.errors.length > 0) {
          recommendations.push('代码生成存在错误，需要重新生成');
          recommendations.push(...state.errors.map(error => `修复错误: ${error}`));
        }
        break;

      case 'review':
        if (state.validationPassed) {
          nextPhase = 'optimization';
          recommendations.push('质量审查通过，开始优化');
        } else {
          nextPhase = 'code_generation';
          recommendations.push('质量审查未通过，返回代码生成阶段');
        }
        break;

      case 'optimization':
        shouldContinue = false;
        recommendations.push('优化完成，流程结束');
        break;
    }

    // 添加警告处理建议
    if (state.warnings.length > 0) {
      recommendations.push('处理以下警告以提升质量:');
      recommendations.push(...state.warnings.map(warning => `警告: ${warning}`));
    }

    return { shouldContinue, nextPhase, recommendations };
  }

  /**
   * 生成错误修复建议
   */
  public static generateFixSuggestions(errors: ValidationError[]): string[] {
    const suggestions: string[] = [];

    errors.forEach(error => {
      if (error.fix) {
        suggestions.push(`${error.message} - 建议修复: ${error.fix}`);
      } else {
        // 基于错误类型生成通用建议
        switch (error.type) {
          case 'syntax':
            suggestions.push(`${error.message} - 检查代码语法和结构`);
            break;
          case 'logic':
            suggestions.push(`${error.message} - 检查业务逻辑和数据流`);
            break;
          case 'compatibility':
            suggestions.push(`${error.message} - 检查浏览器和设备兼容性`);
            break;
          case 'performance':
            suggestions.push(`${error.message} - 优化性能和资源使用`);
            break;
          case 'security':
            suggestions.push(`${error.message} - 检查安全性和输入验证`);
            break;
        }
      }
    });

    return suggestions;
  }

  /**
   * 计算整体质量分数
   */
  public static calculateOverallQuality(
    designValidation: ValidationResult,
    codeValidation: ValidationResult
  ): number {
    // 设计质量权重40%，代码质量权重60%
    return Math.round(designValidation.score * 0.4 + codeValidation.score * 0.6);
  }

  /**
   * 检查是否达到质量标准
   */
  public static meetsQualityStandard(overallScore: number): boolean {
    return overallScore >= this.MIN_QUALITY_SCORE;
  }
}
