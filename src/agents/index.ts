// 导出所有Agent相关的类和接口

// 自定义Claude4客户端
export {
  CustomClaude4Client,
  DEFAULT_CLAUDE4_CONFIG,
  Claude4Config
} from './CustomClaude4Client';

// 基础Agent
export { BaseAgent, AgentConfig, AgentState, AgentStateAnnotation } from './BaseAgent';

// 游戏设计Agent
export { GameDesignAgent, GameDesign } from './GameDesignAgent';

// 代码生成Agent
export {
  CodeGenerationAgent,
  CodeGenerationConfig,
  GeneratedCode
} from './CodeGenerationAgent';

// Agent协调器
export {
  AgentCoordinator,
  CoordinatorState,
  CoordinatorStateAnnotation,
  WorkflowResult
} from './AgentCoordinator';

// 主API
export {
  PlayableGenAPI,
  PlayableGenConfig,
  GameGenerationRequest,
  GameGenerationResponse
} from './PlayableGenAPI';

// 测试工具
export {
  testClaude4Client,
  performanceTest as claude4PerformanceTest
} from './test-claude4';

export {
  testIntegratedAgents,
  performanceComparison,
  errorHandlingTest
} from './test-integrated-agents';

// 便捷的工厂函数
export function createClaude4Client(config?: Partial<Claude4Config>): CustomClaude4Client {
  return new CustomClaude4Client({
    ...DEFAULT_CLAUDE4_CONFIG,
    ...config
  });
}

export function createPlayableGenAPI(config?: PlayableGenConfig): PlayableGenAPI {
  return new PlayableGenAPI(config);
}

export function createGameDesignAgent(config?: Partial<AgentConfig>): GameDesignAgent {
  return new GameDesignAgent(config);
}

export function createCodeGenerationAgent(config?: Partial<AgentConfig & { generationConfig: CodeGenerationConfig }>): CodeGenerationAgent {
  return new CodeGenerationAgent(config);
}

// 默认配置
export const DEFAULT_CONFIG: PlayableGenConfig = {
  outputDir: './generated-games',
  enableLogging: true,
  codeGeneration: {
    framework: "babylon.js",
    outputFormat: "html",
    includeAssets: true,
    minify: false
  }
};

// 版本信息
export const VERSION = "1.0.0";
export const AGENT_TYPES = {
  GAME_DESIGN: "GameDesignAgent",
  CODE_GENERATION: "CodeGenerationAgent",
  COORDINATOR: "AgentCoordinator"
} as const;
