/**
 * EfficiencyOptimizer - 效率优化与Token管理系统
 * 优化AI调用流程，实现智能Token管理，减少不必要的API调用，提升生成效率
 */

import { GameDesign } from './GameDesignAgent';
import { GeneratedCode } from './CodeGenerationAgent';

// Token使用统计接口
export interface TokenUsageStats {
  totalTokens: number;
  promptTokens: number;
  completionTokens: number;
  cost: number; // 预估成本（美元）
  requestCount: number;
  averageTokensPerRequest: number;
  efficiency: number; // 效率分数 0-100
}

// 缓存条目接口
export interface CacheEntry<T> {
  key: string;
  data: T;
  timestamp: number;
  hitCount: number;
  tokensSaved: number;
}

// 优化建议接口
export interface OptimizationSuggestion {
  type: 'caching' | 'prompt_optimization' | 'batch_processing' | 'template_reuse';
  description: string;
  potentialSavings: {
    tokens: number;
    requests: number;
    timeMs: number;
  };
  implementation: string;
}

// 性能指标接口
export interface PerformanceMetrics {
  averageResponseTime: number;
  successRate: number;
  cacheHitRate: number;
  tokenEfficiency: number;
  costPerGeneration: number;
}

/**
 * 效率优化器类
 */
export class EfficiencyOptimizer {
  private static readonly CACHE_TTL = 24 * 60 * 60 * 1000; // 24小时
  private static readonly MAX_CACHE_SIZE = 1000;
  private static readonly TOKEN_COST_PER_1K = 0.002; // Claude API成本估算

  private static designCache = new Map<string, CacheEntry<GameDesign>>();
  private static codeCache = new Map<string, CacheEntry<GeneratedCode>>();
  private static tokenStats: TokenUsageStats = {
    totalTokens: 0,
    promptTokens: 0,
    completionTokens: 0,
    cost: 0,
    requestCount: 0,
    averageTokensPerRequest: 0,
    efficiency: 100
  };

  /**
   * 生成缓存键
   */
  private static generateCacheKey(input: string, type: 'design' | 'code'): string {
    // 使用简单的哈希算法生成缓存键
    let hash = 0;
    const str = `${type}:${input}`;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash).toString(36);
  }

  /**
   * 检查设计缓存
   */
  public static checkDesignCache(userInput: string): GameDesign | null {
    const key = this.generateCacheKey(userInput, 'design');
    const entry = this.designCache.get(key);

    if (entry && (Date.now() - entry.timestamp) < this.CACHE_TTL) {
      entry.hitCount++;
      console.log(`[EfficiencyOptimizer] 设计缓存命中: ${key}`);
      return entry.data;
    }

    return null;
  }

  /**
   * 缓存设计结果
   */
  public static cacheDesign(userInput: string, design: GameDesign, tokensUsed: number): void {
    const key = this.generateCacheKey(userInput, 'design');
    
    // 清理过期缓存
    this.cleanupCache(this.designCache);

    this.designCache.set(key, {
      key,
      data: design,
      timestamp: Date.now(),
      hitCount: 0,
      tokensSaved: 0
    });

    console.log(`[EfficiencyOptimizer] 设计结果已缓存: ${key}`);
  }

  /**
   * 检查代码缓存
   */
  public static checkCodeCache(designInput: string): GeneratedCode | null {
    const key = this.generateCacheKey(designInput, 'code');
    const entry = this.codeCache.get(key);

    if (entry && (Date.now() - entry.timestamp) < this.CACHE_TTL) {
      entry.hitCount++;
      console.log(`[EfficiencyOptimizer] 代码缓存命中: ${key}`);
      return entry.data;
    }

    return null;
  }

  /**
   * 缓存代码结果
   */
  public static cacheCode(designInput: string, code: GeneratedCode, tokensUsed: number): void {
    const key = this.generateCacheKey(designInput, 'code');
    
    // 清理过期缓存
    this.cleanupCache(this.codeCache);

    this.codeCache.set(key, {
      key,
      data: code,
      timestamp: Date.now(),
      hitCount: 0,
      tokensSaved: 0
    });

    console.log(`[EfficiencyOptimizer] 代码结果已缓存: ${key}`);
  }

  /**
   * 清理过期缓存
   */
  private static cleanupCache<T>(cache: Map<string, CacheEntry<T>>): void {
    const now = Date.now();
    const keysToDelete: string[] = [];

    cache.forEach((entry, key) => {
      if (now - entry.timestamp > this.CACHE_TTL) {
        keysToDelete.push(key);
      }
    });

    keysToDelete.forEach(key => cache.delete(key));

    // 如果缓存过大，删除最少使用的条目
    if (cache.size > this.MAX_CACHE_SIZE) {
      const entries = Array.from(cache.entries());
      entries.sort((a, b) => a[1].hitCount - b[1].hitCount);
      
      const toDelete = entries.slice(0, cache.size - this.MAX_CACHE_SIZE);
      toDelete.forEach(([key]) => cache.delete(key));
    }
  }

  /**
   * 记录Token使用情况
   */
  public static recordTokenUsage(promptTokens: number, completionTokens: number): void {
    this.tokenStats.promptTokens += promptTokens;
    this.tokenStats.completionTokens += completionTokens;
    this.tokenStats.totalTokens += promptTokens + completionTokens;
    this.tokenStats.requestCount++;
    this.tokenStats.cost += (promptTokens + completionTokens) / 1000 * this.TOKEN_COST_PER_1K;
    this.tokenStats.averageTokensPerRequest = this.tokenStats.totalTokens / this.tokenStats.requestCount;
    
    // 计算效率分数（基于缓存命中率和平均token使用量）
    const cacheHitRate = this.getCacheHitRate();
    const tokenEfficiency = Math.max(0, 100 - (this.tokenStats.averageTokensPerRequest / 1000) * 10);
    this.tokenStats.efficiency = Math.round((cacheHitRate * 0.4 + tokenEfficiency * 0.6));
  }

  /**
   * 获取Token使用统计
   */
  public static getTokenStats(): TokenUsageStats {
    return { ...this.tokenStats };
  }

  /**
   * 获取缓存命中率
   */
  public static getCacheHitRate(): number {
    const designHits = Array.from(this.designCache.values()).reduce((sum, entry) => sum + entry.hitCount, 0);
    const codeHits = Array.from(this.codeCache.values()).reduce((sum, entry) => sum + entry.hitCount, 0);
    const totalHits = designHits + codeHits;
    const totalRequests = this.tokenStats.requestCount + totalHits;
    
    return totalRequests > 0 ? (totalHits / totalRequests) * 100 : 0;
  }

  /**
   * 优化提示词长度
   */
  public static optimizePrompt(prompt: string, maxTokens: number = 4000): string {
    // 简单的提示词优化：移除多余空白和重复内容
    let optimized = prompt
      .replace(/\s+/g, ' ') // 合并多个空格
      .replace(/\n\s*\n/g, '\n') // 移除空行
      .trim();

    // 如果仍然太长，进行更激进的优化
    if (this.estimateTokenCount(optimized) > maxTokens) {
      // 移除示例代码块中的注释
      optimized = optimized.replace(/\/\*[\s\S]*?\*\//g, '');
      optimized = optimized.replace(/\/\/.*$/gm, '');
      
      // 压缩JSON示例
      optimized = optimized.replace(/{\s+/g, '{').replace(/\s+}/g, '}');
      optimized = optimized.replace(/,\s+/g, ',').replace(/:\s+/g, ':');
    }

    return optimized;
  }

  /**
   * 估算Token数量（粗略估算）
   */
  private static estimateTokenCount(text: string): number {
    // 粗略估算：平均每个token约4个字符
    return Math.ceil(text.length / 4);
  }

  /**
   * 生成优化建议
   */
  public static generateOptimizationSuggestions(): OptimizationSuggestion[] {
    const suggestions: OptimizationSuggestion[] = [];
    const stats = this.getTokenStats();
    const cacheHitRate = this.getCacheHitRate();

    // 缓存优化建议
    if (cacheHitRate < 20) {
      suggestions.push({
        type: 'caching',
        description: '缓存命中率较低，建议优化缓存策略',
        potentialSavings: {
          tokens: Math.round(stats.averageTokensPerRequest * 0.3),
          requests: Math.round(stats.requestCount * 0.2),
          timeMs: 2000
        },
        implementation: '增加缓存TTL时间，优化缓存键生成算法'
      });
    }

    // 提示词优化建议
    if (stats.averageTokensPerRequest > 8000) {
      suggestions.push({
        type: 'prompt_optimization',
        description: '平均Token使用量较高，建议优化提示词',
        potentialSavings: {
          tokens: Math.round(stats.averageTokensPerRequest * 0.2),
          requests: 0,
          timeMs: 500
        },
        implementation: '压缩提示词，移除冗余信息，使用更简洁的表达'
      });
    }

    // 模板复用建议
    if (this.designCache.size < 10) {
      suggestions.push({
        type: 'template_reuse',
        description: '设计模板复用率低，建议增加标准模板',
        potentialSavings: {
          tokens: Math.round(stats.averageTokensPerRequest * 0.4),
          requests: Math.round(stats.requestCount * 0.3),
          timeMs: 3000
        },
        implementation: '创建更多标准化设计模板，提高模板匹配率'
      });
    }

    // 批处理建议
    if (stats.requestCount > 100) {
      suggestions.push({
        type: 'batch_processing',
        description: '请求数量较多，建议实现批处理',
        potentialSavings: {
          tokens: Math.round(stats.totalTokens * 0.1),
          requests: Math.round(stats.requestCount * 0.5),
          timeMs: 5000
        },
        implementation: '将多个相似请求合并为批处理，减少API调用次数'
      });
    }

    return suggestions;
  }

  /**
   * 获取性能指标
   */
  public static getPerformanceMetrics(): PerformanceMetrics {
    return {
      averageResponseTime: 2500, // 模拟值，实际应该从请求记录中计算
      successRate: 95, // 模拟值
      cacheHitRate: this.getCacheHitRate(),
      tokenEfficiency: this.tokenStats.efficiency,
      costPerGeneration: this.tokenStats.cost / Math.max(1, this.tokenStats.requestCount)
    };
  }

  /**
   * 重置统计数据
   */
  public static resetStats(): void {
    this.tokenStats = {
      totalTokens: 0,
      promptTokens: 0,
      completionTokens: 0,
      cost: 0,
      requestCount: 0,
      averageTokensPerRequest: 0,
      efficiency: 100
    };
  }

  /**
   * 清空所有缓存
   */
  public static clearAllCaches(): void {
    this.designCache.clear();
    this.codeCache.clear();
    console.log('[EfficiencyOptimizer] 所有缓存已清空');
  }

  /**
   * 获取缓存统计信息
   */
  public static getCacheStats(): {
    designCacheSize: number;
    codeCacheSize: number;
    totalHits: number;
    hitRate: number;
  } {
    const designHits = Array.from(this.designCache.values()).reduce((sum, entry) => sum + entry.hitCount, 0);
    const codeHits = Array.from(this.codeCache.values()).reduce((sum, entry) => sum + entry.hitCount, 0);
    
    return {
      designCacheSize: this.designCache.size,
      codeCacheSize: this.codeCache.size,
      totalHits: designHits + codeHits,
      hitRate: this.getCacheHitRate()
    };
  }
}
