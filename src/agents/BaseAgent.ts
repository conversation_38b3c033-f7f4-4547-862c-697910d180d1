import { StateGraph, Annotation, START, END, MemorySaver } from "@langchain/langgraph";
import { BaseMessage, HumanMessage } from "@langchain/core/messages";
import { CustomClaude4Client, DEFAULT_CLAUDE4_CONFIG, StreamCallback } from './CustomClaude4Client';

// 定义Agent状态结构
export const AgentStateAnnotation = Annotation.Root({
  messages: Annotation<BaseMessage[]>({
    reducer: (x, y) => x.concat(y),
    default: () => []
  }),
  context: Annotation<Record<string, any>>({
    reducer: (x, y) => ({ ...x, ...y }),
    default: () => ({})
  }),
  currentTask: Annotation<string>({
    reducer: (x, y) => y ?? x,
    default: () => ""
  }),
  status: Annotation<"idle" | "thinking" | "working" | "completed" | "error" | "ready">({
    reducer: (x, y) => y ?? x,
    default: () => "idle"
  })
});

export type AgentState = typeof AgentStateAnnotation.State;

// Agent配置接口
export interface AgentConfig {
  name: string;
  role: string;
  systemPrompt: string;
  model?: CustomClaude4Client;
  temperature?: number;
  // maxTokens已移除 - 统一使用环境变量配置
}

// 基础Agent类
export abstract class BaseAgent {
  protected name: string;
  protected role: string;
  protected systemPrompt: string;
  protected model: CustomClaude4Client;
  protected memory: MemorySaver;
  protected graph: any;

  constructor(config: AgentConfig) {
    this.name = config.name;
    this.role = config.role;
    this.systemPrompt = config.systemPrompt;
    this.model = config.model || new CustomClaude4Client({
      ...DEFAULT_CLAUDE4_CONFIG,
      temperature: config.temperature || DEFAULT_CLAUDE4_CONFIG.temperature,
      maxTokens: DEFAULT_CLAUDE4_CONFIG.maxTokens, // 直接使用环境变量配置
      // 添加Agent信息用于token统计
      agentType: this.constructor.name,
      agentName: config.role
    });
    this.memory = new MemorySaver();
    this.initializeGraph();
  }

  // 初始化Agent图结构 - 遵循LangGraph.js最佳实践
  protected initializeGraph(): void {
    const workflow = new StateGraph(AgentStateAnnotation)
      .addNode("execute", this.executeNode.bind(this))
      .addEdge(START, "execute")
      .addEdge("execute", END);

    this.graph = workflow.compile({
      checkpointer: this.memory
    });
  }

  // 直接执行节点 - 遵循LangGraph.js最佳实践，简化工作流程

  // 执行节点 - 具体执行任务
  protected abstract executeNode(state: AgentState): Promise<Partial<AgentState>>;

  // 提取任务描述
  protected extractTask(content: string): string {
    // 简单的任务提取逻辑，子类可以重写
    return content.slice(0, 100) + (content.length > 100 ? "..." : "");
  }

  // 公共方法：运行Agent
  public async run(input: string, config?: { threadId?: string }): Promise<AgentState> {
    const threadId = config?.threadId || `thread_${Date.now()}`;

    const initialState: Partial<AgentState> = {
      messages: [new HumanMessage({ content: input })],
      status: "idle"
    };

    const result = await this.graph.invoke(initialState, {
      configurable: { thread_id: threadId }
    });

    return result;
  }

  // 流式运行Agent
  public async runStream(
    input: string,
    streamCallback: StreamCallback,
    config?: { threadId?: string }
  ): Promise<AgentState> {
    const threadId = config?.threadId || `thread_${Date.now()}`;

    // 设置流式回调到模型客户端
    this.model.setStreamCallback?.(streamCallback);

    const initialState: Partial<AgentState> = {
      messages: [new HumanMessage({ content: input })],
      status: "idle",
      context: {
        lastInput: input,
        threadId: threadId
      },
      currentTask: input
    };

    streamCallback.onStatus?.(`开始执行${this.name}...`);

    const result = await this.graph.invoke(initialState, {
      configurable: { thread_id: threadId }
    });

    streamCallback.onStatus?.(`${this.name}执行完成`);

    return result;
  }

  // 获取Agent状态
  public async getState(threadId: string): Promise<any> {
    return await this.graph.getState({
      configurable: { thread_id: threadId }
    });
  }

  // 获取Agent信息
  public getInfo(): { name: string; role: string } {
    return {
      name: this.name,
      role: this.role
    };
  }
}
