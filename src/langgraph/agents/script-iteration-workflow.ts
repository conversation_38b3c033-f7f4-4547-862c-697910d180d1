import { StateGraph, Annotation, START, END, MemorySaver } from "@langchain/langgraph";
import { BaseMessage, HumanMessage, AIMessage } from "@langchain/core/messages";
import { 
  ScriptIterationAgent, 
  IterationRequest, 
  IterationResult
} from "../../agents/ScriptIterationAgent";

// 脚本迭代工作流状态定义
export const ScriptIterationWorkflowStateAnnotation = Annotation.Root({
  messages: Annotation<BaseMessage[]>({
    reducer: (x, y) => x.concat(y),
    default: () => []
  }),
  // 脚本基础信息
  scriptId: Annotation<string>({
    reducer: (x, y) => y ?? x,
    default: () => ""
  }),
  userRequest: Annotation<string>({
    reducer: (x, y) => y ?? x,
    default: () => ""
  }),
  iterationType: Annotation<"enhance" | "fix" | "refactor" | "extend">({
    reducer: (x, y) => y ?? x,
    default: () => "enhance"
  }),
  
  // 上下文信息（使用LangGraph的长期记忆）
  contextInitialized: Annotation<boolean>({
    reducer: (x, y) => y ?? x,
    default: () => false
  }),
  currentScript: Annotation<{
    content: string;
    version: number;
    functionality: string[];
  } | null>({
    reducer: (x, y) => y ?? x,
    default: () => null
  }),
  
  // 迭代历史（短期记忆，存储在状态中）
  iterationHistory: Annotation<Array<{
    version: number;
    userRequest: string;
    success: boolean;
    changes: string;
    timestamp: string;
  }>>({
    reducer: (x, y) => [...x, ...y],
    default: () => []
  }),
  
  // 当前步骤和状态
  currentStep: Annotation<"context_check" | "analysis" | "iteration" | "validation" | "complete">({
    reducer: (x, y) => y ?? x,
    default: () => "context_check"
  }),
  status: Annotation<"idle" | "thinking" | "working" | "completed" | "error">({
    reducer: (x, y) => y ?? x,
    default: () => "idle"
  }),
  
  // 迭代结果
  iterationResult: Annotation<IterationResult | null>({
    reducer: (x, y) => y ?? x,
    default: () => null
  }),
  
  // 错误信息
  error: Annotation<string | null>({
    reducer: (x, y) => y ?? x,
    default: () => null
  }),
  
  // 元数据
  metadata: Annotation<Record<string, unknown>>({
    reducer: (x, y) => ({ ...x, ...y }),
    default: () => ({})
  })
});

export type ScriptIterationWorkflowState = typeof ScriptIterationWorkflowStateAnnotation.State;

// 创建脚本迭代代理实例
const scriptIterationAgent = new ScriptIterationAgent();

/**
 * 上下文检查节点 - 确保脚本上下文已初始化
 */
async function contextCheckNode(state: ScriptIterationWorkflowState): Promise<Partial<ScriptIterationWorkflowState>> {
  console.log("🔍 [ContextCheck] 检查脚本上下文...");

  try {
    const context = await scriptIterationAgent.getScriptContext(state.scriptId);
    
    if (!context) {
      console.log("⚠️ [ContextCheck] 脚本上下文未初始化");
      return {
        messages: [...state.messages, 
          new AIMessage("⚠️ 脚本上下文未初始化，需要先初始化脚本")
        ],
        contextInitialized: false,
        currentStep: "analysis",
        status: "error",
        error: "脚本上下文未初始化"
      };
    }

    console.log(`✅ [ContextCheck] 脚本上下文已存在: v${context.currentScript.version}`);

    return {
      messages: [...state.messages, 
        new AIMessage(`✅ 脚本上下文已就绪: v${context.currentScript.version}`)
      ],
      contextInitialized: true,
      currentScript: {
        content: context.currentScript.content,
        version: context.currentScript.version,
        functionality: context.currentScript.functionality
      },
      iterationHistory: context.iterationHistory.slice(-5).map(h => ({
        version: h.version,
        userRequest: h.userRequest,
        success: h.success,
        changes: h.changes,
        timestamp: h.timestamp.toISOString()
      })),
      currentStep: "analysis",
      status: "thinking"
    };

  } catch (error) {
    console.error('[ContextCheck] 检查失败:', error);
    return {
      messages: [...state.messages, 
        new AIMessage(`❌ 上下文检查失败: ${error instanceof Error ? error.message : '未知错误'}`)
      ],
      status: "error",
      error: error instanceof Error ? error.message : '未知错误'
    };
  }
}

/**
 * 需求分析节点 - 分析用户迭代需求
 */
async function analysisNode(state: ScriptIterationWorkflowState): Promise<Partial<ScriptIterationWorkflowState>> {
  console.log("📊 [Analysis] 分析迭代需求...");

  try {
    if (!state.contextInitialized) {
      throw new Error('脚本上下文未初始化');
    }

    // 简单的需求分析逻辑
    const analysisResult = {
      complexity: state.userRequest.length > 100 ? 'high' : 'medium',
      estimatedTime: state.userRequest.includes('重构') ? '长' : '短',
      riskLevel: state.iterationType === 'refactor' ? 'high' : 'low'
    };

    console.log(`📊 [Analysis] 需求分析完成:`, analysisResult);

    return {
      messages: [...state.messages, 
        new AIMessage(`📊 需求分析完成: 复杂度${analysisResult.complexity}, 预计${analysisResult.estimatedTime}时间完成`)
      ],
      currentStep: "iteration",
      status: "working",
      metadata: {
        ...state.metadata,
        analysis: analysisResult,
        analysisTime: new Date().toISOString()
      }
    };

  } catch (error) {
    console.error('[Analysis] 分析失败:', error);
    return {
      messages: [...state.messages, 
        new AIMessage(`❌ 需求分析失败: ${error instanceof Error ? error.message : '未知错误'}`)
      ],
      status: "error",
      error: error instanceof Error ? error.message : '未知错误'
    };
  }
}

/**
 * 脚本迭代节点 - 执行实际的脚本迭代
 */
async function iterationNode(state: ScriptIterationWorkflowState): Promise<Partial<ScriptIterationWorkflowState>> {
  console.log("🔧 [Iteration] 开始执行脚本迭代...");

  try {
    if (!state.contextInitialized) {
      throw new Error('脚本上下文未初始化');
    }

    // 构建迭代请求
    const iterationRequest: IterationRequest = {
      userRequest: state.userRequest,
      scriptId: state.scriptId,
      iterationType: state.iterationType,
      priority: 'medium',
      expectedBehavior: `用户期望: ${state.userRequest}`
    };

    console.log(`🔧 [Iteration] 执行迭代请求:`, iterationRequest);

    // 执行迭代
    const result = await scriptIterationAgent.iterateScript(iterationRequest);

    if (result.success) {
      console.log(`✅ [Iteration] 迭代成功: v${result.newVersion}`);

      return {
        messages: [...state.messages, 
          new HumanMessage(state.userRequest),
          new AIMessage(`🔧 脚本迭代完成: v${result.newVersion}\n\n变更说明: ${result.updatedScript.changes}\n修改原因: ${result.updatedScript.reasoning}`)
        ],
        currentScript: {
          content: result.updatedScript.content,
          version: result.newVersion,
          functionality: [] // 将在验证节点中更新
        },
        iterationHistory: [...state.iterationHistory, {
          version: result.newVersion,
          userRequest: state.userRequest,
          success: true,
          changes: result.updatedScript.changes,
          timestamp: new Date().toISOString()
        }],
        iterationResult: result,
        currentStep: "validation",
        status: "working"
      };
    } else {
      console.log(`❌ [Iteration] 迭代失败: ${result.errorMessage}`);

      return {
        messages: [...state.messages, 
          new AIMessage(`❌ 脚本迭代失败: ${result.errorMessage || '未知错误'}`)
        ],
        iterationHistory: [...state.iterationHistory, {
          version: state.currentScript?.version || 0,
          userRequest: state.userRequest,
          success: false,
          changes: '',
          timestamp: new Date().toISOString()
        }],
        iterationResult: result,
        status: "error",
        error: result.errorMessage || '迭代失败'
      };
    }

  } catch (error) {
    console.error('[Iteration] 迭代失败:', error);
    return {
      messages: [...state.messages, 
        new AIMessage(`❌ 脚本迭代失败: ${error instanceof Error ? error.message : '未知错误'}`)
      ],
      status: "error",
      error: error instanceof Error ? error.message : '未知错误'
    };
  }
}

/**
 * 验证节点 - 验证迭代结果
 */
async function validationNode(state: ScriptIterationWorkflowState): Promise<Partial<ScriptIterationWorkflowState>> {
  console.log("✅ [Validation] 验证迭代结果...");

  try {
    if (!state.iterationResult || !state.iterationResult.success) {
      throw new Error('没有可验证的迭代结果');
    }

    // 简单的验证逻辑
    const script = state.iterationResult.updatedScript.content;
    const validationResults = {
      hasExecuteFunction: script.includes('function executeScript'),
      hasErrorHandling: script.includes('try') && script.includes('catch'),
      usesNodeRegistry: script.includes('NodeRegistry'),
      codeLength: script.length,
      estimatedComplexity: script.split('\n').length > 30 ? 'high' : 'medium'
    };

    const isValid = validationResults.hasExecuteFunction && 
                   validationResults.usesNodeRegistry &&
                   validationResults.codeLength > 100;

    console.log(`✅ [Validation] 验证结果:`, { isValid, ...validationResults });

    if (isValid) {
      return {
        messages: [...state.messages, 
          new AIMessage(`✅ 脚本验证通过: 代码结构完整，符合规范`)
        ],
        currentStep: "complete",
        status: "completed",
        metadata: {
          ...state.metadata,
          validation: validationResults,
          validationTime: new Date().toISOString(),
          recommendations: state.iterationResult.recommendations
        }
      };
    } else {
      return {
        messages: [...state.messages, 
          new AIMessage(`⚠️ 脚本验证警告: 代码可能存在问题，但已完成迭代`)
        ],
        currentStep: "complete",
        status: "completed",
        metadata: {
          ...state.metadata,
          validation: validationResults,
          validationTime: new Date().toISOString(),
          validationWarnings: '代码结构可能不完整'
        }
      };
    }

  } catch (error) {
    console.error('[Validation] 验证失败:', error);
    return {
      messages: [...state.messages, 
        new AIMessage(`❌ 脚本验证失败: ${error instanceof Error ? error.message : '未知错误'}`)
      ],
      status: "error",
      error: error instanceof Error ? error.message : '未知错误'
    };
  }
}

/**
 * 条件边 - 决定下一个节点
 */
function shouldContinue(state: ScriptIterationWorkflowState): string {
  if (state.status === "error") {
    return END;
  }

  switch (state.currentStep) {
    case "context_check":
      return state.contextInitialized ? "analysis" : END;
    case "analysis":
      return "iteration";
    case "iteration":
      return state.iterationResult?.success ? "validation" : END;
    case "validation":
    case "complete":
      return END;
    default:
      return END;
  }
}

// 创建脚本迭代工作流
const scriptIterationWorkflow = new StateGraph(ScriptIterationWorkflowStateAnnotation)
  .addNode("context_check", contextCheckNode)
  .addNode("analysis", analysisNode)
  .addNode("iteration", iterationNode)
  .addNode("validation", validationNode)
  .addEdge(START, "context_check")
  .addConditionalEdges("context_check", shouldContinue)
  .addConditionalEdges("analysis", shouldContinue)
  .addConditionalEdges("iteration", shouldContinue)
  .addConditionalEdges("validation", shouldContinue);

// 编译工作流
export const scriptIterationGraph = scriptIterationWorkflow.compile({
  checkpointer: new MemorySaver(), // 使用内存保存器实现持久化
  interruptBefore: [], // 可以在特定节点前暂停
  interruptAfter: []   // 可以在特定节点后暂停
});

// 导出单例代理实例用于外部访问
export { scriptIterationAgent }; 