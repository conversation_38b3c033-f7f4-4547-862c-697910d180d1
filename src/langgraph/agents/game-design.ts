import { StateGraph, Annotation, START, END, MemorySaver } from "@langchain/langgraph";
import { BaseMessage, HumanMessage, AIMessage } from "@langchain/core/messages";
import { GameDesignAgent, GameDesign } from "../../agents/GameDesignAgent";

// 简化的游戏设计工作流状态
export const GameDesignStateAnnotation = Annotation.Root({
  messages: Annotation<BaseMessage[]>({
    reducer: (x, y) => x.concat(y),
    default: () => []
  }),
  userRequirement: Annotation<string>({
    reducer: (x, y) => y ?? x,
    default: () => ""
  }),
  gameDesign: Annotation<GameDesign | Record<string, unknown>>({
    reducer: (x, y) => ({ ...x, ...y }),
    default: () => ({})
  }),
  status: Annotation<"idle" | "working" | "completed" | "error">({
    reducer: (x, y) => y ?? x,
    default: () => "idle"
  }),
  metadata: Annotation<Record<string, unknown>>({
    reducer: (x, y) => ({ ...x, ...y }),
    default: () => ({})
  })
});

export type GameDesignState = typeof GameDesignStateAnnotation.State;

// 初始化Agent实例
const gameDesignAgent = new GameDesignAgent();

// 简化的游戏设计节点 - 直接调用GameDesignAgent
async function designGameNode(state: GameDesignState): Promise<Partial<GameDesignState>> {
  console.log("🎮 开始游戏设计...");

  try {
    // 直接使用GameDesignAgent进行完整的游戏设计
    const result = await gameDesignAgent.run(state.userRequirement);

    return {
      messages: [...state.messages, new HumanMessage(state.userRequirement), new AIMessage("游戏设计完成")],
      gameDesign: result.context?.gameDesign || {},
      status: "completed",
      metadata: {
        ...state.metadata,
        designTime: new Date().toISOString(),
        designTokens: 0
      }
    };
  } catch (error) {
    console.error("游戏设计失败:", error);
    return {
      status: "error",
      messages: [...state.messages, new AIMessage(`游戏设计失败: ${error}`)],
      metadata: {
        ...state.metadata,
        error: error instanceof Error ? error.message : String(error)
      }
    };
  }
}

// 创建简化的游戏设计工作流
const gameDesignWorkflow = new StateGraph(GameDesignStateAnnotation)
  .addNode("design_game", designGameNode)
  .addEdge(START, "design_game")
  .addEdge("design_game", END);

// 编译工作流
export const gameDesignGraph = gameDesignWorkflow.compile({
  checkpointer: new MemorySaver(),
});


