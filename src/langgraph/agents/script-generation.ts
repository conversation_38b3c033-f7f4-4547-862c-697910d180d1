import { StateGraph, Annotation, START, END, MemorySaver } from "@langchain/langgraph";
import { BaseMessage, HumanMessage, AIMessage } from "@langchain/core/messages";
import { CodeGenerationAgent, GeneratedScript } from "../../agents/CodeGenerationAgent";

// 脚本生成工作流状态定义
export const ScriptGenerationStateAnnotation = Annotation.Root({
  messages: Annotation<BaseMessage[]>({
    reducer: (x, y) => x.concat(y),
    default: () => []
  }),
  userRequirement: Annotation<string>({
    reducer: (x, y) => y ?? x,
    default: () => ""
  }),
  sceneContext: Annotation<Record<string, unknown> | null>({
    reducer: (x, y) => y ?? x,
    default: () => null
  }),
  taskType: Annotation<string>({
    reducer: (x, y) => y ?? x,
    default: () => "script_generation"
  }),
  currentStep: Annotation<"analysis" | "development" | "complete">({
    reducer: (x, y) => y ?? x,
    default: () => "analysis"
  }),
  generatedScript: Annotation<GeneratedScript | null>({
    reducer: (x, y) => y ?? x,
    default: () => null
  }),
  status: Annotation<"idle" | "thinking" | "working" | "completed" | "error">({
    reducer: (x, y) => y ?? x,
    default: () => "idle"
  }),
  metadata: Annotation<Record<string, unknown>>({
    reducer: (x, y) => ({ ...x, ...y }),
    default: () => ({})
  })
});

// 定义状态类型
export type ScriptGenerationState = typeof ScriptGenerationStateAnnotation.State;

// 初始化代码生成Agent实例
const codeGenerationAgent = new CodeGenerationAgent();

/**
 * 项目管理节点 - 需求分析和任务规划
 */
async function projectManagerNode(state: ScriptGenerationState): Promise<Partial<ScriptGenerationState>> {
  console.log("📋 [PM] 开始分析用户需求...");

  try {
    // 分析用户需求和场景上下文
    const analysis = analyzeUserRequirement(state.userRequirement, state.sceneContext);
    
    return {
      messages: [...state.messages, new AIMessage(`📋 需求分析完成：${analysis.summary}`)],
      currentStep: "development",
      status: "working",
      metadata: {
        ...state.metadata,
        analysis,
        analysisTime: new Date().toISOString()
      }
    };
  } catch (error) {
    console.error("📋 [PM] 需求分析失败:", error);
    return {
      status: "error",
      metadata: {
        ...state.metadata,
        error: error instanceof Error ? error.message : "需求分析失败"
      }
    };
  }
}

/**
 * 脚本开发节点 - 实际代码生成
 */
async function scriptDevelopmentNode(state: ScriptGenerationState): Promise<Partial<ScriptGenerationState>> {
  console.log("🔧 [Dev] 开始生成脚本代码...");

  try {
    // 构建场景上下文
    const sceneContext = state.sceneContext ? {
      totalNodes: (state.sceneContext.totalNodes as number) || 0,
      nodesByType: (state.sceneContext.nodesByType as Record<string, number>) || {},
      nodes: (state.sceneContext.nodes as Array<{
        id: string;
        name: string;
        type: string;
        position: { x: number; y: number; z: number };
        rotation: { x: number; y: number; z: number };
        scaling: { x: number; y: number; z: number };
      }>) || [],
      selectedNode: (state.sceneContext.selectedNode as {
        id: string;
        name: string;
        type: string;
      } | null) || null,
      availableAssets: (state.sceneContext.availableAssets as {
        models: Array<{ name: string; path: string }>;
        textures: Array<{ name: string; path: string }>;
      }) || { models: [], textures: [] }
    } : null;

    // 使用CodeGenerationAgent生成脚本
    const generatedScript = await codeGenerationAgent.generateScript(
      state.userRequirement,
      sceneContext
    );

    return {
      messages: [...state.messages, 
        new HumanMessage(state.userRequirement),
        new AIMessage(`🔧 脚本生成完成：${generatedScript.name}`)
      ],
      generatedScript,
      currentStep: "complete",
      status: "completed",
      metadata: {
        ...state.metadata,
        generationTime: new Date().toISOString(),
        scriptLength: generatedScript.content.length,
        functionType: generatedScript.metadata.functionType
      }
    };
  } catch (error) {
    console.error("🔧 [Dev] 脚本生成失败:", error);
    return {
      status: "error",
      messages: [...state.messages, new AIMessage(`❌ 脚本生成失败: ${error instanceof Error ? error.message : '未知错误'}`)],
      metadata: {
        ...state.metadata,
        error: error instanceof Error ? error.message : "脚本生成失败"
      }
    };
  }
}

/**
 * 条件边 - 决定工作流的下一步
 */
function shouldContinue(state: ScriptGenerationState): "script_development" | typeof END {
  if (state.status === "error") {
    return END;
  }
  
  if (state.currentStep === "analysis") {
    return "script_development";
  }
  
  if (state.currentStep === "complete") {
    return END;
  }

  return "script_development";
}

/**
 * 分析用户需求
 */
function analyzeUserRequirement(userRequirement: string, sceneContext: Record<string, unknown> | null) {
  const keywords = extractKeywords(userRequirement);
  const intentType = classifyIntent(userRequirement);
  const complexity = assessComplexity(userRequirement, sceneContext);
  
  return {
    summary: `分析了用户需求"${userRequirement.substring(0, 50)}..."，识别为${intentType}类型任务，复杂度：${complexity}`,
    keywords,
    intentType,
    complexity,
    hasSceneContext: !!sceneContext,
    contextInfo: sceneContext ? {
      nodeCount: sceneContext.totalNodes || 0,
      hasSelectedNode: !!sceneContext.selectedNode,
      availableAssets: sceneContext.availableAssets || {}
    } : null
  };
}

/**
 * 提取关键词
 */
function extractKeywords(text: string): string[] {
  const keywords: string[] = [];
  const lowerText = text.toLowerCase();
  
  // 检测动作关键词
  const actionKeywords = ['移动', '旋转', '缩放', '动画', '播放', '停止', '隐藏', '显示', '点击', '拖拽'];
  actionKeywords.forEach(keyword => {
    if (lowerText.includes(keyword)) {
      keywords.push(keyword);
    }
  });
  
  // 检测对象关键词
  const objectKeywords = ['玩家', '敌人', '道具', '相机', '光源', '模型', '材质', '纹理'];
  objectKeywords.forEach(keyword => {
    if (lowerText.includes(keyword)) {
      keywords.push(keyword);
    }
  });
  
  return keywords;
}

/**
 * 分类意图类型
 */
function classifyIntent(text: string): string {
  const lowerText = text.toLowerCase();
  
  if (lowerText.includes('动画') || lowerText.includes('播放') || lowerText.includes('移动')) {
    return 'animation';
  }
  if (lowerText.includes('点击') || lowerText.includes('交互') || lowerText.includes('按键')) {
    return 'interaction';
  }
  if (lowerText.includes('行为') || lowerText.includes('逻辑') || lowerText.includes('规则')) {
    return 'behavior';
  }
  if (lowerText.includes('效果') || lowerText.includes('特效') || lowerText.includes('粒子')) {
    return 'effect';
  }
  
  return 'utility';
}

/**
 * 评估复杂度
 */
function assessComplexity(text: string, sceneContext: Record<string, unknown> | null): string {
  let score = 0;
  
  // 基于文本长度
  score += Math.min(text.length / 50, 2);
  
  // 基于关键词数量
  const keywords = extractKeywords(text);
  score += keywords.length * 0.5;
  
  // 基于场景上下文
  if (sceneContext) {
    const nodeCount = sceneContext.totalNodes as number || 0;
    if (nodeCount > 5) score += 1;
    if (sceneContext.selectedNode) score += 0.5;
  }
  
  if (score < 2) return '简单';
  if (score < 4) return '中等';
  return '复杂';
}

// 创建脚本生成工作流
const scriptGenerationWorkflow = new StateGraph(ScriptGenerationStateAnnotation)
  .addNode("pm_analysis", projectManagerNode)
  .addNode("script_development", scriptDevelopmentNode)
  .addEdge(START, "pm_analysis")
  .addConditionalEdges("pm_analysis", shouldContinue)
  .addConditionalEdges("script_development", shouldContinue);

// 编译工作流
export const scriptGenerationGraph = scriptGenerationWorkflow.compile({
  checkpointer: new MemorySaver(),
}); 