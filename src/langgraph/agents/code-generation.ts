import { StateGraph, Annotation, START, END, MemorySaver } from "@langchain/langgraph";
import { BaseMessage, HumanMessage, AIMessage } from "@langchain/core/messages";
import { CodeGenerationAgent, GeneratedCode } from "../../agents/CodeGenerationAgent";
import { GameDesign } from "../../agents/GameDesignAgent";

// 简化的代码生成工作流状态 - 使用更简单的类型定义
export const CodeGenerationStateAnnotation = Annotation.Root({
  messages: Annotation<BaseMessage[]>({
    reducer: (x, y) => x.concat(y),
    default: () => []
  }),
  gameDesign: Annotation<any>({
    reducer: (x, y) => ({ ...x, ...y }),
    default: () => ({})
  }),
  generatedCode: Annotation<any>({
    reducer: (x, y) => ({ ...x, ...y }),
    default: () => ({
      component: "",
      fileName: "",
      dependencies: [],
      assets: {
        textures: [],
        models: [],
        sounds: [],
        other: []
      },
      metadata: {
        componentName: "",
        gameType: "",
        description: ""
      }
    })
  }),
  status: Annotation<string>({
    reducer: (x, y) => y ?? x,
    default: () => "idle"
  }),
  metadata: Annotation<any>({
    reducer: (x, y) => ({ ...x, ...y }),
    default: () => ({})
  })
});

// 简化的状态类型定义
export interface CodeGenerationState {
  messages: BaseMessage[];
  gameDesign: any;
  generatedCode: any;
  status: string;
  metadata: any;
}

// 初始化Agent实例
const codeGenerationAgent = new CodeGenerationAgent();

// 简化的代码生成节点 - 直接调用CodeGenerationAgent
async function generateCodeNode(state: CodeGenerationState): Promise<Partial<CodeGenerationState>> {
  console.log("💻 开始代码生成...");

  try {
    // 直接使用CodeGenerationAgent进行完整的代码生成
    const result = await codeGenerationAgent.run(JSON.stringify(state.gameDesign));

    return {
      messages: [...state.messages, new HumanMessage(JSON.stringify(state.gameDesign)), new AIMessage("代码生成完成")],
      generatedCode: result.context?.generatedCode || {
        html: "",
        css: "",
        javascript: "",
        assets: {
          textures: [],
          models: [],
          sounds: []
        },
        dependencies: []
      },
      status: "completed",
      metadata: {
        ...state.metadata,
        generationTime: new Date().toISOString(),
        generationTokens: 0
      }
    };
  } catch (error) {
    console.error("代码生成失败:", error);
    return {
      status: "error",
      messages: [...state.messages, new AIMessage(`代码生成失败: ${error}`)],
      metadata: {
        ...state.metadata,
        error: error instanceof Error ? error.message : String(error)
      }
    };
  }
}

// 创建简化的代码生成工作流
const codeGenerationWorkflow = new StateGraph(CodeGenerationStateAnnotation)
  .addNode("generate_code", generateCodeNode)
  .addEdge(START, "generate_code")
  .addEdge("generate_code", END);

// 编译工作流
export const codeGenerationGraph = codeGenerationWorkflow.compile({
  checkpointer: new MemorySaver(),
});


