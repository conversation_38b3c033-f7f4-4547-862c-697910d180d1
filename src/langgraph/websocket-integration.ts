/**
 * LangGraph状态管理模块
 * 管理LangGraph工作流执行状态（已移除WebSocket依赖）
 */

// 注意：WebSocket功能已迁移到LangGraph SDK的原生流式功能

// LangGraph状态到WebSocket状态的映射
export function mapLangGraphStatusToWebSocket(status: string): 'idle' | 'working' | 'completed' | 'error' {
  switch (status) {
    case 'idle':
    case 'ready':
      return 'idle'
    case 'thinking':
    case 'working':
    case 'analyzing':
    case 'designing':
    case 'planning':
    case 'generating':
    case 'optimizing':
    case 'testing':
    case 'validating':
      return 'working'
    case 'completed':
    case 'complete':
      return 'completed'
    case 'error':
      return 'error'
    default:
      return 'working'
  }
}

// 获取Agent友好名称
export function getAgentFriendlyName(agentId: string): string {
  switch (agentId) {
    case 'playable_gen_workflow':
      return '游戏生成工作流'
    case 'game_design_agent':
      return '游戏设计Agent'
    case 'code_generation_agent':
      return '代码生成Agent'
    default:
      return agentId.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
  }
}

// 获取当前步骤的友好名称
export function getStepFriendlyName(step: string): string {
  const stepMap: Record<string, string> = {
    // 主工作流步骤
    'design': '游戏设计',
    'code': '代码生成',
    'review': '审查验证',
    'complete': '完成',
    
    // 游戏设计Agent步骤
    'analyzing': '需求分析',
    'designing': '方案设计',
    'validating': '设计验证',
    
    // 代码生成Agent步骤
    'planning': '代码规划',
    'generating': '代码生成',
    'optimizing': '代码优化',
    'testing': '代码测试',
    
    // 通用步骤
    'idle': '空闲',
    'thinking': '思考中',
    'working': '工作中',
    'completed': '已完成',
    'error': '错误'
  }
  
  return stepMap[step] || step
}

// 计算进度百分比
export function calculateProgress(currentStep: string, agentType: string): number {
  const progressMap: Record<string, Record<string, number>> = {
    'playable_gen_workflow': {
      'design': 25,
      'code': 75,
      'review': 90,
      'complete': 100
    },
    'game_design_agent': {
      'analyzing': 30,
      'designing': 70,
      'validating': 90,
      'completed': 100
    },
    'code_generation_agent': {
      'planning': 20,
      'generating': 60,
      'optimizing': 80,
      'testing': 95,
      'completed': 100
    }
  }
  
  return progressMap[agentType]?.[currentStep] || 0
}

// WebSocket状态广播器类
export class LangGraphWebSocketBroadcaster {
  private activeAgents = new Map<string, {
    agentId: string
    agentName: string
    status: string
    currentTask: string
    startTime: number
  }>()

  // 广播Agent状态更新
  async broadcastAgentStatus(agentId: string, status: string, currentTask?: string) {
    try {
      const agentName = getAgentFriendlyName(agentId)
      const wsStatus = mapLangGraphStatusToWebSocket(status)
      const friendlyTask = currentTask ? getStepFriendlyName(currentTask) : undefined
      
      // 更新内部状态
      if (wsStatus === 'working') {
        this.activeAgents.set(agentId, {
          agentId,
          agentName,
          status,
          currentTask: currentTask || '',
          startTime: Date.now()
        })
      } else if (wsStatus === 'completed' || wsStatus === 'error') {
        this.activeAgents.delete(agentId)
      }

      // 状态更新（WebSocket广播已移除，使用LangGraph SDK流式功能）
      console.log(`[LangGraph] Agent状态更新: ${agentName} - ${wsStatus}`, {
        agentId,
        agentName,
        status: wsStatus,
        currentTask: friendlyTask,
        progress: calculateProgress(status, agentId)
      })

      console.log(`[LangGraph] Agent状态记录: ${agentName} - ${wsStatus}`)
    } catch (error) {
      console.error('[LangGraph] 记录Agent状态失败:', error)
    }
  }

  // 记录进度更新
  async broadcastProgress(taskId: string, taskName: string, progress: number, status: string, details?: string) {
    try {
      // 进度记录（WebSocket广播已移除，使用LangGraph SDK流式功能）
      console.log(`[LangGraph] 进度更新: ${taskName} - ${progress}%`, {
        taskId,
        taskName,
        progress,
        status: getStepFriendlyName(status),
        details,
        timestamp: Date.now()
      })
    } catch (error) {
      console.error('[LangGraph] 记录进度失败:', error)
    }
  }

  // 广播工作流开始
  async broadcastWorkflowStart(workflowId: string, userRequirement: string) {
    const taskName = `${getAgentFriendlyName(workflowId)}执行`
    await this.broadcastProgress(
      workflowId,
      taskName,
      0,
      'started',
      `开始处理需求: ${userRequirement.substring(0, 50)}...`
    )
    
    await this.broadcastAgentStatus(workflowId, 'working', 'started')
  }

  // 广播工作流完成
  async broadcastWorkflowComplete(workflowId: string, result?: any) {
    const taskName = `${getAgentFriendlyName(workflowId)}执行`
    await this.broadcastProgress(
      workflowId,
      taskName,
      100,
      'completed',
      '工作流执行完成'
    )
    
    await this.broadcastAgentStatus(workflowId, 'completed')
  }

  // 广播工作流错误
  async broadcastWorkflowError(workflowId: string, error: Error) {
    const taskName = `${getAgentFriendlyName(workflowId)}执行`
    await this.broadcastProgress(
      workflowId,
      taskName,
      0,
      'error',
      `执行失败: ${error.message}`
    )
    
    await this.broadcastAgentStatus(workflowId, 'error', 'error')
  }

  // 广播节点执行开始
  async broadcastNodeStart(workflowId: string, nodeName: string) {
    const friendlyNodeName = getStepFriendlyName(nodeName)
    const taskName = `${getAgentFriendlyName(workflowId)} - ${friendlyNodeName}`
    
    await this.broadcastProgress(
      `${workflowId}_${nodeName}`,
      taskName,
      0,
      'working',
      `开始执行: ${friendlyNodeName}`
    )
    
    await this.broadcastAgentStatus(workflowId, 'working', nodeName)
  }

  // 广播节点执行完成
  async broadcastNodeComplete(workflowId: string, nodeName: string, result?: any) {
    const friendlyNodeName = getStepFriendlyName(nodeName)
    const taskName = `${getAgentFriendlyName(workflowId)} - ${friendlyNodeName}`
    
    await this.broadcastProgress(
      `${workflowId}_${nodeName}`,
      taskName,
      100,
      'completed',
      `完成: ${friendlyNodeName}`
    )
  }

  // 获取活跃Agent列表
  getActiveAgents() {
    return Array.from(this.activeAgents.values())
  }

  // 清理过期的Agent状态
  cleanupExpiredAgents(timeoutMs = 300000) { // 5分钟超时
    const now = Date.now()
    for (const [agentId, agent] of this.activeAgents.entries()) {
      if (now - agent.startTime > timeoutMs) {
        this.activeAgents.delete(agentId)
        this.broadcastAgentStatus(agentId, 'error', 'timeout')
      }
    }
  }
}

// 全局广播器实例
export const langGraphBroadcaster = new LangGraphWebSocketBroadcaster()

// 定期清理过期Agent
if (typeof window === 'undefined') {
  setInterval(() => {
    langGraphBroadcaster.cleanupExpiredAgents()
  }, 60000) // 每分钟检查一次
}

// 工作流执行包装器
export function withWebSocketBroadcast<T extends (...args: any[]) => Promise<any>>(
  workflowId: string,
  workflowFn: T
): T {
  return (async (...args: any[]) => {
    try {
      // 广播开始
      const userRequirement = args[0]?.userRequirement || '未知需求'
      await langGraphBroadcaster.broadcastWorkflowStart(workflowId, userRequirement)
      
      // 执行工作流
      const result = await workflowFn(...args)
      
      // 广播完成
      await langGraphBroadcaster.broadcastWorkflowComplete(workflowId, result)
      
      return result
    } catch (error) {
      // 广播错误
      await langGraphBroadcaster.broadcastWorkflowError(workflowId, error as Error)
      throw error
    }
  }) as T
}

// 节点执行包装器
export function withNodeBroadcast<T extends (...args: any[]) => Promise<any>>(
  workflowId: string,
  nodeName: string,
  nodeFn: T
): T {
  return (async (...args: any[]) => {
    try {
      // 广播节点开始
      await langGraphBroadcaster.broadcastNodeStart(workflowId, nodeName)
      
      // 执行节点
      const result = await nodeFn(...args)
      
      // 广播节点完成
      await langGraphBroadcaster.broadcastNodeComplete(workflowId, nodeName, result)
      
      return result
    } catch (error) {
      // 节点执行失败
      await langGraphBroadcaster.broadcastProgress(
        `${workflowId}_${nodeName}`,
        `${getAgentFriendlyName(workflowId)} - ${getStepFriendlyName(nodeName)}`,
        0,
        'error',
        `执行失败: ${(error as Error).message}`
      )
      throw error
    }
  }) as T
}
