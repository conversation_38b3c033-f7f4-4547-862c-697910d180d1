import { StateGraph, Annotation, START, END, MemorySaver } from "@langchain/langgraph";
import { BaseMessage, HumanMessage, AIMessage } from "@langchain/core/messages";
// import { GameDesignAgent } from "../agents/GameDesignAgent"; // 移除设计师Agent
import { CodeGenerationAgent } from "../agents/CodeGenerationAgent";
import { withNodeBroadcast, langGraphBroadcaster } from "./websocket-integration";

// 定义PlayableGen工作流状态
export const PlayableGenStateAnnotation = Annotation.Root({
  messages: Annotation<BaseMessage[]>({
    reducer: (x, y) => x.concat(y),
    default: () => []
  }),
  userRequirement: Annotation<string>({
    reducer: (x, y) => y ?? x,
    default: () => ""
  }),
  // gameDesign: Annotation<Record<string, any>>({ // 移除游戏设计状态
  //   reducer: (x, y) => ({ ...x, ...y }),
  //   default: () => ({})
  // }),
  generatedCode: Annotation<{
    html: string;
    css: string;
    javascript: string;
  }>({
    reducer: (x, y) => ({ ...x, ...y }),
    default: () => ({ html: "", css: "", javascript: "" })
  }),
  currentStep: Annotation<"code" | "review" | "complete">({ // 移除design步骤
    reducer: (x, y) => y ?? x,
    default: () => "code"
  }),
  status: Annotation<"idle" | "thinking" | "working" | "completed" | "error">({
    reducer: (x, y) => y ?? x,
    default: () => "idle"
  }),
  metadata: Annotation<Record<string, any>>({
    reducer: (x, y) => ({ ...x, ...y }),
    default: () => ({})
  })
});

export type PlayableGenState = typeof PlayableGenStateAnnotation.State;

// 初始化Agent实例
// const gameDesignAgent = new GameDesignAgent(); // 移除设计师Agent实例
const codeGenerationAgent = new CodeGenerationAgent();

// 移除游戏设计节点，直接使用代码生成节点

// 代码生成节点 - 直接处理用户需求
const codeGenerationNode = withNodeBroadcast(
  "playable_gen_workflow",
  "code",
  async (state: PlayableGenState): Promise<Partial<PlayableGenState>> => {
    console.log("💻 开始代码生成阶段...");

    try {
      // 直接使用用户需求作为输入，让代码生成器自己处理
      const result = await codeGenerationAgent.run(state.userRequirement);
      const generatedCode = result.context?.generatedCode || { html: "", css: "", javascript: "" };

      return {
        messages: [...state.messages, new HumanMessage(state.userRequirement), new AIMessage("代码生成完成")],
        generatedCode,
        currentStep: "review",
        status: "working",
        metadata: {
          ...state.metadata,
          codeTime: new Date().toISOString(),
          codeTokens: 0
        }
      };
    } catch (error) {
      console.error("代码生成阶段出错:", error);
      return {
        status: "error",
        metadata: {
          ...state.metadata,
          error: error instanceof Error ? error.message : "未知错误"
        }
      };
    }
  }
);

// 审查节点
async function reviewNode(state: PlayableGenState): Promise<Partial<PlayableGenState>> {
  console.log("🔍 开始审查阶段...");
  
  // 简单的审查逻辑，检查生成的代码是否完整
  const { html, css, javascript } = state.generatedCode;
  const isComplete = html.length > 0 && css.length > 0 && javascript.length > 0;
  
  if (isComplete) {
    return {
      currentStep: "complete",
      status: "completed",
      messages: [...state.messages, new AIMessage("审查通过，游戏生成完成！")],
      metadata: {
        ...state.metadata,
        reviewTime: new Date().toISOString(),
        totalTime: Date.now() - new Date(state.metadata.startTime || Date.now()).getTime()
      }
    };
  } else {
    return {
      currentStep: "code",
      status: "working",
      messages: [...state.messages, new AIMessage("审查发现问题，重新生成代码...")],
      metadata: {
        ...state.metadata,
        reviewTime: new Date().toISOString(),
        retryCount: (state.metadata.retryCount || 0) + 1
      }
    };
  }
}

// 条件路由函数
function shouldContinue(state: PlayableGenState): string {
  if (state.status === "error") {
    return END;
  }

  switch (state.currentStep) {
    case "code":
      return "review";
    case "review":
      return state.status === "completed" ? END : "code_generation";
    case "complete":
      return END;
    default:
      return END;
  }
}

// 创建工作流图 - 移除游戏设计节点
const workflow = new StateGraph(PlayableGenStateAnnotation)
  .addNode("code_generation", codeGenerationNode)
  .addNode("review", reviewNode)
  .addEdge(START, "code_generation") // 直接从开始到代码生成
  .addConditionalEdges("code_generation", shouldContinue)
  .addConditionalEdges("review", shouldContinue);

// 添加内存持久化
const memory = new MemorySaver();

// 编译工作流
export const playableGenWorkflow = workflow.compile({
  checkpointer: memory,
  interruptBefore: [], // 可以在这里添加需要人工干预的节点
  interruptAfter: []   // 可以在这里添加需要人工确认的节点
});

// 导出类型和工具函数

// 可视化工具函数
export async function visualizeWorkflow() {
  try {
    const graph = playableGenWorkflow.getGraph();
    const image = await graph.drawMermaidPng();
    return image;
  } catch (error) {
    console.error("可视化工作流失败:", error);
    return null;
  }
}
