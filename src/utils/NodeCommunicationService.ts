import * as THREE from 'three';
import { NodeRegistry } from './NodeRegistry';
import {
  GameNodeProperties,
  GameNodeType,
  MeshNodeProperties,
  LightNodeProperties,
  CameraNodeProperties,
  NodeChangeListener
} from '../types/NodeTypes';

/**
 * 节点通信服务
 * 负责UI和Three.js场景之间的节点数据同步
 */
export class NodeCommunicationService {
  private static instance: NodeCommunicationService;
  private nodeRegistry: NodeRegistry;

  constructor() {
    this.nodeRegistry = NodeRegistry.getInstance();
  }

  public static getInstance(): NodeCommunicationService {
    if (!NodeCommunicationService.instance) {
      NodeCommunicationService.instance = new NodeCommunicationService();
    }
    return NodeCommunicationService.instance;
  }

  /**
   * 获取所有节点数据（用于UI显示）
   */
  public getAllNodes(): GameNodeProperties[] {
    return this.nodeRegistry.getAllNodes();
  }

  /**
   * 获取特定节点数据
   */
  public getNode(nodeId: string): GameNodeProperties | undefined {
    return this.nodeRegistry.getNode(nodeId);
  }

  /**
   * 更新节点属性（从UI触发）
   */
  public updateNodeFromUI(nodeId: string, updates: Partial<GameNodeProperties>): void {
    this.nodeRegistry.updateNode(nodeId, updates);
  }

  /**
   * 创建示例节点数据（用于演示）
   */
  public createSampleNodes(): void {
    const sampleNodes: GameNodeProperties[] = [
      {
        id: 'player_mesh',
        name: '玩家',
        type: GameNodeType.MESH,
        visible: true,
        enabled: true,
        position: new THREE.Vector3(0, 0, 0),
        rotation: new THREE.Vector3(0, 0, 0),
        scaling: new THREE.Vector3(1, 1, 1),
        parent: undefined,
        children: [],
        geometry: {
          type: 'box',
          parameters: { size: 2 }
        },
        material: {
          type: 'standard',
          diffuseColor: new THREE.Color(0x0000ff)
        }
      } as MeshNodeProperties,
      
             {
        id: 'main_camera',
        name: '主摄像机',
        type: GameNodeType.CAMERA,
        visible: true,
        enabled: true,
        position: new THREE.Vector3(0, 5, -10),
        rotation: new THREE.Vector3(-15, 0, 0),
        scaling: new THREE.Vector3(1, 1, 1),
        parent: undefined,
        children: [],
        cameraType: 'arc_rotate',
        fov: 0.8,
        target: new THREE.Vector3(0, 0, 0),
        minZ: 0.1,
        maxZ: 1000
      } as CameraNodeProperties,
      
      {
        id: 'main_light',
        name: '主光源',
        type: GameNodeType.LIGHT,
        visible: true,
        enabled: true,
        position: new THREE.Vector3(2, 10, 5),
        rotation: new THREE.Vector3(0, 0, 0),
        scaling: new THREE.Vector3(1, 1, 1),
        parent: undefined,
        children: [],
        lightType: 'hemispheric',
        intensity: 1.0,
        color: new THREE.Color(0xffffff)
      } as LightNodeProperties
    ];

    sampleNodes.forEach(node => {
      this.nodeRegistry.register(node);
    });
  }

  /**
   * 清理所有节点
   */
  public clearAllNodes(): void {
    this.nodeRegistry.clear();
  }

  /**
   * 添加节点变化监听器
   */
  public addNodeChangeListener(listener: NodeChangeListener): void {
    this.nodeRegistry.addListener(listener);
  }

  /**
   * 移除节点变化监听器
   */
  public removeNodeChangeListener(listener: NodeChangeListener): void {
    this.nodeRegistry.removeListener(listener);
  }

  /**
   * 将Vector3转换为UI使用的简单对象
   */
  public vector3ToObject(vector: THREE.Vector3): { x: number, y: number, z: number } {
    return {
      x: vector.x,
      y: vector.y,
      z: vector.z
    };
  }

  /**
   * 将简单对象转换为Vector3
   */
  public objectToVector3(obj: { x: number, y: number, z: number }): THREE.Vector3 {
    return new THREE.Vector3(obj.x, obj.y, obj.z);
  }

  /**
   * 安全地获取节点的标量属性
   */
  public getNodeScalingAsObject(node: GameNodeProperties): { x: number, y: number, z: number } {
    if (node.scaling instanceof THREE.Vector3) {
      return this.vector3ToObject(node.scaling);
    }
    // 兼容处理，如果scaling是其他格式
    return { x: 1, y: 1, z: 1 };
  }

  /**
   * 安全地更新节点的缩放属性
   */
  public updateNodeScaling(nodeId: string, scaling: { x: number, y: number, z: number }): void {
    this.updateNodeFromUI(nodeId, {
      scaling: this.objectToVector3(scaling)
    });
  }

  /**
   * 安全地获取节点的位置属性
   */
  public getNodePositionAsObject(node: GameNodeProperties): { x: number, y: number, z: number } {
    if (node.position instanceof THREE.Vector3) {
      return this.vector3ToObject(node.position);
    }
    return { x: 0, y: 0, z: 0 };
  }

  /**
   * 安全地更新节点的位置属性
   */
  public updateNodePosition(nodeId: string, position: { x: number, y: number, z: number }): void {
    this.updateNodeFromUI(nodeId, {
      position: this.objectToVector3(position)
    });
  }

  /**
   * 安全地获取节点的旋转属性
   */
  public getNodeRotationAsObject(node: GameNodeProperties): { x: number, y: number, z: number } {
    if (node.rotation instanceof THREE.Vector3) {
      return this.vector3ToObject(node.rotation);
    }
    return { x: 0, y: 0, z: 0 };
  }

  /**
   * 安全地更新节点的旋转属性
   */
  public updateNodeRotation(nodeId: string, rotation: { x: number, y: number, z: number }): void {
    this.updateNodeFromUI(nodeId, {
      rotation: this.objectToVector3(rotation)
    });
  }

  /**
   * 将UI修改同步到Three.js场景
   */
  public syncUIToScene(scene: any, nodeId: string, property: string, value: any): void {
    const threeObject = scene.getObjectByName(nodeId);
    if (!threeObject) {
      console.warn(`[NodeCommunication] 找不到场景对象: ${nodeId}`);
      return;
    }

    try {
      // 检查是否是摄像机
      const isCamera = threeObject instanceof THREE.Camera;

      if (isCamera && property === 'position' && value instanceof THREE.Vector3) {
        // 对于摄像机，直接设置位置
        threeObject.position.copy(value);
        console.log(`[NodeCommunication] 相机${nodeId}位置已更新`);
      } else if (property === 'position' && value instanceof THREE.Vector3) {
        // 非摄像机对象直接设置位置
        threeObject.position.copy(value);
        console.log(`[NodeCommunication] 对象${nodeId}位置已更新`);
      } else if (property === 'rotation' && value instanceof THREE.Vector3) {
        threeObject.rotation.setFromVector3(value);
        console.log(`[NodeCommunication] 对象${nodeId}旋转已更新`);
      } else if (property === 'scaling' && value instanceof THREE.Vector3) {
        threeObject.scale.copy(value);
        console.log(`[NodeCommunication] 对象${nodeId}缩放已更新`);
      }
    } catch (error) {
      console.warn(`[NodeCommunication] 同步${property}失败:`, error);
    }
  }
}

// 导出单例实例
export const nodeCommService = NodeCommunicationService.getInstance(); 