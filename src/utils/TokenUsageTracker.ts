// Token使用记录接口
export interface TokenUsageRecord {
  id: string;
  timestamp: number;
  agentType: string;
  agentName: string;
  threadId: string;
  model: string;
  promptTokens: number;
  completionTokens: number;
  totalTokens: number;
  cost: number;
  taskDescription?: string;
  duration?: number; // 请求耗时(ms)
}

// Token使用统计接口
export interface TokenUsageStats {
  totalCalls: number;
  totalPromptTokens: number;
  totalCompletionTokens: number;
  totalTokens: number;
  totalCost: number;
  averageTokensPerCall: number;
  averageCostPerCall: number;
  byAgent: Record<string, {
    calls: number;
    tokens: number;
    cost: number;
  }>;
  byModel: Record<string, {
    calls: number;
    tokens: number;
    cost: number;
  }>;
  dailyStats: Record<string, {
    calls: number;
    tokens: number;
    cost: number;
  }>;
}

// 模型定价配置
export interface ModelPricing {
  model: string;
  inputTokenPrice: number;  // 每1K token的价格(USD)
  outputTokenPrice: number; // 每1K token的价格(USD)
  currency: string;
}

// 默认模型定价 (基于Claude API定价)
export const DEFAULT_MODEL_PRICING: Record<string, ModelPricing> = {
  'claude-4-sonnet': {
    model: 'claude-4-sonnet',
    inputTokenPrice: 0.003,   // $3/1M tokens
    outputTokenPrice: 0.015,  // $15/1M tokens
    currency: 'USD'
  },
  'claude-3.5-sonnet': {
    model: 'claude-3.5-sonnet',
    inputTokenPrice: 0.003,
    outputTokenPrice: 0.015,
    currency: 'USD'
  },
  'gpt-4': {
    model: 'gpt-4',
    inputTokenPrice: 0.03,
    outputTokenPrice: 0.06,
    currency: 'USD'
  },
  'gpt-3.5-turbo': {
    model: 'gpt-3.5-turbo',
    inputTokenPrice: 0.0015,
    outputTokenPrice: 0.002,
    currency: 'USD'
  }
};

/**
 * Token使用统计追踪器
 * 负责记录、统计和分析AI模型的token使用情况
 */
export class TokenUsageTracker {
  private records: TokenUsageRecord[] = [];
  private modelPricing: Record<string, ModelPricing>;
  private storageKey = 'playablegen_token_usage';

  constructor(customPricing?: Record<string, ModelPricing>) {
    this.modelPricing = { ...DEFAULT_MODEL_PRICING, ...customPricing };
    this.loadFromStorage();
  }

  /**
   * 记录一次token使用
   */
  recordUsage(usage: {
    agentType: string;
    agentName: string;
    threadId: string;
    model: string;
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
    taskDescription?: string;
    duration?: number;
  }): TokenUsageRecord {
    const record: TokenUsageRecord = {
      id: this.generateId(),
      timestamp: Date.now(),
      cost: this.calculateCost(usage.model, usage.promptTokens, usage.completionTokens),
      ...usage
    };

    this.records.push(record);
    this.saveToStorage();

    console.log(`[TokenTracker] 记录token使用: ${usage.agentType} - ${usage.totalTokens} tokens - $${record.cost.toFixed(4)}`);
    
    return record;
  }

  /**
   * 计算成本
   */
  private calculateCost(model: string, promptTokens: number, completionTokens: number): number {
    const pricing = this.modelPricing[model];
    if (!pricing) {
      console.warn(`[TokenTracker] 未找到模型 ${model} 的定价信息，使用默认定价`);
      return (promptTokens + completionTokens) * 0.001; // 默认每1K token $1
    }

    const inputCost = (promptTokens / 1000) * pricing.inputTokenPrice;
    const outputCost = (completionTokens / 1000) * pricing.outputTokenPrice;
    
    return inputCost + outputCost;
  }

  /**
   * 获取统计信息
   */
  getStats(timeRange?: { start: number; end: number }): TokenUsageStats {
    let filteredRecords = this.records;
    
    if (timeRange) {
      filteredRecords = this.records.filter(
        record => record.timestamp >= timeRange.start && record.timestamp <= timeRange.end
      );
    }

    const stats: TokenUsageStats = {
      totalCalls: filteredRecords.length,
      totalPromptTokens: 0,
      totalCompletionTokens: 0,
      totalTokens: 0,
      totalCost: 0,
      averageTokensPerCall: 0,
      averageCostPerCall: 0,
      byAgent: {},
      byModel: {},
      dailyStats: {}
    };

    // 计算总计
    filteredRecords.forEach(record => {
      stats.totalPromptTokens += record.promptTokens;
      stats.totalCompletionTokens += record.completionTokens;
      stats.totalTokens += record.totalTokens;
      stats.totalCost += record.cost;

      // 按Agent统计
      if (!stats.byAgent[record.agentType]) {
        stats.byAgent[record.agentType] = { calls: 0, tokens: 0, cost: 0 };
      }
      stats.byAgent[record.agentType].calls++;
      stats.byAgent[record.agentType].tokens += record.totalTokens;
      stats.byAgent[record.agentType].cost += record.cost;

      // 按模型统计
      if (!stats.byModel[record.model]) {
        stats.byModel[record.model] = { calls: 0, tokens: 0, cost: 0 };
      }
      stats.byModel[record.model].calls++;
      stats.byModel[record.model].tokens += record.totalTokens;
      stats.byModel[record.model].cost += record.cost;

      // 按日期统计
      const dateKey = new Date(record.timestamp).toISOString().split('T')[0];
      if (!stats.dailyStats[dateKey]) {
        stats.dailyStats[dateKey] = { calls: 0, tokens: 0, cost: 0 };
      }
      stats.dailyStats[dateKey].calls++;
      stats.dailyStats[dateKey].tokens += record.totalTokens;
      stats.dailyStats[dateKey].cost += record.cost;
    });

    // 计算平均值
    if (stats.totalCalls > 0) {
      stats.averageTokensPerCall = stats.totalTokens / stats.totalCalls;
      stats.averageCostPerCall = stats.totalCost / stats.totalCalls;
    }

    return stats;
  }

  /**
   * 获取最近的使用记录
   */
  getRecentRecords(limit: number = 50): TokenUsageRecord[] {
    return this.records
      .sort((a, b) => b.timestamp - a.timestamp)
      .slice(0, limit);
  }

  /**
   * 清除历史记录
   */
  clearHistory(olderThan?: number): void {
    if (olderThan) {
      this.records = this.records.filter(record => record.timestamp > olderThan);
    } else {
      this.records = [];
    }
    this.saveToStorage();
    console.log('[TokenTracker] 历史记录已清除');
  }

  /**
   * 导出数据
   */
  exportData(): { records: TokenUsageRecord[]; stats: TokenUsageStats } {
    return {
      records: this.records,
      stats: this.getStats()
    };
  }

  /**
   * 更新模型定价
   */
  updateModelPricing(model: string, pricing: ModelPricing): void {
    this.modelPricing[model] = pricing;
    console.log(`[TokenTracker] 已更新模型 ${model} 的定价`);
  }

  // 私有方法
  private generateId(): string {
    return `token_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private saveToStorage(): void {
    if (typeof window !== 'undefined') {
      try {
        localStorage.setItem(this.storageKey, JSON.stringify(this.records));
      } catch (error) {
        console.warn('[TokenTracker] 保存到localStorage失败:', error);
      }
    }
  }

  private loadFromStorage(): void {
    if (typeof window !== 'undefined') {
      try {
        const stored = localStorage.getItem(this.storageKey);
        if (stored) {
          this.records = JSON.parse(stored);
          console.log(`[TokenTracker] 从localStorage加载了 ${this.records.length} 条记录`);
        }
      } catch (error) {
        console.warn('[TokenTracker] 从localStorage加载失败:', error);
        this.records = [];
      }
    }
  }
}

// 全局单例实例
export const tokenTracker = new TokenUsageTracker();
