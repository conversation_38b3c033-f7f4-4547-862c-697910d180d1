/**
 * 节点性能监控器
 * 监控节点系统的性能指标，包括创建速度、更新频率、内存使用等
 */
export class NodePerformanceMonitor {
  private static instance: NodePerformanceMonitor;
  
  // 性能指标
  private metrics = {
    nodeCreations: 0,
    nodeUpdates: 0,
    nodeDeletes: 0,
    totalNodes: 0,
    averageCreateTime: 0,
    averageUpdateTime: 0,
    renderFrameTime: 0,
    memoryUsage: 0
  };

  // 计时器
  private timers = new Map<string, number>();
  
  // 历史数据（最多保留100个采样点）
  private history = {
    frameTimes: [] as number[],
    nodeCounts: [] as number[],
    memoryUsage: [] as number[],
    timestamps: [] as number[]
  };

  private constructor() {
    this.startPerformanceTracking();
  }

  public static getInstance(): NodePerformanceMonitor {
    if (!NodePerformanceMonitor.instance) {
      NodePerformanceMonitor.instance = new NodePerformanceMonitor();
    }
    return NodePerformanceMonitor.instance;
  }

  /**
   * 开始计时
   */
  public startTimer(operationId: string): void {
    this.timers.set(operationId, performance.now());
  }

  /**
   * 结束计时并记录
   */
  public endTimer(operationId: string, operationType: 'create' | 'update' | 'delete'): number {
    const startTime = this.timers.get(operationId);
    if (!startTime) return 0;

    const duration = performance.now() - startTime;
    this.timers.delete(operationId);

    // 更新相应的指标
    switch (operationType) {
      case 'create':
        this.metrics.nodeCreations++;
        this.metrics.averageCreateTime = this.updateAverage(
          this.metrics.averageCreateTime, 
          duration, 
          this.metrics.nodeCreations
        );
        break;
      case 'update':
        this.metrics.nodeUpdates++;
        this.metrics.averageUpdateTime = this.updateAverage(
          this.metrics.averageUpdateTime, 
          duration, 
          this.metrics.nodeUpdates
        );
        break;
      case 'delete':
        this.metrics.nodeDeletes++;
        break;
    }

    console.log(`[Performance] ${operationType} took ${duration.toFixed(2)}ms`);
    return duration;
  }

  /**
   * 记录节点数量变化
   */
  public recordNodeCount(count: number): void {
    this.metrics.totalNodes = count;
    this.addToHistory('nodeCounts', count);
  }

  /**
   * 记录渲染帧时间
   */
  public recordFrameTime(frameTime: number): void {
    this.metrics.renderFrameTime = frameTime;
    this.addToHistory('frameTimes', frameTime);
  }

  /**
   * 获取当前性能指标
   */
  public getMetrics(): typeof this.metrics {
    return { ...this.metrics };
  }

  /**
   * 获取性能历史数据
   */
  public getHistory(): typeof this.history {
    return {
      frameTimes: [...this.history.frameTimes],
      nodeCounts: [...this.history.nodeCounts],
      memoryUsage: [...this.history.memoryUsage],
      timestamps: [...this.history.timestamps]
    };
  }

  /**
   * 获取性能报告
   */
  public getPerformanceReport(): {
    summary: string;
    recommendations: string[];
    metrics: typeof this.metrics;
    health: 'good' | 'warning' | 'critical';
  } {
    const avgFrameTime = this.getAverageFrameTime();
    const nodeCreationRate = this.metrics.nodeCreations / Math.max(1, Date.now() / 1000);
    
    let health: 'good' | 'warning' | 'critical' = 'good';
    const recommendations: string[] = [];

    // 性能健康度评估
    if (avgFrameTime > 16.67) { // 低于60fps
      health = 'warning';
      recommendations.push('渲染帧时间偏高，考虑优化场景复杂度');
    }

    if (avgFrameTime > 33.33) { // 低于30fps
      health = 'critical';
      recommendations.push('渲染性能严重不足，需要立即优化');
    }

    if (this.metrics.totalNodes > 1000) {
      health = health === 'good' ? 'warning' : health;
      recommendations.push('节点数量较多，考虑使用实例化或LOD优化');
    }

    if (this.metrics.averageCreateTime > 10) {
      recommendations.push('节点创建时间较长，优化NodeFactory性能');
    }

    if (this.metrics.averageUpdateTime > 5) {
      recommendations.push('节点更新时间较长，优化更新算法');
    }

    const summary = `
节点性能总览:
- 总节点数: ${this.metrics.totalNodes}
- 平均帧时间: ${avgFrameTime.toFixed(2)}ms (${(1000/avgFrameTime).toFixed(1)} FPS)
- 节点创建率: ${nodeCreationRate.toFixed(2)}/秒
- 平均创建时间: ${this.metrics.averageCreateTime.toFixed(2)}ms
- 平均更新时间: ${this.metrics.averageUpdateTime.toFixed(2)}ms
    `.trim();

    return {
      summary,
      recommendations,
      metrics: this.metrics,
      health
    };
  }

  /**
   * 重置所有统计数据
   */
  public reset(): void {
    this.metrics = {
      nodeCreations: 0,
      nodeUpdates: 0,
      nodeDeletes: 0,
      totalNodes: 0,
      averageCreateTime: 0,
      averageUpdateTime: 0,
      renderFrameTime: 0,
      memoryUsage: 0
    };

    this.history = {
      frameTimes: [],
      nodeCounts: [],
      memoryUsage: [],
      timestamps: []
    };

    this.timers.clear();
    console.log('[Performance] 性能监控数据已重置');
  }

  /**
   * 开始自动性能跟踪
   */
  private startPerformanceTracking(): void {
    // 每秒更新一次性能数据
    setInterval(() => {
      this.updateMemoryUsage();
      this.addToHistory('timestamps', Date.now());
    }, 1000);
  }

  /**
   * 更新平均值
   */
  private updateAverage(currentAvg: number, newValue: number, count: number): number {
    return (currentAvg * (count - 1) + newValue) / count;
  }

  /**
   * 添加数据到历史记录
   */
  private addToHistory(key: keyof typeof this.history, value: number): void {
    const history = this.history[key] as number[];
    history.push(value);
    
    // 保持最多100个数据点
    if (history.length > 100) {
      history.shift();
    }
  }

  /**
   * 获取平均帧时间
   */
  private getAverageFrameTime(): number {
    if (this.history.frameTimes.length === 0) return 16.67; // 默认60fps
    
    const sum = this.history.frameTimes.reduce((a, b) => a + b, 0);
    return sum / this.history.frameTimes.length;
  }

  /**
   * 更新内存使用情况
   */
  private updateMemoryUsage(): void {
    if ('memory' in performance) {
      // @ts-expect-error - performance.memory在某些浏览器中可用
      const memory = performance.memory as { usedJSHeapSize: number };
      this.metrics.memoryUsage = memory.usedJSHeapSize / 1024 / 1024; // MB
      this.addToHistory('memoryUsage', this.metrics.memoryUsage);
    }
  }
}

// 导出单例实例
export const nodePerformanceMonitor = NodePerformanceMonitor.getInstance(); 