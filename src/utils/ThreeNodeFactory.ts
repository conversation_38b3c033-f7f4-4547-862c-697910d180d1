/**
 * ThreeNodeFactory - Three.js节点工厂类
 * 专注于核心功能，提供稳定的Three.js节点创建和注册
 */

import * as THREE from 'three';
import { NodeRegistry } from './NodeRegistry';
import {
  GameNodeType,
  MeshNodeProperties,
  LightNodeProperties,
  CameraNodeProperties
} from '../types/NodeTypes';

export class ThreeNodeFactory {
  private static instance: ThreeNodeFactory;
  private nodeRegistry: NodeRegistry;

  private constructor() {
    this.nodeRegistry = NodeRegistry.getInstance();
  }

  public static getInstance(): ThreeNodeFactory {
    if (!ThreeNodeFactory.instance) {
      ThreeNodeFactory.instance = new ThreeNodeFactory();
    }
    return ThreeNodeFactory.instance;
  }

  /**
   * 创建基础方块Mesh
   */
  public createBoxMesh(scene: THREE.Scene, config: {
    id: string;
    name: string;
    size?: number;
    position?: THREE.Vector3;
    color?: THREE.Color;
  }): THREE.Mesh {
    console.log(`[ThreeNodeFactory] 创建方块: ${config.name}`);

    // 创建方块几何体
    const geometry = new THREE.BoxGeometry(
      config.size || 1, 
      config.size || 1, 
      config.size || 1
    );
    
    // 创建材质
    const material = new THREE.MeshStandardMaterial({
      color: config.color || new THREE.Color(0xffffff)
    });
    
    const box = new THREE.Mesh(geometry, material);
    box.name = config.id;
    
    // 设置位置
    if (config.position) {
      box.position.copy(config.position);
    }

    // 启用阴影
    box.castShadow = true;
    box.receiveShadow = true;

    // 添加到场景
    scene.add(box);

    // 注册到NodeRegistry
    const position = box.position.clone();
    const nodeProperties: MeshNodeProperties = {
      id: config.id,
      name: config.name,
      type: GameNodeType.MESH,
      visible: true,
      enabled: true,
      position: position || new THREE.Vector3(0, 0, 0),
      rotation: new THREE.Vector3(box.rotation.x, box.rotation.y, box.rotation.z),
      scaling: box.scale.clone() || new THREE.Vector3(1, 1, 1),
      parent: undefined,
      children: [],
      geometry: {
        type: 'box',
        parameters: { size: config.size || 1 }
      },
      material: {
        type: 'standard',
        diffuseColor: config.color || new THREE.Color(0xffffff)
      }
    };

    this.nodeRegistry.register(nodeProperties);
    console.log(`[ThreeNodeFactory] 方块创建完成: ${config.id}`);
    return box;
  }

  /**
   * 创建地面Mesh
   */
  public createGroundMesh(scene: THREE.Scene, config: {
    id: string;
    name: string;
    width?: number;
    height?: number;
    position?: THREE.Vector3;
    color?: THREE.Color;
  }): THREE.Mesh {
    console.log(`[ThreeNodeFactory] 创建地面: ${config.name}`);

    // 创建地面几何体
    const geometry = new THREE.PlaneGeometry(
      config.width || 10, 
      config.height || 10
    );
    
    // 创建材质
    const material = new THREE.MeshStandardMaterial({
      color: config.color || new THREE.Color(0x808080)
    });
    
    const ground = new THREE.Mesh(geometry, material);
    ground.name = config.id;
    ground.rotation.x = -Math.PI / 2; // 水平放置
    
    // 设置位置
    if (config.position) {
      ground.position.copy(config.position);
    }

    // 启用阴影接收
    ground.receiveShadow = true;

    // 添加到场景
    scene.add(ground);

    // 注册到NodeRegistry
    const position = ground.position.clone();
    const nodeProperties: MeshNodeProperties = {
      id: config.id,
      name: config.name,
      type: GameNodeType.MESH,
      visible: true,
      enabled: true,
      position: position || new THREE.Vector3(0, 0, 0),
      rotation: new THREE.Vector3(ground.rotation.x, ground.rotation.y, ground.rotation.z),
      scaling: ground.scale.clone() || new THREE.Vector3(1, 1, 1),
      parent: undefined,
      children: [],
      geometry: {
        type: 'ground',
        parameters: { width: config.width || 10, height: config.height || 10 }
      },
      material: {
        type: 'standard',
        diffuseColor: config.color || new THREE.Color(0x808080)
      }
    };

    this.nodeRegistry.register(nodeProperties);
    console.log(`[ThreeNodeFactory] 地面创建完成: ${config.id}`);
    return ground;
  }

  /**
   * 创建环境光源
   */
  public createAmbientLight(scene: THREE.Scene, config: {
    id: string;
    name: string;
    intensity?: number;
    color?: THREE.Color;
  }): THREE.AmbientLight {
    console.log(`[ThreeNodeFactory] 创建环境光源: ${config.name}`);

    const light = new THREE.AmbientLight(
      config.color || new THREE.Color(0xffffff),
      config.intensity || 0.4
    );
    light.name = config.id;

    // 添加到场景
    scene.add(light);

    // 注册到NodeRegistry
    const nodeProperties: LightNodeProperties = {
      id: config.id,
      name: config.name,
      type: GameNodeType.LIGHT,
      visible: true,
      enabled: true,
      position: new THREE.Vector3(0, 0, 0),
      rotation: new THREE.Vector3(0, 0, 0),
      scaling: new THREE.Vector3(1, 1, 1),
      parent: undefined,
      children: [],
      lightType: 'hemispheric',
      intensity: config.intensity || 0.4,
      color: config.color || new THREE.Color(0xffffff)
    };

    this.nodeRegistry.register(nodeProperties);
    console.log(`[ThreeNodeFactory] 环境光源创建完成: ${config.id}`);
    return light;
  }

  /**
   * 创建方向光源
   */
  public createDirectionalLight(scene: THREE.Scene, config: {
    id: string;
    name: string;
    intensity?: number;
    color?: THREE.Color;
    position?: THREE.Vector3;
  }): THREE.DirectionalLight {
    console.log(`[ThreeNodeFactory] 创建方向光源: ${config.name}`);

    const light = new THREE.DirectionalLight(
      config.color || new THREE.Color(0xffffff),
      config.intensity || 1.0
    );
    light.name = config.id;
    
    if (config.position) {
      light.position.copy(config.position);
    } else {
      light.position.set(10, 10, 5);
    }

    // 启用阴影
    light.castShadow = true;
    light.shadow.mapSize.width = 2048;
    light.shadow.mapSize.height = 2048;

    // 添加到场景
    scene.add(light);

    // 注册到NodeRegistry
    const position = light.position.clone();
    const nodeProperties: LightNodeProperties = {
      id: config.id,
      name: config.name,
      type: GameNodeType.LIGHT,
      visible: true,
      enabled: true,
      position: position || new THREE.Vector3(0, 0, 0),
      rotation: new THREE.Vector3(0, 0, 0),
      scaling: new THREE.Vector3(1, 1, 1),
      parent: undefined,
      children: [],
      lightType: 'directional',
      intensity: config.intensity || 1.0,
      color: config.color || new THREE.Color(0xffffff)
    };

    this.nodeRegistry.register(nodeProperties);
    console.log(`[ThreeNodeFactory] 方向光源创建完成: ${config.id}`);
    return light;
  }

  /**
   * 创建摄像机
   */
  public createCamera(scene: THREE.Scene, config: {
    id: string;
    name: string;
    position?: THREE.Vector3;
    target?: THREE.Vector3;
    fov?: number;
    aspect?: number;
    near?: number;
    far?: number;
  }): THREE.PerspectiveCamera {
    console.log(`[ThreeNodeFactory] 创建摄像机: ${config.name}`);

    const camera = new THREE.PerspectiveCamera(
      config.fov || 75,
      config.aspect || window.innerWidth / window.innerHeight,
      config.near || 0.1,
      config.far || 1000
    );
    camera.name = config.id;
    
    // 设置位置
    if (config.position) {
      camera.position.copy(config.position);
    } else {
      camera.position.set(0, 5, -10);
    }

    // 设置朝向
    if (config.target) {
      camera.lookAt(config.target);
      camera.userData.target = config.target.clone();
    } else {
      camera.lookAt(0, 0, 0);
      camera.userData.target = new THREE.Vector3(0, 0, 0);
    }

    // 注册到NodeRegistry
    const position = camera.position.clone();
    const nodeProperties: CameraNodeProperties = {
      id: config.id,
      name: config.name,
      type: GameNodeType.CAMERA,
      visible: true,
      enabled: true,
      position: position,
      rotation: new THREE.Vector3(camera.rotation.x, camera.rotation.y, camera.rotation.z),
      scaling: new THREE.Vector3(1, 1, 1),
      parent: undefined,
      children: [],
      cameraType: 'free',
      target: config.target || new THREE.Vector3(0, 0, 0),
      fov: config.fov || 75,
      minZ: config.near || 0.1,
      maxZ: config.far || 1000,
      viewLocked: true // 默认锁定视角
    };

    // 将摄像机添加到场景中
    scene.add(camera);

    this.nodeRegistry.register(nodeProperties);
    console.log(`[ThreeNodeFactory] 摄像机创建完成: ${config.id}`);
    return camera;
  }

  /**
   * 注册现有摄像机到节点系统
   */
  public registerExistingCamera(scene: THREE.Scene, camera: THREE.PerspectiveCamera, config: {
    id: string;
    name: string;
  }): void {
    console.log(`[ThreeNodeFactory] 注册现有摄像机: ${config.name}`);

    // 设置摄像机名称为ID，以便场景查找
    camera.name = config.id;

    // 将摄像机添加到场景中，确保getObjectByName能找到它
    if (!scene.getObjectByName(config.id)) {
      scene.add(camera);
      console.log(`[ThreeNodeFactory] 摄像机已添加到场景: ${config.id}`);
    }

    // 初始化userData.target
    if (!camera.userData.target) {
      camera.userData.target = new THREE.Vector3(0, 0, 0);
    }

    // 注册到NodeRegistry
    const position = camera.position.clone();
    const nodeProperties: CameraNodeProperties = {
      id: config.id,
      name: config.name,
      type: GameNodeType.CAMERA,
      visible: true,
      enabled: true,
      position: position,
      rotation: new THREE.Vector3(camera.rotation.x, camera.rotation.y, camera.rotation.z),
      scaling: new THREE.Vector3(1, 1, 1),
      parent: undefined,
      children: [],
      cameraType: 'free',
      target: camera.userData.target,
      fov: camera.fov,
      minZ: camera.near,
      maxZ: camera.far,
      viewLocked: true // 默认锁定视角
    };

    this.nodeRegistry.register(nodeProperties);
    console.log(`[ThreeNodeFactory] 现有摄像机注册完成: ${config.id}`);
  }

  /**
   * 创建简单的3D场景
   */
  public createBasic3DScene(scene: THREE.Scene, existingCamera?: THREE.PerspectiveCamera): {
    ground: THREE.Mesh;
    playerBox: THREE.Mesh;
    ambientLight: THREE.AmbientLight;
    directionalLight: THREE.DirectionalLight;
    camera?: THREE.PerspectiveCamera;
  } {
    console.log('[ThreeNodeFactory] 创建基础3D场景...');

    // 创建地面
    const ground = this.createGroundMesh(scene, {
      id: 'ground',
      name: '地面',
      width: 20,
      height: 20,
      position: new THREE.Vector3(0, 0, 0),
      color: new THREE.Color(0x808080)
    });

    // 创建玩家方块
    const playerBox = this.createBoxMesh(scene, {
      id: 'player_box',
      name: '玩家方块',
      size: 2,
      position: new THREE.Vector3(0, 1, 0),
      color: new THREE.Color(0x3366ff)
    });

    // 创建环境光源（不注册到节点列表，只保留功能）
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.4);
    ambientLight.name = 'ambient_light_hidden';
    scene.add(ambientLight);

    // 创建方向光源
    const directionalLight = this.createDirectionalLight(scene, {
      id: 'directional_light',
      name: '方向光源',
      intensity: 1.0,
      color: new THREE.Color(0xffffff),
      position: new THREE.Vector3(10, 10, 5)
    });

    // 如果提供了现有摄像机，注册它；否则创建新的摄像机
    let camera: THREE.PerspectiveCamera | undefined;
    if (existingCamera) {
      this.registerExistingCamera(scene, existingCamera, {
        id: 'main_camera',
        name: '主摄像机'
      });
      camera = existingCamera;
    } else {
      camera = this.createCamera(scene, {
        id: 'main_camera',
        name: '主摄像机',
        position: new THREE.Vector3(0, 5, -10),
        target: new THREE.Vector3(0, 0, 0),
        fov: 75
      });
    }

    console.log('[ThreeNodeFactory] 基础3D场景创建完成');
    
    return {
      ground,
      playerBox,
      ambientLight,
      directionalLight,
      camera
    };
  }

  /**
   * 清理所有节点
   */
  public clearAllNodes(): void {
    this.nodeRegistry.clear();
    console.log('[ThreeNodeFactory] 所有节点已清理');
  }

  /**
   * 获取节点统计信息
   */
  public getNodeStats(): { total: number; byType: Record<string, number> } {
    const allNodes = this.nodeRegistry.getAllNodes();
    const stats = {
      total: allNodes.length,
      byType: {} as Record<string, number>
    };

    allNodes.forEach(node => {
      stats.byType[node.type] = (stats.byType[node.type] || 0) + 1;
    });

    return stats;
  }
}

export default ThreeNodeFactory;
