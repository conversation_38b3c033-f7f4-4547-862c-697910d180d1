/**
 * ID一致性保护机制
 * 确保项目ID在整个生成流程中保持一致
 */

export class IdConsistencyGuard {
  private static activeProjectIds = new Map<string, string>();

  /**
   * 注册项目ID，开始跟踪
   */
  public static registerProjectId(projectId: string, context: string): void {
    console.log(`[IdConsistencyGuard] 注册项目ID: ${projectId} (${context})`);
    this.activeProjectIds.set(projectId, context);
  }

  /**
   * 验证项目ID是否已注册
   */
  public static validateProjectId(projectId: string, context: string): boolean {
    if (!this.activeProjectIds.has(projectId)) {
      console.warn(`[IdConsistencyGuard] 警告: 未注册的项目ID ${projectId} 在 ${context} 中使用`);
      return false;
    }
    
    console.log(`[IdConsistencyGuard] 验证通过: ${projectId} (${context})`);
    return true;
  }

  /**
   * 生成文件路径时验证ID一致性
   */
  public static validateFilePath(projectId: string, filePath: string, context: string): boolean {
    if (!filePath.includes(projectId)) {
      console.error(`[IdConsistencyGuard] 错误: 文件路径 ${filePath} 不包含项目ID ${projectId} (${context})`);
      return false;
    }
    
    console.log(`[IdConsistencyGuard] 文件路径验证通过: ${filePath} (${context})`);
    return true;
  }

  /**
   * 完成项目处理，取消注册
   */
  public static unregisterProjectId(projectId: string, context: string): void {
    console.log(`[IdConsistencyGuard] 取消注册项目ID: ${projectId} (${context})`);
    this.activeProjectIds.delete(projectId);
  }

  /**
   * 获取所有活跃的项目ID
   */
  public static getActiveProjectIds(): string[] {
    return Array.from(this.activeProjectIds.keys());
  }

  /**
   * 清理所有注册的项目ID
   */
  public static clearAll(): void {
    console.log(`[IdConsistencyGuard] 清理所有注册的项目ID`);
    this.activeProjectIds.clear();
  }

  /**
   * 验证组件文件名与项目ID的一致性
   */
  public static validateComponentFileName(projectId: string, fileName: string): boolean {
    const expectedFileName = `${projectId}.tsx`;
    
    if (fileName !== expectedFileName) {
      console.error(`[IdConsistencyGuard] 组件文件名不一致: 期望 ${expectedFileName}, 实际 ${fileName}`);
      return false;
    }
    
    console.log(`[IdConsistencyGuard] 组件文件名验证通过: ${fileName}`);
    return true;
  }

  /**
   * 验证游戏目录名与项目ID的一致性
   */
  public static validateGameDirectoryName(projectId: string, dirName: string): boolean {
    if (dirName !== projectId) {
      console.error(`[IdConsistencyGuard] 游戏目录名不一致: 期望 ${projectId}, 实际 ${dirName}`);
      return false;
    }
    
    console.log(`[IdConsistencyGuard] 游戏目录名验证通过: ${dirName}`);
    return true;
  }

  /**
   * 生成一致性报告
   */
  public static generateConsistencyReport(): {
    activeProjects: number;
    projectIds: string[];
    timestamp: string;
  } {
    return {
      activeProjects: this.activeProjectIds.size,
      projectIds: Array.from(this.activeProjectIds.keys()),
      timestamp: new Date().toISOString()
    };
  }
}
