import { 
  GameNodeProperties, 
  GameNodeType, 
  NodeRegistryInterface, 
  NodeChangeEvent, 
  NodeChangeListener 
} from '../types/NodeTypes';

/**
 * 节点注册表 - 单例模式
 * 管理场景中所有游戏节点的注册、查询和更新
 */
export class NodeRegistry implements NodeRegistryInterface {
  private static instance: NodeRegistry;
  private nodes: Map<string, GameNodeProperties> = new Map();
  private listeners: NodeChangeListener[] = [];

  private constructor() {}

  /**
   * 获取单例实例
   */
  public static getInstance(): NodeRegistry {
    if (!NodeRegistry.instance) {
      NodeRegistry.instance = new NodeRegistry();
    }
    return NodeRegistry.instance;
  }

  /**
   * 注册节点
   */
  public register(node: GameNodeProperties): void {
    this.nodes.set(node.id, { ...node });
    this.notifyListeners({
      type: 'add',
      nodeId: node.id,
      node: { ...node }
    });
    console.log(`[NodeRegistry] 注册节点: ${node.name} (${node.type})`);
  }

  /**
   * 注销节点
   */
  public unregister(nodeId: string): void {
    const node = this.nodes.get(nodeId);
    if (node) {
      this.nodes.delete(nodeId);
      this.notifyListeners({
        type: 'remove',
        nodeId,
        node: { ...node }
      });
      console.log(`[NodeRegistry] 注销节点: ${nodeId}`);
    }
  }

  /**
   * 获取节点
   */
  public getNode(nodeId: string): GameNodeProperties | undefined {
    return this.nodes.get(nodeId);
  }

  /**
   * 获取所有节点
   */
  public getAllNodes(): GameNodeProperties[] {
    return Array.from(this.nodes.values());
  }

  /**
   * 根据类型获取节点
   */
  public getNodesByType(type: GameNodeType): GameNodeProperties[] {
    return Array.from(this.nodes.values()).filter(node => node.type === type);
  }

  /**
   * 更新节点
   */
  public updateNode(nodeId: string, updates: Partial<GameNodeProperties>): void {
    const existingNode = this.nodes.get(nodeId);
    if (existingNode) {
      const updatedNode = { ...existingNode, ...updates } as GameNodeProperties;
      this.nodes.set(nodeId, updatedNode);
      this.notifyListeners({
        type: 'update',
        nodeId,
        node: updatedNode,
        changes: updates
      });
      console.log(`[NodeRegistry] 更新节点: ${nodeId}`, updates);
    }
  }

  /**
   * 清空所有节点
   */
  public clear(): void {
    this.nodes.clear();
    console.log(`[NodeRegistry] 清空所有节点`);
  }

  /**
   * 添加变更监听器
   */
  public addListener(listener: NodeChangeListener): void {
    this.listeners.push(listener);
  }

  /**
   * 移除变更监听器
   */
  public removeListener(listener: NodeChangeListener): void {
    const index = this.listeners.indexOf(listener);
    if (index > -1) {
      this.listeners.splice(index, 1);
    }
  }

  /**
   * 通知所有监听器
   */
  private notifyListeners(event: NodeChangeEvent): void {
    this.listeners.forEach(listener => {
      try {
        listener(event);
      } catch (error) {
        console.error('[NodeRegistry] 监听器错误:', error);
      }
    });
  }

  /**
   * 获取节点层级结构
   */
  public getNodeHierarchy(): GameNodeProperties[] {
    const rootNodes = Array.from(this.nodes.values()).filter(node => !node.parent);
    
    const buildHierarchy = (nodes: GameNodeProperties[]): GameNodeProperties[] => {
      return nodes.map(node => {
        const children = Array.from(this.nodes.values()).filter(child => child.parent === node.id);
        return {
          ...node,
          children: children.map(child => child.id)
        };
      });
    };

    return buildHierarchy(rootNodes);
  }

  /**
   * 导出节点数据为JSON
   */
  public exportToJSON(): string {
    const data = {
      timestamp: new Date().toISOString(),
      nodes: Array.from(this.nodes.values())
    };
    return JSON.stringify(data, null, 2);
  }

  /**
   * 从JSON导入节点数据
   */
  public importFromJSON(jsonData: string): void {
    try {
      const data = JSON.parse(jsonData);
      if (data.nodes && Array.isArray(data.nodes)) {
        this.clear();
        data.nodes.forEach((node: GameNodeProperties) => {
          this.register(node);
        });
        console.log(`[NodeRegistry] 导入 ${data.nodes.length} 个节点`);
      }
    } catch (error) {
      console.error('[NodeRegistry] JSON导入失败:', error);
      throw new Error('无效的节点数据格式');
    }
  }

  /**
   * 获取统计信息
   */
  public getStatistics(): Record<string, number> {
    const stats: Record<string, number> = {
      total: this.nodes.size
    };

    // 按类型统计
    Object.values(GameNodeType).forEach(type => {
      stats[type] = this.getNodesByType(type).length;
    });

    return stats;
  }

  /**
   * 添加观察者（简化API）
   */
  public addObserver(callback: (nodes: GameNodeProperties[]) => void): void {
    this.addListener(() => {
      callback(this.getAllNodes());
    });
  }

  /**
   * 更新节点（简化API）
   */
  public update(node: GameNodeProperties): void {
    this.updateNode(node.id, node);
  }
} 