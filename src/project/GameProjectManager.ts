/**
 * GameProjectManager - 游戏项目管理系统
 * 负责游戏项目的创建、存储、版本管理和状态跟踪
 */

import { GameDesign } from '../agents/GameDesignAgent';
import { GeneratedCode } from '../agents/CodeGenerationAgent';

// 项目数据序列化接口
interface SerializedGameProject {
  id: string;
  name: string;
  description: string;
  status: GameProjectStatus;
  createdAt: string;
  updatedAt: string;
  userInput: {
    description: string;
    gameType?: string;
    platform?: string;
    targetAudience?: string;
  };
  gameDesign?: GameDesign;
  designIterations: GameDesign[];
  generatedCode?: GeneratedCode;
  codeIterations: GeneratedCode[];
  config: {
    enableOptimization: boolean;
    qualityThreshold: number;
    maxIterations: number;
  };
  metadata: {
    totalIterations: number;
    generationTime: number;
    tokensUsed: number;
    qualityScore: number;
    templateUsed?: string;
  };
}

import fs from 'fs/promises';
import path from 'path';

// 游戏项目状态枚举
export enum GameProjectStatus {
  CREATING = 'creating',
  DESIGNING = 'designing',
  GENERATING = 'generating',
  COMPLETED = 'completed',
  ERROR = 'error'
}

// 游戏项目接口
export interface GameProject {
  id: string;
  name: string;
  description: string;
  status: GameProjectStatus;
  createdAt: Date;
  updatedAt: Date;
  
  // 用户输入
  userInput: {
    description: string;
    gameType?: string;
    platform?: string;
    targetAudience?: string;
  };
  
  // 设计阶段
  gameDesign?: GameDesign;
  designIterations: GameDesign[];
  
  // 代码生成阶段
  generatedCode?: GeneratedCode;
  codeIterations: GeneratedCode[];
  
  // 项目配置
  config: {
    enableOptimization: boolean;
    qualityThreshold: number;
    maxIterations: number;
  };
  
  // 元数据
  metadata: {
    totalIterations: number;
    generationTime: number;
    tokensUsed: number;
    qualityScore: number;
    templateUsed?: string;
  };
}

// 项目创建请求接口
export interface CreateProjectRequest {
  name: string;
  description: string;
  gameType?: string;
  platform?: string;
  targetAudience?: string;
  config?: Partial<GameProject['config']>;
}

// 项目更新请求接口
export interface UpdateProjectRequest {
  name?: string;
  description?: string;
  status?: GameProjectStatus;
  gameDesign?: GameDesign;
  generatedCode?: GeneratedCode;
  metadata?: Partial<GameProject['metadata']>;
}

/**
 * 游戏项目管理器类
 */
export class GameProjectManager {
  private static instance: GameProjectManager;
  private projects: Map<string, GameProject> = new Map();
  private readonly STORAGE_KEY = 'playablegen_projects';
  private readonly SERVER_STORAGE_PATH = './data/projects.json';

  private constructor() {
    this.loadProjects();
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): GameProjectManager {
    if (!GameProjectManager.instance) {
      GameProjectManager.instance = new GameProjectManager();
    }
    return GameProjectManager.instance;
  }

  /**
   * 创建新项目
   */
  public createProject(request: CreateProjectRequest): GameProject {
    const projectId = this.generateProjectId();
    
    const project: GameProject = {
      id: projectId,
      name: request.name,
      description: request.description,
      status: GameProjectStatus.CREATING,
      createdAt: new Date(),
      updatedAt: new Date(),
      
      userInput: {
        description: request.description,
        gameType: request.gameType,
        platform: request.platform,
        targetAudience: request.targetAudience
      },
      
      designIterations: [],
      codeIterations: [],
      
      config: {
        enableOptimization: true,
        qualityThreshold: 75,
        maxIterations: 3,
        ...request.config
      },
      
      metadata: {
        totalIterations: 0,
        generationTime: 0,
        tokensUsed: 0,
        qualityScore: 0
      }
    };

    this.projects.set(projectId, project);
    this.saveProjects();
    
    console.log(`[GameProjectManager] 创建项目: ${projectId} - ${request.name}`);
    return project;
  }

  /**
   * 获取项目
   */
  public getProject(projectId: string): GameProject | null {
    return this.projects.get(projectId) || null;
  }

  /**
   * 获取所有项目
   */
  public getAllProjects(): GameProject[] {
    return Array.from(this.projects.values()).sort(
      (a, b) => b.updatedAt.getTime() - a.updatedAt.getTime()
    );
  }

  /**
   * 更新项目
   */
  public updateProject(projectId: string, updates: UpdateProjectRequest): GameProject | null {
    const project = this.projects.get(projectId);
    if (!project) {
      console.error(`[GameProjectManager] 项目不存在: ${projectId}`);
      return null;
    }

    // 更新基本信息
    if (updates.name) project.name = updates.name;
    if (updates.description) project.description = updates.description;
    if (updates.status) project.status = updates.status;
    
    // 更新设计
    if (updates.gameDesign) {
      project.gameDesign = updates.gameDesign;
      project.designIterations.push(updates.gameDesign);
    }
    
    // 更新代码
    if (updates.generatedCode) {
      project.generatedCode = updates.generatedCode;
      project.codeIterations.push(updates.generatedCode);
    }
    
    // 更新元数据
    if (updates.metadata) {
      project.metadata = { ...project.metadata, ...updates.metadata };
    }
    
    project.updatedAt = new Date();
    this.projects.set(projectId, project);
    this.saveProjects();
    
    console.log(`[GameProjectManager] 更新项目: ${projectId}`);
    return project;
  }

  /**
   * 删除项目
   */
  public deleteProject(projectId: string): boolean {
    const success = this.projects.delete(projectId);
    if (success) {
      this.saveProjects();
      console.log(`[GameProjectManager] 删除项目: ${projectId}`);
    }
    return success;
  }

  /**
   * 获取项目统计信息
   */
  public getProjectStats(): {
    total: number;
    byStatus: Record<GameProjectStatus, number>;
    averageQuality: number;
    totalTokensUsed: number;
  } {
    const projects = this.getAllProjects();
    const byStatus = {} as Record<GameProjectStatus, number>;
    
    // 初始化状态计数
    Object.values(GameProjectStatus).forEach(status => {
      byStatus[status] = 0;
    });
    
    let totalQuality = 0;
    let totalTokens = 0;
    let completedProjects = 0;
    
    projects.forEach(project => {
      byStatus[project.status]++;
      totalTokens += project.metadata.tokensUsed;
      
      if (project.status === GameProjectStatus.COMPLETED) {
        totalQuality += project.metadata.qualityScore;
        completedProjects++;
      }
    });
    
    return {
      total: projects.length,
      byStatus,
      averageQuality: completedProjects > 0 ? totalQuality / completedProjects : 0,
      totalTokensUsed: totalTokens
    };
  }

  /**
   * 搜索项目
   */
  public searchProjects(query: string): GameProject[] {
    const lowerQuery = query.toLowerCase();
    return this.getAllProjects().filter(project => 
      project.name.toLowerCase().includes(lowerQuery) ||
      project.description.toLowerCase().includes(lowerQuery) ||
      project.userInput.description.toLowerCase().includes(lowerQuery)
    );
  }

  /**
   * 生成项目ID - 使用与PlayableGenAPI一致的格式
   * 确保项目ID可以直接用作生成的文件夹名和组件名
   */
  private generateProjectId(): string {
    return `game_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * 保存项目到存储
   */
  private saveProjects(): void {
    try {
      const projectsData: SerializedGameProject[] = Array.from(this.projects.entries()).map(([, project]) => ({
        ...project,
        createdAt: project.createdAt.toISOString(),
        updatedAt: project.updatedAt.toISOString()
      }));

      // 检查是否在浏览器环境
      if (typeof window !== 'undefined' && window.localStorage) {
        localStorage.setItem(this.STORAGE_KEY, JSON.stringify(projectsData));
      } else {
        // 服务器端环境，使用文件系统（异步执行，不阻塞）
        this.saveToFileSystem(projectsData).catch(error => {
          console.error('[GameProjectManager] 异步保存失败:', error);
        });
      }
    } catch (error) {
      console.error('[GameProjectManager] 保存项目失败:', error);
    }
  }

  /**
   * 从存储加载项目
   */
  private loadProjects(): void {
    try {
      let projectsData: SerializedGameProject[] = [];

      // 检查是否在浏览器环境
      if (typeof window !== 'undefined' && window.localStorage) {
        const stored = localStorage.getItem(this.STORAGE_KEY);
        if (stored) {
          projectsData = JSON.parse(stored) as SerializedGameProject[];
        }
      } else {
        // 服务器端环境，从文件系统加载
        projectsData = this.loadFromFileSystem();
      }

      // 加载项目数据
      projectsData.forEach((data: SerializedGameProject) => {
        const project: GameProject = {
          ...data,
          createdAt: new Date(data.createdAt),
          updatedAt: new Date(data.updatedAt)
        };
        this.projects.set(project.id, project);
      });

      console.log(`[GameProjectManager] 加载了 ${this.projects.size} 个项目`);
    } catch (error) {
      console.error('[GameProjectManager] 加载项目失败:', error);
    }
  }

  /**
   * 导出项目数据
   */
  public exportProject(projectId: string): string | null {
    const project = this.getProject(projectId);
    if (!project) return null;
    
    return JSON.stringify(project, null, 2);
  }

  /**
   * 导入项目数据
   */
  public importProject(projectData: string): GameProject | null {
    try {
      const data = JSON.parse(projectData);
      const project: GameProject = {
        ...data,
        id: this.generateProjectId(), // 生成新ID避免冲突
        createdAt: new Date(data.createdAt),
        updatedAt: new Date()
      };
      
      this.projects.set(project.id, project);
      this.saveProjects();
      
      return project;
    } catch (error) {
      console.error('[GameProjectManager] 导入项目失败:', error);
      return null;
    }
  }

  /**
   * 保存到文件系统（服务器端）
   */
  private async saveToFileSystem(projectsData: SerializedGameProject[]): Promise<void> {
    try {
      // 确保数据目录存在
      const dataDir = path.dirname(this.SERVER_STORAGE_PATH);
      await fs.mkdir(dataDir, { recursive: true });

      // 保存项目数据
      await fs.writeFile(this.SERVER_STORAGE_PATH, JSON.stringify(projectsData, null, 2), 'utf-8');
      console.log(`[GameProjectManager] 保存了 ${projectsData.length} 个项目到文件系统`);
    } catch (error) {
      console.error('[GameProjectManager] 文件系统保存失败:', error);
    }
  }

  /**
   * 从文件系统加载（服务器端）
   */
  private loadFromFileSystem(): SerializedGameProject[] {
    try {
      // 同步读取文件（在构造函数中调用，需要同步）
      const fs_sync = eval('require')('fs');
      if (fs_sync.existsSync(this.SERVER_STORAGE_PATH)) {
        const data = fs_sync.readFileSync(this.SERVER_STORAGE_PATH, 'utf-8');
        const projectsData = JSON.parse(data) as SerializedGameProject[];
        console.log(`[GameProjectManager] 从文件系统加载了 ${projectsData.length} 个项目`);
        return projectsData;
      }
    } catch (error) {
      console.error('[GameProjectManager] 文件系统加载失败:', error);
    }
    return [];
  }
}
