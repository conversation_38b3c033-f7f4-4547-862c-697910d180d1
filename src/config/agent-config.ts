/**
 * PlayableGen Agent 统一配置文件
 * 基于环境变量的类型安全配置管理
 */

// 环境变量解析辅助函数
function getEnvNumber(key: string, defaultValue: number): number {
  const value = process.env[key];
  if (!value) return defaultValue;
  const parsed = parseInt(value, 10);
  return isNaN(parsed) ? defaultValue : parsed;
}

function getEnvString(key: string, defaultValue: string): string {
  return process.env[key] || defaultValue;
}

function getEnvBoolean(key: string, defaultValue: boolean): boolean {
  const value = process.env[key];
  if (!value) return defaultValue;
  return value.toLowerCase() === 'true';
}

// 基于环境变量的配置
export const AGENT_CONFIG = {
  // Claude API配置
  API: {
    KEY: getEnvString('CLAUDE_API_KEY', 'sk-CTLTnYuCBuDaraGx30Bd2a98C30646838c5f1f568b182183'),
    BASE_URL: getEnvString('CLAUDE_BASE_URL', 'http://oneapi.funplus.com/v1/'),
    MODEL: getEnvString('CLAUDE_MODEL', 'claude-4-sonnet')
  },

  // Token配置
  MAX_TOKENS: {
    DEFAULT: getEnvNumber('DEFAULT_MAX_TOKENS', 20000),
    DESIGN: getEnvNumber('DESIGN_MAX_TOKENS', 20000),
    CODE_GENERATION: getEnvNumber('CODE_MAX_TOKENS', 35000), // 增加代码生成token限制
    SMALL_TASK: getEnvNumber('SMALL_TASK_MAX_TOKENS', 8000),
    LARGE_TASK: getEnvNumber('LARGE_TASK_MAX_TOKENS', 30000)
  },

  // Temperature配置
  TEMPERATURE: {
    DEFAULT: parseFloat(getEnvString('DEFAULT_TEMPERATURE', '0.7')),
    CREATIVE: parseFloat(getEnvString('CREATIVE_TEMPERATURE', '0.9')),
    PRECISE: parseFloat(getEnvString('PRECISE_TEMPERATURE', '0.2')),
    BALANCED: parseFloat(getEnvString('BALANCED_TEMPERATURE', '0.5'))
  },

  // 超时配置（毫秒）
  TIMEOUT: {
    DEFAULT: getEnvNumber('DEFAULT_TIMEOUT', 120000),
    SHORT: getEnvNumber('SHORT_TIMEOUT', 60000),
    LONG: getEnvNumber('LONG_TIMEOUT', 300000),
    EXTRA_LONG: getEnvNumber('EXTRA_LONG_TIMEOUT', 600000)
  },

  // 应用配置
  APP: {
    OUTPUT_DIR: getEnvString('GAMES_OUTPUT_DIR', './public/games'),
    DEBUG_LOGGING: getEnvBoolean('DEBUG_LOGGING', true),
    ENABLE_TOKEN_TRACKING: getEnvBoolean('ENABLE_TOKEN_TRACKING', true)
  }
} as const;

// Agent特定配置
export const AGENT_SPECIFIC_CONFIG = {
  GameDesignAgent: {
    maxTokens: AGENT_CONFIG.MAX_TOKENS.DESIGN,
    temperature: AGENT_CONFIG.TEMPERATURE.CREATIVE,
    timeout: AGENT_CONFIG.TIMEOUT.DEFAULT
  },
  
  CodeGenerationAgent: {
    maxTokens: AGENT_CONFIG.MAX_TOKENS.CODE_GENERATION,
    temperature: AGENT_CONFIG.TEMPERATURE.PRECISE,
    timeout: AGENT_CONFIG.TIMEOUT.LONG
  },
  
  BaseAgent: {
    maxTokens: AGENT_CONFIG.MAX_TOKENS.DEFAULT,
    temperature: AGENT_CONFIG.TEMPERATURE.DEFAULT,
    timeout: AGENT_CONFIG.TIMEOUT.DEFAULT
  }
} as const;

// 导出便捷函数
export function getAgentConfig(agentType: keyof typeof AGENT_SPECIFIC_CONFIG) {
  return AGENT_SPECIFIC_CONFIG[agentType] || AGENT_SPECIFIC_CONFIG.BaseAgent;
}

// 导出默认值（向后兼容）
export const DEFAULT_MAX_TOKENS = AGENT_CONFIG.MAX_TOKENS.DEFAULT;
export const DEFAULT_TEMPERATURE = AGENT_CONFIG.TEMPERATURE.DEFAULT;
export const DEFAULT_TIMEOUT = AGENT_CONFIG.TIMEOUT.DEFAULT;
