/**
 * HTML导出服务 - 用于将PlayableAgent场景导出为独立的HTML5广告文件
 * 支持广告平台部署，包含完整的Three.js场景和交互逻辑
 */

import * as THREE from 'three';

export interface ExportOptions {
  includeModels?: boolean;
  includeAnimations?: boolean;
  includeScripts?: boolean;
  compressOutput?: boolean;
  maxFileSize?: number; // MB
}

export interface SceneExportData {
  nodes: Record<string, any>;
  scripts: Array<{
    id: string;
    name: string;
    content: string;
    isActive: boolean;
  }>;
  models: Record<string, string>; // base64 encoded models
  textures: Record<string, string>; // base64 encoded textures
  sceneConfig: {
    background: string;
    fog?: any;
    lights: any[];
    camera: {
      position: [number, number, number];
      target: [number, number, number];
    };
  };
}

export class HTMLExporter {
  private options: ExportOptions;

  constructor(options: ExportOptions = {}) {
    this.options = {
      includeModels: true,
      includeAnimations: true,
      includeScripts: true,
      compressOutput: true,
      maxFileSize: 5, // 5MB default for ad platforms
      ...options
    };
  }

  /**
   * 导出当前场景为HTML5广告文件
   */
  async exportScene(
    scene: THREE.Scene,
    camera: THREE.Camera,
    nodeProperties: Record<string, any>,
    persistentFeatures: any[],
    models: Record<string, any> = {}
  ): Promise<string> {
    try {
      // 1. 收集场景数据
      const sceneData = await this.collectSceneData(
        scene,
        camera,
        nodeProperties,
        persistentFeatures,
        models
      );

      // 2. 生成HTML内容
      const htmlContent = await this.generateHTML(sceneData);

      // 3. 压缩优化（如果启用）
      if (this.options.compressOutput) {
        return this.compressHTML(htmlContent);
      }

      return htmlContent;
    } catch (error) {
      console.error('HTML导出失败:', error);
      throw new Error(`导出失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 收集场景数据
   */
  private async collectSceneData(
    scene: THREE.Scene,
    camera: THREE.Camera,
    nodeProperties: Record<string, any>,
    persistentFeatures: any[],
    models: Record<string, any>
  ): Promise<SceneExportData> {
    const sceneData: SceneExportData = {
      nodes: nodeProperties,
      scripts: [],
      models: {},
      textures: {},
      sceneConfig: {
        background: scene.background ? this.serializeBackground(scene.background) : '#000000',
        lights: [],
        camera: {
          position: [camera.position.x, camera.position.y, camera.position.z],
          target: [0, 0, 0] // 默认目标点
        }
      }
    };

    // 收集脚本数据
    if (this.options.includeScripts) {
      sceneData.scripts = persistentFeatures.map(feature => ({
        id: feature.id || Math.random().toString(36).substring(2, 11),
        name: feature.name || 'Unnamed Script',
        content: feature.content || '',
        isActive: feature.isActive || false
      }));
    }

    // 收集光照数据
    scene.traverse((object) => {
      if (object instanceof THREE.Light) {
        sceneData.sceneConfig.lights.push(this.serializeLight(object));
      }
    });

    // 收集模型数据（如果启用）
    if (this.options.includeModels) {
      for (const [key, modelData] of Object.entries(models)) {
        if (modelData && modelData.url) {
          try {
            const base64Data = await this.convertToBase64(modelData.url);
            sceneData.models[key] = base64Data;
          } catch (error) {
            console.warn(`无法转换模型 ${key}:`, error);
          }
        }
      }
    }

    return sceneData;
  }

  /**
   * 序列化背景
   */
  private serializeBackground(background: THREE.Color | THREE.Texture | null): string {
    if (background instanceof THREE.Color) {
      return `#${background.getHexString()}`;
    }
    return '#000000';
  }

  /**
   * 序列化光照
   */
  private serializeLight(light: THREE.Light): any {
    const lightData: any = {
      type: light.type,
      color: light.color.getHex(),
      intensity: light.intensity,
      position: [light.position.x, light.position.y, light.position.z]
    };

    if (light instanceof THREE.DirectionalLight) {
      lightData.target = [
        light.target.position.x,
        light.target.position.y,
        light.target.position.z
      ];
    }

    return lightData;
  }

  /**
   * 将文件转换为Base64
   */
  private async convertToBase64(url: string): Promise<string> {
    try {
      const response = await fetch(url);
      const blob = await response.blob();
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result as string);
        reader.onerror = reject;
        reader.readAsDataURL(blob);
      });
    } catch (error) {
      throw new Error(`无法加载文件 ${url}: ${error}`);
    }
  }

  /**
   * 生成HTML内容
   */
  private async generateHTML(sceneData: SceneExportData): Promise<string> {
    // 获取Three.js库代码（简化版本，实际应该从CDN或本地文件读取）
    const threeJsCode = await this.getThreeJsCode();
    
    // 生成场景初始化代码
    const sceneInitCode = this.generateSceneInitCode(sceneData);
    
    // 生成交互逻辑代码
    const interactionCode = this.generateInteractionCode(sceneData);

    return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PlayableAgent Game</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            background: #000;
            font-family: Arial, sans-serif;
        }
        #gameContainer {
            width: 100vw;
            height: 100vh;
            position: relative;
        }
        #gameCanvas {
            display: block;
            width: 100%;
            height: 100%;
        }
        #ui {
            position: absolute;
            top: 10px;
            left: 10px;
            color: white;
            z-index: 100;
        }
        #cta {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: #ff6b35;
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 18px;
            border-radius: 25px;
            cursor: pointer;
            z-index: 100;
            box-shadow: 0 4px 15px rgba(255, 107, 53, 0.4);
            transition: all 0.3s ease;
        }
        #cta:hover {
            background: #e55a2b;
            transform: translateX(-50%) translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 107, 53, 0.6);
        }
    </style>
</head>
<body>
    <div id="gameContainer">
        <canvas id="gameCanvas"></canvas>
        <div id="ui">
            <div>点击并拖拽控制视角</div>
            <div>WASD键移动角色</div>
        </div>
        <button id="cta" onclick="handleCTA()">立即下载游戏</button>
    </div>

    <script>
        // Three.js库代码
        ${threeJsCode}
        
        // 场景数据
        const SCENE_DATA = ${JSON.stringify(sceneData, null, 2)};
        
        // 场景初始化代码
        ${sceneInitCode}
        
        // 交互逻辑代码
        ${interactionCode}
        
        // CTA处理
        function handleCTA() {
            // 广告平台会替换这个函数
            if (window.mraid && window.mraid.open) {
                window.mraid.open('https://play.google.com/store/apps/details?id=your.app.id');
            } else if (window.ExitApi && window.ExitApi.exit) {
                window.ExitApi.exit();
            } else {
                window.open('https://play.google.com/store/apps/details?id=your.app.id', '_blank');
            }
        }
        
        // 初始化游戏
        window.addEventListener('DOMContentLoaded', initGame);
    </script>
</body>
</html>`;
  }

  /**
   * 获取Three.js代码
   */
  private async getThreeJsCode(): Promise<string> {
    try {
      // 尝试从CDN获取Three.js代码
      const threeJsUrl = 'https://unpkg.com/three@0.178.0/build/three.min.js';
      const response = await fetch(threeJsUrl);

      if (response.ok) {
        const threeJsCode = await response.text();

        // 添加OrbitControls（如果需要）
        const orbitControlsUrl = 'https://unpkg.com/three@0.178.0/examples/js/controls/OrbitControls.js';
        const orbitResponse = await fetch(orbitControlsUrl);
        let orbitControlsCode = '';

        if (orbitResponse.ok) {
          orbitControlsCode = await orbitResponse.text();
        }

        return `
          // Three.js 核心库 (v0.178.0)
          ${threeJsCode}

          // OrbitControls
          ${orbitControlsCode}

          // 确保THREE对象可用
          if (typeof THREE === 'undefined') {
            console.error('Three.js 加载失败');
          } else {
            console.log('Three.js 已成功加载');
          }
        `;
      } else {
        throw new Error('无法从CDN获取Three.js代码');
      }
    } catch (error) {
      console.warn('从CDN获取Three.js失败，使用本地备用方案:', error);

      // 备用方案：返回基础的Three.js结构
      return this.getThreeJsFallback();
    }
  }

  /**
   * Three.js备用代码（简化版本）
   */
  private getThreeJsFallback(): string {
    return `
      // Three.js 备用代码（简化版本）
      // 注意：这是一个简化的实现，仅用于演示

      window.THREE = {
        Scene: function() {
          this.children = [];
          this.background = null;
          this.add = function(object) { this.children.push(object); };
          this.remove = function(object) {
            const index = this.children.indexOf(object);
            if (index > -1) this.children.splice(index, 1);
          };
          this.traverse = function(callback) {
            callback(this);
            this.children.forEach(child => {
              if (child.traverse) child.traverse(callback);
            });
          };
        },

        PerspectiveCamera: function(fov, aspect, near, far) {
          this.fov = fov;
          this.aspect = aspect;
          this.near = near;
          this.far = far;
          this.position = { x: 0, y: 0, z: 0, set: function(x, y, z) { this.x = x; this.y = y; this.z = z; } };
          this.updateProjectionMatrix = function() {};
        },

        WebGLRenderer: function(options) {
          this.domElement = options.canvas || document.createElement('canvas');
          this.shadowMap = { enabled: false, type: null };
          this.setSize = function(width, height) {
            this.domElement.width = width;
            this.domElement.height = height;
          };
          this.render = function(scene, camera) {
            // 简化的渲染逻辑
            const ctx = this.domElement.getContext('2d');
            if (ctx) {
              ctx.fillStyle = scene.background || '#000000';
              ctx.fillRect(0, 0, this.domElement.width, this.domElement.height);
            }
          };
        },

        BoxGeometry: function(width, height, depth) {
          this.width = width || 1;
          this.height = height || 1;
          this.depth = depth || 1;
        },

        MeshPhongMaterial: function(options) {
          this.color = options.color || 0x00ff00;
        },

        Mesh: function(geometry, material) {
          this.geometry = geometry;
          this.material = material;
          this.position = { x: 0, y: 0, z: 0, set: function(x, y, z) { this.x = x; this.y = y; this.z = z; } };
          this.rotation = { x: 0, y: 0, z: 0, set: function(x, y, z) { this.x = x; this.y = y; this.z = z; } };
          this.scale = { x: 1, y: 1, z: 1, set: function(x, y, z) { this.x = x; this.y = y; this.z = z; } };
          this.castShadow = false;
          this.receiveShadow = false;
        },

        AmbientLight: function(color, intensity) {
          this.color = { getHex: function() { return color; } };
          this.intensity = intensity || 1;
          this.type = 'AmbientLight';
        },

        DirectionalLight: function(color, intensity) {
          this.color = { getHex: function() { return color; } };
          this.intensity = intensity || 1;
          this.type = 'DirectionalLight';
          this.position = { x: 0, y: 0, z: 0, set: function(x, y, z) { this.x = x; this.y = y; this.z = z; } };
          this.target = { position: { x: 0, y: 0, z: 0 } };
          this.castShadow = false;
        },

        Color: function(color) {
          this.r = 1;
          this.g = 1;
          this.b = 1;
          this.getHexString = function() { return 'ffffff'; };
          if (typeof color === 'string') {
            this.set(color);
          }
          this.set = function(color) {
            if (typeof color === 'string') {
              const hex = color.replace('#', '');
              this.r = parseInt(hex.substr(0, 2), 16) / 255;
              this.g = parseInt(hex.substr(2, 2), 16) / 255;
              this.b = parseInt(hex.substr(4, 2), 16) / 255;
            }
          };
        },

        PCFSoftShadowMap: 'PCFSoftShadowMap'
      };

      console.log('Three.js 备用代码已加载');
    `;
  }

  /**
   * 生成场景初始化代码
   */
  private generateSceneInitCode(sceneData: SceneExportData): string {
    return `
        let scene, camera, renderer, controls;
        let gameObjects = {};
        
        function initGame() {
            // 创建场景
            scene = new THREE.Scene();
            scene.background = new THREE.Color('${sceneData.sceneConfig.background}');
            
            // 创建相机
            camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
            camera.position.set(${sceneData.sceneConfig.camera.position.join(', ')});
            
            // 创建渲染器
            const canvas = document.getElementById('gameCanvas');
            renderer = new THREE.WebGLRenderer({ canvas: canvas, antialias: true });
            renderer.setSize(window.innerWidth, window.innerHeight);
            renderer.shadowMap.enabled = true;
            renderer.shadowMap.type = THREE.PCFSoftShadowMap;
            
            // 创建控制器
            // controls = new THREE.OrbitControls(camera, renderer.domElement);
            
            // 创建光照
            ${this.generateLightingCode(sceneData.sceneConfig.lights)}
            
            // 创建场景对象
            ${this.generateSceneObjectsCode(sceneData.nodes)}
            
            // 执行脚本
            ${this.generateScriptExecutionCode(sceneData.scripts)}
            
            // 开始渲染循环
            animate();
            
            // 处理窗口大小变化
            window.addEventListener('resize', onWindowResize);
        }
        
        function animate() {
            requestAnimationFrame(animate);
            
            // 更新控制器
            if (controls) controls.update();
            
            // 渲染场景
            renderer.render(scene, camera);
        }
        
        function onWindowResize() {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        }
    `;
  }

  /**
   * 生成光照代码
   */
  private generateLightingCode(lights: Array<{
    type: string;
    color: number;
    intensity: number;
    position: [number, number, number];
    target?: [number, number, number];
  }>): string {
    return lights.map(light => {
      switch (light.type) {
        case 'AmbientLight':
          return `
            const ambientLight = new THREE.AmbientLight(0x${light.color.toString(16)}, ${light.intensity});
            scene.add(ambientLight);
          `;
        case 'DirectionalLight':
          return `
            const directionalLight = new THREE.DirectionalLight(0x${light.color.toString(16)}, ${light.intensity});
            directionalLight.position.set(${light.position.join(', ')});
            directionalLight.castShadow = true;
            scene.add(directionalLight);
          `;
        default:
          return '';
      }
    }).join('\n');
  }

  /**
   * 生成场景对象代码
   */
  private generateSceneObjectsCode(nodes: Record<string, {
    position?: { x: number; y: number; z: number };
    rotation?: { x: number; y: number; z: number };
    scale?: { x: number; y: number; z: number };
    color?: string;
    model?: string;
    material?: string;
    animation?: string;
  }>): string {
    return Object.entries(nodes).map(([nodeId, nodeData]) => {
      return `
        // 创建节点: ${nodeId}
        const geometry_${nodeId} = new THREE.BoxGeometry(
          ${nodeData.scale?.x || 1},
          ${nodeData.scale?.y || 1},
          ${nodeData.scale?.z || 1}
        );
        const material_${nodeId} = new THREE.MeshPhongMaterial({
          color: 0x${(nodeData.color || '#00ff00').replace('#', '')}
        });
        const mesh_${nodeId} = new THREE.Mesh(geometry_${nodeId}, material_${nodeId});
        mesh_${nodeId}.position.set(
          ${nodeData.position?.x || 0},
          ${nodeData.position?.y || 0},
          ${nodeData.position?.z || 0}
        );
        mesh_${nodeId}.rotation.set(
          ${nodeData.rotation?.x || 0},
          ${nodeData.rotation?.y || 0},
          ${nodeData.rotation?.z || 0}
        );
        mesh_${nodeId}.castShadow = true;
        mesh_${nodeId}.receiveShadow = true;
        scene.add(mesh_${nodeId});
        gameObjects['${nodeId}'] = mesh_${nodeId};
      `;
    }).join('\n');
  }

  /**
   * 生成脚本执行代码
   */
  private generateScriptExecutionCode(scripts: Array<{
    id: string;
    name: string;
    content: string;
    isActive: boolean;
  }>): string {
    const activeScripts = scripts.filter(script => script.isActive);
    
    return activeScripts.map(script => `
      // 执行脚本: ${script.name}
      try {
        ${script.content}
      } catch (error) {
        console.error('脚本执行错误 (${script.name}):', error);
      }
    `).join('\n');
  }

  /**
   * 生成交互逻辑代码
   */
  private generateInteractionCode(_sceneData: SceneExportData): string {
    return `
        // 键盘控制
        const keys = {};
        
        document.addEventListener('keydown', (event) => {
            keys[event.code] = true;
        });
        
        document.addEventListener('keyup', (event) => {
            keys[event.code] = false;
        });
        
        // 触摸控制（移动设备）
        let touchStartX = 0;
        let touchStartY = 0;
        
        document.addEventListener('touchstart', (event) => {
            touchStartX = event.touches[0].clientX;
            touchStartY = event.touches[0].clientY;
        });
        
        document.addEventListener('touchmove', (event) => {
            event.preventDefault();
            const touchX = event.touches[0].clientX;
            const touchY = event.touches[0].clientY;
            
            const deltaX = touchX - touchStartX;
            const deltaY = touchY - touchStartY;
            
            // 简单的触摸控制逻辑
            if (Math.abs(deltaX) > 50) {
                // 水平滑动
                if (deltaX > 0) {
                    // 向右滑动
                } else {
                    // 向左滑动
                }
            }
        });
    `;
  }

  /**
   * 压缩HTML内容
   */
  private compressHTML(html: string): string {
    // 简单的压缩：移除多余空白和注释
    return html
      .replace(/\s+/g, ' ')
      .replace(/<!--[\s\S]*?-->/g, '')
      .replace(/>\s+</g, '><')
      .trim();
  }

  /**
   * 下载HTML文件
   */
  static downloadHTML(content: string, filename: string = 'playable-game.html'): void {
    const blob = new Blob([content], { type: 'text/html;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    link.style.display = 'none';
    
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    URL.revokeObjectURL(url);
  }
}
