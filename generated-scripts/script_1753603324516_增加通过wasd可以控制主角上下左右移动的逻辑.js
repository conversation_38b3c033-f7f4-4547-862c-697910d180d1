/**
 * 自动生成的脚本文件
 * 
 * 脚本ID: script_1753603324516
 * 脚本名称: 增加通过wasd可以控制主角上下左右移动的逻辑
 * 生成时间: 2025/7/27 16:02:04
 * 功能类型: utility
 * 目标节点: mesh
 * 依赖项: three
 * 描述: 增加通过wasd可以控制主角上下左右移动的逻辑
 */

function executeScript(scene, camera, renderer) {
  // 获取主角对象
  const player = scene.getObjectByName('box_1753425549287');
  if (!player) {
    console.warn('Player object not found');
    return;
  }

  // 键盘状态追踪
  const keys = {
    w: false,
    a: false,
    s: false,
    d: false
  };

  // 移动速度（单位/秒）
  const moveSpeed = 5;
  let lastTime = performance.now();
  let animationId;

  // 键盘事件处理
  function onKeyDown(event) {
    event.preventDefault();
    const key = event.key.toLowerCase();
    if (keys.hasOwnProperty(key)) {
      keys[key] = true;
    }
  }

  function onKeyUp(event) {
    event.preventDefault();
    const key = event.key.toLowerCase();
    if (keys.hasOwnProperty(key)) {
      keys[key] = false;
    }
  }

  // 更新主角位置
  function updatePlayer(deltaTime) {
    const moveDistance = moveSpeed * deltaTime;
    
    if (keys.w) player.position.z -= moveDistance; // 向前
    if (keys.s) player.position.z += moveDistance; // 向后
    if (keys.a) player.position.x -= moveDistance; // 向左
    if (keys.d) player.position.x += moveDistance; // 向右
  }

  // 动画循环
  function animate() {
    const currentTime = performance.now();
    const deltaTime = (currentTime - lastTime) / 1000;
    lastTime = currentTime;

    updatePlayer(deltaTime);
    
    animationId = requestAnimationFrame(animate);
  }

  // 添加事件监听器
  document.addEventListener('keydown', onKeyDown);
  document.addEventListener('keyup', onKeyUp);

  // 开始动画
  animate();

  return function cleanup() {
    // 清理事件监听器
    document.removeEventListener('keydown', onKeyDown);
    document.removeEventListener('keyup', onKeyUp);
    
    // 停止动画循环
    if (animationId) {
      cancelAnimationFrame(animationId);
    }
  };
}