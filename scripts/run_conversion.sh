#!/bin/bash

# PlayableGen FBX到GLB转换执行脚本
# 使用Blender后台模式执行Python转换脚本

echo "=== PlayableGen FBX到GLB转换工具 ==="
echo "开始时间: $(date)"

# 检查Blender是否安装
if ! command -v blender &> /dev/null; then
    echo "错误: 未找到Blender命令。请确保Blender已正确安装并添加到PATH中。"
    echo "macOS安装方法: brew install blender"
    echo "或从官网下载: https://www.blender.org/download/"
    exit 1
fi

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PYTHON_SCRIPT="$SCRIPT_DIR/fbx_to_glb_converter.py"

# 检查Python脚本是否存在
if [ ! -f "$PYTHON_SCRIPT" ]; then
    echo "错误: 未找到转换脚本 $PYTHON_SCRIPT"
    exit 1
fi

echo "Blender版本信息:"
blender --version

echo ""
echo "开始执行FBX到GLB转换..."
echo "转换脚本: $PYTHON_SCRIPT"
echo ""

# 执行Blender转换
blender --background --python "$PYTHON_SCRIPT"

# 检查执行结果
if [ $? -eq 0 ]; then
    echo ""
    echo "✅ 转换成功完成!"
    echo "输出目录: /Users/<USER>/code/AI/PlayableAgent/public/models/player/"
    echo ""
    echo "生成的文件:"
    echo "- player.glb (角色模型)"
    echo "- animations/ (动画文件夹)"
    echo "- textures/ (贴图文件夹)"
    echo "- conversion_report.json (转换报告)"
else
    echo ""
    echo "❌ 转换过程中出现错误"
    echo "请检查上方的错误信息"
    exit 1
fi

echo ""
echo "结束时间: $(date)"
echo "=== 转换完成 ==="
