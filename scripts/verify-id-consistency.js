#!/usr/bin/env node

/**
 * ID一致性验证脚本
 * 验证项目ID与生成文件ID的一致性
 */

const fs = require('fs').promises;
const path = require('path');

async function verifyIdConsistency() {
  console.log('🔍 开始验证ID一致性...');

  try {
    // 1. 读取项目数据
    const projectsPath = path.join(process.cwd(), 'data', 'projects.json');
    const projectsData = await fs.readFile(projectsPath, 'utf-8');
    const projects = JSON.parse(projectsData);

    console.log(`📋 验证 ${projects.length} 个项目的ID一致性`);

    let allConsistent = true;
    const results = [];

    for (const project of projects) {
      const projectId = project.id;
      console.log(`\n🔍 验证项目: ${projectId}`);

      const result = {
        projectId,
        status: project.status,
        hasComponent: false,
        hasGameDir: false,
        componentPath: null,
        gameDirPath: null,
        consistent: false
      };

      // 2. 检查组件文件
      const expectedComponentFile = `${projectId}.tsx`;
      const componentPath = path.join(process.cwd(), 'components', 'generated', expectedComponentFile);
      
      try {
        await fs.access(componentPath);
        result.hasComponent = true;
        result.componentPath = componentPath;
        console.log(`   ✅ 组件文件存在: ${expectedComponentFile}`);
      } catch {
        console.log(`   ❌ 组件文件不存在: ${expectedComponentFile}`);
      }

      // 3. 检查游戏目录
      const expectedGameDir = projectId;
      const gameDirPath = path.join(process.cwd(), 'generated-games', expectedGameDir);
      
      try {
        const stats = await fs.stat(gameDirPath);
        if (stats.isDirectory()) {
          result.hasGameDir = true;
          result.gameDirPath = gameDirPath;
          console.log(`   ✅ 游戏目录存在: ${expectedGameDir}`);
        }
      } catch {
        console.log(`   ❌ 游戏目录不存在: ${expectedGameDir}`);
      }

      // 4. 检查项目状态
      console.log(`   📊 项目状态: ${project.status}`);

      // 5. 判断一致性
      if (project.status === 'completed') {
        result.consistent = result.hasComponent && result.hasGameDir;
      } else {
        result.consistent = true; // 未完成的项目不需要文件
      }

      if (result.consistent) {
        console.log(`   🎉 项目 ${projectId} ID一致性验证通过`);
      } else {
        console.log(`   ⚠️  项目 ${projectId} ID一致性验证失败`);
        allConsistent = false;
      }

      results.push(result);
    }

    // 6. 输出总结
    console.log('\n📊 验证结果总结:');
    console.log('='.repeat(50));
    
    for (const result of results) {
      const status = result.consistent ? '✅ 通过' : '❌ 失败';
      console.log(`${result.projectId}: ${status}`);
      
      if (!result.consistent) {
        if (!result.hasComponent) {
          console.log(`  - 缺少组件文件: components/generated/${result.projectId}.tsx`);
        }
        if (!result.hasGameDir) {
          console.log(`  - 缺少游戏目录: generated-games/${result.projectId}/`);
        }
      }
    }

    console.log('='.repeat(50));
    
    if (allConsistent) {
      console.log('🎉 所有项目ID一致性验证通过！');
      console.log('✅ Canvas页面应该能够正确加载游戏组件');
    } else {
      console.log('⚠️  存在ID一致性问题，需要修复');
      console.log('💡 运行 node scripts/fix-id-consistency.js 进行修复');
    }

    return allConsistent;

  } catch (error) {
    console.error('❌ 验证过程中出错:', error);
    return false;
  }
}

// 运行验证脚本
if (require.main === module) {
  verifyIdConsistency()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(console.error);
}

module.exports = { verifyIdConsistency };
