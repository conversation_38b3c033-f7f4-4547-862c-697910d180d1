#!/usr/bin/env node

/**
 * ID一致性修复脚本
 * 修复项目ID与生成文件ID不一致的问题
 */

const fs = require('fs').promises;
const path = require('path');

async function fixIdConsistency() {
  console.log('🔧 开始修复ID一致性问题...');

  try {
    // 1. 读取项目数据
    const projectsPath = path.join(process.cwd(), 'data', 'projects.json');
    const projectsData = await fs.readFile(projectsPath, 'utf-8');
    const projects = JSON.parse(projectsData);

    console.log(`📋 找到 ${projects.length} 个项目`);

    for (const project of projects) {
      const projectId = project.id;
      console.log(`\n🔍 检查项目: ${projectId}`);

      // 2. 检查components/generated目录
      const componentsDir = path.join(process.cwd(), 'components', 'generated');
      const generatedGamesDir = path.join(process.cwd(), 'generated-games');

      try {
        const componentFiles = await fs.readdir(componentsDir);
        const gameDirectories = await fs.readdir(generatedGamesDir);

        console.log(`   📁 Components目录文件: ${componentFiles.join(', ')}`);
        console.log(`   📁 Generated-games目录: ${gameDirectories.join(', ')}`);

        // 3. 查找不匹配的文件
        const expectedComponentFile = `${projectId}.tsx`;
        const expectedGameDir = projectId;

        const hasCorrectComponent = componentFiles.includes(expectedComponentFile);
        const hasCorrectGameDir = gameDirectories.includes(expectedGameDir);

        console.log(`   ✅ 正确的组件文件存在: ${hasCorrectComponent}`);
        console.log(`   ✅ 正确的游戏目录存在: ${hasCorrectGameDir}`);

        // 4. 查找需要重命名的文件
        const wrongComponentFiles = componentFiles.filter(file => 
          file.startsWith('game_') && file !== expectedComponentFile
        );
        const wrongGameDirs = gameDirectories.filter(dir => 
          dir.startsWith('game_') && dir !== expectedGameDir
        );

        if (wrongComponentFiles.length > 0) {
          console.log(`   🔄 需要重命名的组件文件: ${wrongComponentFiles.join(', ')}`);
          
          for (const wrongFile of wrongComponentFiles) {
            const oldPath = path.join(componentsDir, wrongFile);
            const newPath = path.join(componentsDir, expectedComponentFile);
            
            console.log(`   📝 重命名: ${wrongFile} -> ${expectedComponentFile}`);
            await fs.rename(oldPath, newPath);
          }
        }

        if (wrongGameDirs.length > 0) {
          console.log(`   🔄 需要重命名的游戏目录: ${wrongGameDirs.join(', ')}`);
          
          for (const wrongDir of wrongGameDirs) {
            const oldPath = path.join(generatedGamesDir, wrongDir);
            const newPath = path.join(generatedGamesDir, expectedGameDir);
            
            console.log(`   📝 重命名目录: ${wrongDir} -> ${expectedGameDir}`);
            await fs.rename(oldPath, newPath);
          }
        }

        // 5. 验证修复结果
        const updatedComponentFiles = await fs.readdir(componentsDir);
        const updatedGameDirectories = await fs.readdir(generatedGamesDir);

        const finalHasCorrectComponent = updatedComponentFiles.includes(expectedComponentFile);
        const finalHasCorrectGameDir = updatedGameDirectories.includes(expectedGameDir);

        console.log(`   ✅ 修复后组件文件正确: ${finalHasCorrectComponent}`);
        console.log(`   ✅ 修复后游戏目录正确: ${finalHasCorrectGameDir}`);

        if (finalHasCorrectComponent && finalHasCorrectGameDir) {
          console.log(`   🎉 项目 ${projectId} ID一致性修复完成`);
        } else {
          console.log(`   ⚠️  项目 ${projectId} 仍存在问题`);
        }

      } catch (error) {
        console.error(`   ❌ 处理项目 ${projectId} 时出错:`, error.message);
      }
    }

    console.log('\n🎉 ID一致性修复完成！');

  } catch (error) {
    console.error('❌ 修复过程中出错:', error);
    process.exit(1);
  }
}

// 运行修复脚本
if (require.main === module) {
  fixIdConsistency().catch(console.error);
}

module.exports = { fixIdConsistency };
