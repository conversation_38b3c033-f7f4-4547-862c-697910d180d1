#!/usr/bin/env python3
"""
FBX到GLB批量转换脚本
基于Blender Python API实现FBX文件到GLB格式的批量转换
适用于PlayableGen木材收集游戏3D角色模型替换项目

使用方法:
blender --background --python fbx_to_glb_converter.py

作者: PlayableGen开发团队
日期: 2025-01-09
"""

import bpy
import os
import sys
import time
import json
from pathlib import Path

# 配置路径
CONFIG = {
    "input_paths": {
        "character_model": "/Users/<USER>/code/AI/PlayableAgent/WoodCollect/gongcheng/assets/resources/model/jiek/Naide_DaTou@rig无枪.fbx",
        "animations_dir": "/Users/<USER>/code/AI/PlayableAgent/WoodCollect/gongcheng/assets/resources/model/naide/",
        "textures_dir": "/Users/<USER>/code/AI/PlayableAgent/WoodCollect/gongcheng/assets/resources/model/jiek/Cocos_TX_512X512_Jpg/",
        # 金币模型配置
        "coin_model": "/Users/<USER>/code/AI/PlayableAgent/WoodCollect/gongcheng/assets/resources/model/coin/coin.fbx",
        "coin_textures_dir": "/Users/<USER>/code/AI/PlayableAgent/WoodCollect/gongcheng/assets/resources/model/coin/",
        # 夹板场景模型配置
        "jiaban_model": "/Users/<USER>/code/AI/PlayableAgent/WoodCollect/gongcheng/assets/resources/model/jiaban/Amuban.fbx",
        "jiaban_textures_dir": "/Users/<USER>/code/AI/PlayableAgent/WoodCollect/gongcheng/assets/resources/model/jiaban/TX/",
        # 围栏模型配置
        "zhalan_model": "/Users/<USER>/code/AI/PlayableAgent/WoodCollect/gongcheng/assets/resources/model/qitamb/zhalan/zhalan.fbx",
        "zhalan_textures_dir": "/Users/<USER>/code/AI/PlayableAgent/WoodCollect/gongcheng/assets/resources/model/qitamb/TX/",
        # 木材模型配置
        "wood_model": "/Users/<USER>/code/AI/PlayableAgent/WoodCollect/gongcheng/assets/resources/model/wood/wood.fbx",
        "wood_textures_dir": "/Users/<USER>/code/AI/PlayableAgent/WoodCollect/gongcheng/assets/resources/model/wood/"
    },
    "output_paths": {
        "base_dir": "/Users/<USER>/code/AI/PlayableAgent/public/models/player/",
        "character_glb": "player.glb",
        "animations_dir": "animations/",
        "textures_dir": "textures/",
        # 金币输出配置
        "coin_base_dir": "/Users/<USER>/code/AI/PlayableAgent/public/models/coin/",
        "coin_glb": "coin.glb",
        "coin_textures_dir": "textures/",
        # 夹板场景输出配置
        "jiaban_base_dir": "/Users/<USER>/code/AI/PlayableAgent/public/models/jiaban/",
        "jiaban_glb": "jiaban.glb",
        "jiaban_textures_dir": "textures/",
        # 围栏输出配置
        "zhalan_base_dir": "/Users/<USER>/code/AI/PlayableAgent/public/models/zhalan/",
        "zhalan_glb": "zhalan.glb",
        "zhalan_textures_dir": "textures/",
        # 木材模型输出配置
        "wood_base_dir": "/Users/<USER>/code/AI/PlayableAgent/public/models/wood",
        "wood_glb": "wood.glb",
        "wood_textures_dir": "textures"
    },
    "animation_mapping": {
        # 根据实际动画文件名映射到标准名称
        "idle": ["idle", "Idle", "IDLE", "待机", "站立", "Naide_Datou@idle"],
        "walk": ["walk", "Walk", "WALK", "行走", "走路", "Naide_Datou@Walk"],
        "run": ["run", "Run", "RUN", "跑步", "奔跑", "Naide_Datou@run", "naide_datou@run"],
        "pickup": ["pickup", "Pickup", "PICKUP", "拾取", "捡起", "Naide_Datou@PickUp", "naide_datou@pickup"]
    }
}

class FBXToGLBConverter:
    def __init__(self):
        self.conversion_log = []
        self.start_time = time.time()
        
    def log(self, message):
        """记录转换日志"""
        timestamp = time.strftime("%H:%M:%S", time.localtime())
        log_message = f"[{timestamp}] {message}"
        print(log_message)
        self.conversion_log.append(log_message)
    
    def clear_scene(self):
        """清空当前场景"""
        self.log("清空Blender场景...")
        bpy.ops.wm.read_factory_settings(use_empty=True)
        
        # 删除默认对象
        bpy.ops.object.select_all(action='SELECT')
        bpy.ops.object.delete(use_global=False)
        
    def ensure_output_directories(self):
        """确保输出目录存在"""
        base_dir = Path(CONFIG["output_paths"]["base_dir"])
        animations_dir = base_dir / CONFIG["output_paths"]["animations_dir"]
        textures_dir = base_dir / CONFIG["output_paths"]["textures_dir"]

        # 金币输出目录
        coin_base_dir = Path(CONFIG["output_paths"]["coin_base_dir"])
        coin_textures_dir = coin_base_dir / CONFIG["output_paths"]["coin_textures_dir"]

        # 夹板场景输出目录
        jiaban_base_dir = Path(CONFIG["output_paths"]["jiaban_base_dir"])
        jiaban_textures_dir = jiaban_base_dir / CONFIG["output_paths"]["jiaban_textures_dir"]

        # 围栏输出目录
        zhalan_base_dir = Path(CONFIG["output_paths"]["zhalan_base_dir"])
        zhalan_textures_dir = zhalan_base_dir / CONFIG["output_paths"]["zhalan_textures_dir"]

        for directory in [base_dir, animations_dir, textures_dir, coin_base_dir, coin_textures_dir, jiaban_base_dir, jiaban_textures_dir, zhalan_base_dir, zhalan_textures_dir]:
            directory.mkdir(parents=True, exist_ok=True)
            self.log(f"创建目录: {directory}")
    
    def copy_textures(self):
        """复制贴图文件到输出目录"""
        import shutil

        textures_input = Path(CONFIG["input_paths"]["textures_dir"])
        textures_output = Path(CONFIG["output_paths"]["base_dir"]) / CONFIG["output_paths"]["textures_dir"]

        if not textures_input.exists():
            self.log(f"警告: 贴图目录不存在: {textures_input}")
            return

        self.log(f"复制贴图文件从 {textures_input} 到 {textures_output}")

        # 支持的贴图格式
        texture_extensions = ['.jpg', '.jpeg', '.png', '.tga', '.bmp', '.tiff']

        copied_count = 0
        for texture_file in textures_input.iterdir():
            if texture_file.suffix.lower() in texture_extensions:
                dest_file = textures_output / texture_file.name
                shutil.copy2(texture_file, dest_file)
                self.log(f"复制贴图: {texture_file.name}")
                copied_count += 1

        self.log(f"共复制 {copied_count} 个贴图文件")

    def copy_coin_textures(self):
        """复制金币贴图文件到输出目录"""
        import shutil

        textures_input = Path(CONFIG["input_paths"]["coin_textures_dir"])
        textures_output = Path(CONFIG["output_paths"]["coin_base_dir"]) / CONFIG["output_paths"]["coin_textures_dir"]

        if not textures_input.exists():
            self.log(f"警告: 金币贴图目录不存在: {textures_input}")
            return

        self.log(f"复制金币贴图文件从 {textures_input} 到 {textures_output}")

        # 支持的贴图格式
        texture_extensions = ['.jpg', '.jpeg', '.png', '.tga', '.bmp', '.tiff']

        copied_count = 0
        for texture_file in textures_input.iterdir():
            if texture_file.suffix.lower() in texture_extensions:
                dest_file = textures_output / texture_file.name
                shutil.copy2(texture_file, dest_file)
                self.log(f"复制金币贴图: {texture_file.name}")
                copied_count += 1

        self.log(f"共复制 {copied_count} 个金币贴图文件")

    def copy_jiaban_textures(self):
        """复制夹板场景贴图文件到输出目录"""
        import shutil

        textures_input = Path(CONFIG["input_paths"]["jiaban_textures_dir"])
        textures_output = Path(CONFIG["output_paths"]["jiaban_base_dir"]) / CONFIG["output_paths"]["jiaban_textures_dir"]

        if not textures_input.exists():
            self.log(f"警告: 夹板场景贴图目录不存在: {textures_input}")
            return

        self.log(f"复制夹板场景贴图文件从 {textures_input} 到 {textures_output}")

        # 支持的贴图格式
        texture_extensions = ['.jpg', '.jpeg', '.png', '.tga', '.bmp', '.tiff']

        copied_count = 0
        for texture_file in textures_input.iterdir():
            if texture_file.suffix.lower() in texture_extensions:
                dest_file = textures_output / texture_file.name
                shutil.copy2(texture_file, dest_file)
                self.log(f"复制夹板场景贴图: {texture_file.name}")
                copied_count += 1

        self.log(f"共复制 {copied_count} 个夹板场景贴图文件")

    def copy_zhalan_textures(self):
        """复制围栏贴图文件到输出目录"""
        import shutil

        textures_input = Path(CONFIG["input_paths"]["zhalan_textures_dir"])
        textures_output = Path(CONFIG["output_paths"]["zhalan_base_dir"]) / CONFIG["output_paths"]["zhalan_textures_dir"]

        if not textures_input.exists():
            self.log(f"警告: 围栏贴图目录不存在: {textures_input}")
            return

        self.log(f"复制围栏贴图文件从 {textures_input} 到 {textures_output}")

        # 支持的贴图格式
        texture_extensions = ['.jpg', '.jpeg', '.png', '.tga', '.bmp', '.tiff']

        copied_count = 0
        for texture_file in textures_input.iterdir():
            if texture_file.suffix.lower() in texture_extensions:
                dest_file = textures_output / texture_file.name
                shutil.copy2(texture_file, dest_file)
                self.log(f"复制围栏贴图: {texture_file.name}")
                copied_count += 1

        self.log(f"共复制 {copied_count} 个围栏贴图文件")

    def copy_wood_textures(self):
        """复制木材贴图文件"""
        self.log("复制木材贴图文件...")

        source_dir = Path(CONFIG["input_paths"]["wood_textures_dir"])
        target_dir = Path(CONFIG["output_paths"]["wood_base_dir"]) / CONFIG["output_paths"]["wood_textures_dir"]

        # 确保目标目录存在
        target_dir.mkdir(parents=True, exist_ok=True)

        copied_count = 0
        if source_dir.exists():
            for texture_file in source_dir.glob("*.jpg"):
                target_file = target_dir / texture_file.name
                shutil.copy2(texture_file, target_file)
                copied_count += 1
                self.log(f"复制木材贴图: {texture_file.name}")

            # 也复制PNG格式的贴图
            for texture_file in source_dir.glob("*.png"):
                target_file = target_dir / texture_file.name
                shutil.copy2(texture_file, target_file)
                copied_count += 1
                self.log(f"复制木材贴图: {texture_file.name}")
        else:
            self.log(f"警告: 木材贴图目录不存在: {source_dir}")

        self.log(f"共复制 {copied_count} 个木材贴图文件")
    
    def import_fbx(self, fbx_path):
        """导入FBX文件"""
        if not Path(fbx_path).exists():
            raise FileNotFoundError(f"FBX文件不存在: {fbx_path}")
        
        self.log(f"导入FBX文件: {fbx_path}")
        
        # 导入FBX文件
        bpy.ops.import_scene.fbx(
            filepath=fbx_path,
            use_manual_orientation=False,
            global_scale=1.0,
            bake_space_transform=False,
            use_custom_normals=True,
            use_image_search=True,
            use_alpha_decals=False,
            decal_offset=0.0,
            use_anim=True,
            anim_offset=1.0,
            use_subsurf=False,
            use_custom_props=True,
            use_custom_props_enum_as_string=True,
            ignore_leaf_bones=False,
            force_connect_children=False,
            automatic_bone_orientation=False,
            primary_bone_axis='Y',
            secondary_bone_axis='X',
            use_prepost_rot=True
        )
        
        self.log(f"FBX导入完成，场景中有 {len(bpy.context.scene.objects)} 个对象")
    
    def export_glb(self, output_path, export_animations=True):
        """导出GLB文件"""
        self.log(f"导出GLB文件: {output_path}")

        # 确保输出目录存在
        Path(output_path).parent.mkdir(parents=True, exist_ok=True)

        # 导出GLB - 使用Blender 4.4.3兼容的参数
        bpy.ops.export_scene.gltf(
            filepath=output_path,
            export_format='GLB',
            ui_tab='GENERAL',
            export_copyright="PlayableGen Team",
            export_image_format='AUTO',
            export_texture_dir="",
            export_keep_originals=False,
            export_texcoords=True,
            export_normals=True,
            export_draco_mesh_compression_enable=False,
            export_draco_mesh_compression_level=6,
            export_draco_position_quantization=14,
            export_draco_normal_quantization=10,
            export_draco_texcoord_quantization=12,
            export_draco_color_quantization=10,
            export_draco_generic_quantization=12,
            export_tangents=False,
            export_materials='EXPORT',
            export_vertex_color='MATERIAL',  # 替换 export_colors
            use_mesh_edges=False,
            use_mesh_vertices=False,
            export_cameras=False,
            use_selection=False,
            use_visible=False,
            use_renderable=False,
            use_active_collection=False,
            export_extras=False,
            export_yup=True,
            export_apply=False,
            export_animations=export_animations,
            export_frame_range=False,  # 改为False，使用默认帧范围
            export_frame_step=1,
            export_force_sampling=True,
            export_nla_strips=True,
            export_def_bones=False,
            export_current_frame=False,
            export_skins=True,
            export_all_influences=False,
            export_morph=True,
            export_morph_normal=True,
            export_morph_tangent=False,
            export_lights=False,
            will_save_settings=False,
            filter_glob="*.glb"
        )

        self.log(f"GLB导出完成: {output_path}")
    
    def convert_character_model(self):
        """转换角色模型"""
        self.log("=== 开始转换角色模型 ===")
        
        # 清空场景
        self.clear_scene()
        
        # 导入角色FBX
        character_fbx = CONFIG["input_paths"]["character_model"]
        self.import_fbx(character_fbx)
        
        # 导出角色GLB
        output_path = Path(CONFIG["output_paths"]["base_dir"]) / CONFIG["output_paths"]["character_glb"]
        self.export_glb(str(output_path), export_animations=False)
        
        self.log("=== 角色模型转换完成 ===")
    
    def find_animation_files(self):
        """查找动画文件"""
        animations_dir = Path(CONFIG["input_paths"]["animations_dir"])
        
        if not animations_dir.exists():
            self.log(f"警告: 动画目录不存在: {animations_dir}")
            return {}
        
        animation_files = {}
        fbx_files = list(animations_dir.glob("*.fbx"))
        
        self.log(f"在 {animations_dir} 中找到 {len(fbx_files)} 个FBX文件")
        
        # 根据文件名映射到标准动画名称
        for fbx_file in fbx_files:
            file_name = fbx_file.stem.lower()
            self.log(f"检查文件: {fbx_file.name} (小写: {file_name})")

            found_match = False
            for standard_name, variations in CONFIG["animation_mapping"].items():
                if found_match:
                    break
                for variation in variations:
                    if variation.lower() in file_name:
                        animation_files[standard_name] = str(fbx_file)
                        self.log(f"映射动画: {fbx_file.name} -> {standard_name}")
                        found_match = True
                        break
        
        return animation_files
    
    def convert_animations(self):
        """转换动画文件"""
        self.log("=== 开始转换动画文件 ===")
        
        animation_files = self.find_animation_files()
        
        if not animation_files:
            self.log("警告: 未找到任何动画文件")
            return
        
        animations_output_dir = Path(CONFIG["output_paths"]["base_dir"]) / CONFIG["output_paths"]["animations_dir"]
        
        for anim_name, fbx_path in animation_files.items():
            self.log(f"转换动画: {anim_name}")
            
            # 清空场景
            self.clear_scene()
            
            # 导入动画FBX
            self.import_fbx(fbx_path)
            
            # 导出动画GLB
            output_path = animations_output_dir / f"{anim_name}.glb"
            self.export_glb(str(output_path), export_animations=True)
        
        self.log("=== 动画转换完成 ===")

    def convert_coin_model(self):
        """转换金币模型"""
        self.log("=== 开始转换金币模型 ===")

        # 清空场景
        self.clear_scene()

        # 导入金币FBX
        coin_fbx = CONFIG["input_paths"]["coin_model"]
        self.import_fbx(coin_fbx)

        # 导出金币GLB
        output_path = Path(CONFIG["output_paths"]["coin_base_dir"]) / CONFIG["output_paths"]["coin_glb"]
        self.export_glb(str(output_path), export_animations=False)

        self.log("=== 金币模型转换完成 ===")

    def convert_jiaban_model(self):
        """转换夹板场景模型"""
        self.log("=== 开始转换夹板场景模型 ===")

        # 清空场景
        self.clear_scene()

        # 导入夹板场景FBX
        jiaban_fbx = CONFIG["input_paths"]["jiaban_model"]
        self.import_fbx(jiaban_fbx)

        # 导出夹板场景GLB
        output_path = Path(CONFIG["output_paths"]["jiaban_base_dir"]) / CONFIG["output_paths"]["jiaban_glb"]
        self.export_glb(str(output_path), export_animations=False)

        self.log("=== 夹板场景模型转换完成 ===")

    def convert_zhalan_model(self):
        """转换围栏模型"""
        self.log("=== 开始转换围栏模型 ===")

        # 清空场景
        self.clear_scene()

        # 导入围栏FBX
        zhalan_fbx = CONFIG["input_paths"]["zhalan_model"]
        self.import_fbx(zhalan_fbx)

        # 导出围栏GLB
        output_path = Path(CONFIG["output_paths"]["zhalan_base_dir"]) / CONFIG["output_paths"]["zhalan_glb"]
        self.export_glb(str(output_path), export_animations=False)

        self.log("=== 围栏模型转换完成 ===")

    def convert_wood_model(self):
        """转换木材模型"""
        self.log("=== 开始转换木材模型 ===")

        # 清空场景
        self.clear_scene()

        # 导入木材FBX
        wood_fbx = CONFIG["input_paths"]["wood_model"]
        self.import_fbx(wood_fbx)

        # 导出木材GLB
        output_path = Path(CONFIG["output_paths"]["wood_base_dir"]) / CONFIG["output_paths"]["wood_glb"]
        self.export_glb(str(output_path), export_animations=False)

        self.log("=== 木材模型转换完成 ===")

    def generate_conversion_report(self):
        """生成转换报告"""
        report_path = Path(CONFIG["output_paths"]["base_dir"]) / "conversion_report.json"
        
        report = {
            "conversion_time": time.time() - self.start_time,
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()),
            "input_paths": CONFIG["input_paths"],
            "output_paths": CONFIG["output_paths"],
            "conversion_log": self.conversion_log,
            "blender_version": bpy.app.version_string,
            "status": "completed"
        }
        
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        self.log(f"转换报告已保存: {report_path}")
    
    def run_conversion(self):
        """执行完整的转换流程"""
        try:
            self.log("=== PlayableGen FBX到GLB转换开始 ===")
            self.log(f"Blender版本: {bpy.app.version_string}")
            
            # 1. 确保输出目录存在
            self.ensure_output_directories()
            
            # 2. 复制贴图文件
            self.copy_textures()

            # 3. 复制金币贴图文件
            self.copy_coin_textures()

            # 4. 复制夹板场景贴图文件
            self.copy_jiaban_textures()

            # 5. 复制围栏贴图文件
            self.copy_zhalan_textures()

            # 6. 复制木材贴图文件
            self.copy_wood_textures()

            # 7. 转换角色模型
            self.convert_character_model()

            # 7. 转换动画文件
            self.convert_animations()

            # 8. 转换金币模型
            self.convert_coin_model()

            # 9. 转换夹板场景模型
            self.convert_jiaban_model()

            # 10. 转换围栏模型
            self.convert_zhalan_model()

            # 11. 转换木材模型
            self.convert_wood_model()

            # 12. 生成转换报告
            self.generate_conversion_report()
            
            total_time = time.time() - self.start_time
            self.log(f"=== 转换完成！总耗时: {total_time:.2f}秒 ===")
            
        except Exception as e:
            self.log(f"转换过程中发生错误: {str(e)}")
            import traceback
            self.log(f"错误详情: {traceback.format_exc()}")
            raise

def main():
    """主函数"""
    converter = FBXToGLBConverter()
    converter.run_conversion()

if __name__ == "__main__":
    main()
