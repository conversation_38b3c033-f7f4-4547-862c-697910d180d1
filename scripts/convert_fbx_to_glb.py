#!/usr/bin/env python3
"""
FBX to GLB converter using Blender Python API
使用Blender将FBX文件转换为GLB格式，保留动画和材质
"""

import bpy
import sys
import os
from pathlib import Path

def clear_scene():
    """清空场景中的所有对象"""
    bpy.ops.object.select_all(action='SELECT')
    bpy.ops.object.delete(use_global=False)
    
    # 清理孤立的数据
    for block in bpy.data.meshes:
        if block.users == 0:
            bpy.data.meshes.remove(block)
    
    for block in bpy.data.materials:
        if block.users == 0:
            bpy.data.materials.remove(block)
    
    for block in bpy.data.textures:
        if block.users == 0:
            bpy.data.textures.remove(block)
    
    for block in bpy.data.images:
        if block.users == 0:
            bpy.data.images.remove(block)

def convert_fbx_to_glb(fbx_path: str, output_path: str) -> bool:
    """
    将FBX文件转换为GLB格式
    
    Args:
        fbx_path: 输入的FBX文件路径
        output_path: 输出的GLB文件路径
        
    Returns:
        bool: 转换是否成功
    """
    try:
        print(f"[FBX转GLB] 开始转换: {fbx_path}")
        
        # 检查输入文件是否存在
        if not os.path.exists(fbx_path):
            print(f"[错误] 输入文件不存在: {fbx_path}")
            return False
        
        # 清空场景
        clear_scene()
        
        # 导入FBX文件
        print(f"[FBX转GLB] 导入FBX文件...")
        try:
            bpy.ops.import_scene.fbx(
                filepath=fbx_path,
                use_custom_normals=True,
                use_image_search=True,
                use_anim=True,
                anim_offset=1.0,
                use_subsurf=False,
                use_custom_props=True,
                ignore_leaf_bones=False,
                global_scale=1.0,
                bake_space_transform=False,
                use_prepost_rot=True
            )
        except TypeError as e:
            # 如果某些参数不支持，使用简化的参数集
            print(f"[FBX转GLB] 使用简化参数重试导入: {e}")
            bpy.ops.import_scene.fbx(
                filepath=fbx_path,
                use_anim=True,
                global_scale=1.0
            )
        
        # 检查是否成功导入了对象
        if len(bpy.context.scene.objects) == 0:
            print(f"[错误] FBX文件导入失败，没有找到任何对象")
            return False
        
        print(f"[FBX转GLB] 成功导入 {len(bpy.context.scene.objects)} 个对象")
        
        # 检查动画
        animation_count = len(bpy.data.actions)
        if animation_count > 0:
            print(f"[FBX转GLB] 发现 {animation_count} 个动画")
        
        # 检查材质
        material_count = len(bpy.data.materials)
        if material_count > 0:
            print(f"[FBX转GLB] 发现 {material_count} 个材质")
        
        # 确保输出目录存在
        output_dir = os.path.dirname(output_path)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir, exist_ok=True)
        
        # 导出为GLB格式
        print(f"[FBX转GLB] 导出GLB文件到: {output_path}")
        try:
            bpy.ops.export_scene.gltf(
                filepath=output_path,
                export_format='GLB',
                export_texcoords=True,
                export_normals=True,
                export_materials='EXPORT',
                export_animations=True,
                export_skins=True,
                export_morph=True,
                export_yup=True
            )
        except TypeError as e:
            # 如果某些参数不支持，使用最基本的参数
            print(f"[FBX转GLB] 使用简化参数重试导出: {e}")
            bpy.ops.export_scene.gltf(
                filepath=output_path,
                export_format='GLB',
                export_animations=True
            )
        
        # 验证输出文件是否创建成功
        if os.path.exists(output_path):
            file_size = os.path.getsize(output_path)
            print(f"[FBX转GLB] 转换成功! 输出文件大小: {file_size / (1024*1024):.2f}MB")
            return True
        else:
            print(f"[错误] GLB文件导出失败")
            return False
            
    except Exception as e:
        print(f"[错误] 转换过程中发生异常: {str(e)}")
        return False

def main():
    """主函数"""
    if len(sys.argv) < 3:
        print("用法: blender --background --python convert_fbx_to_glb.py -- <input.fbx> <output.glb>")
        sys.exit(1)
    
    # 获取命令行参数
    # Blender会在--后面传递自定义参数
    argv = sys.argv
    if "--" in argv:
        argv = argv[argv.index("--") + 1:]
    else:
        print("错误: 请使用 -- 分隔Blender参数和脚本参数")
        sys.exit(1)
    
    if len(argv) < 2:
        print("错误: 需要提供输入和输出文件路径")
        sys.exit(1)
    
    input_fbx = argv[0]
    output_glb = argv[1]
    
    print(f"开始转换FBX文件...")
    print(f"输入文件: {input_fbx}")
    print(f"输出文件: {output_glb}")
    
    # 执行转换
    success = convert_fbx_to_glb(input_fbx, output_glb)
    
    if success:
        print("转换完成!")
        sys.exit(0)
    else:
        print("转换失败!")
        sys.exit(1)

if __name__ == "__main__":
    main() 