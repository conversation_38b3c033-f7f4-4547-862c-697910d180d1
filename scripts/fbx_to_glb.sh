#!/bin/bash

# FBX to GLB Converter Shell Script
# 使用Blender将FBX文件转换为GLB格式

set -e  # 遇到错误时立即退出

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 显示使用方法
show_usage() {
    echo "用法: $0 <input.fbx> [output.glb]"
    echo "示例: $0 model.fbx"
    echo "      $0 model.fbx converted_model.glb"
    exit 1
}

# 检查参数
if [ $# -lt 1 ]; then
    echo -e "${RED}错误: 请提供输入文件${NC}"
    show_usage
fi

INPUT_FILE="$1"

# 检查输入文件是否存在
if [ ! -f "$INPUT_FILE" ]; then
    echo -e "${RED}错误: 输入文件不存在: $INPUT_FILE${NC}"
    exit 1
fi

# 检查文件扩展名
if [[ ! "$INPUT_FILE" =~ \.(fbx|FBX)$ ]]; then
    echo -e "${RED}错误: 输入文件必须是FBX格式${NC}"
    exit 1
fi

# 确定输出文件名
if [ $# -ge 2 ]; then
    OUTPUT_FILE="$2"
else
    # 自动生成输出文件名（将.fbx替换为.glb）
    OUTPUT_FILE="${INPUT_FILE%.*}.glb"
fi

# 获取脚本所在目录
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
CONVERTER_SCRIPT="$SCRIPT_DIR/convert_fbx_to_glb.py"

# 检查转换脚本是否存在
if [ ! -f "$CONVERTER_SCRIPT" ]; then
    echo -e "${RED}错误: 转换脚本不存在: $CONVERTER_SCRIPT${NC}"
    exit 1
fi

# 检查Blender是否安装
if ! command -v blender &> /dev/null; then
    echo -e "${RED}错误: 未找到Blender，请确保已正确安装Blender${NC}"
    exit 1
fi

# 获取绝对路径
INPUT_ABS=$(realpath "$INPUT_FILE")
OUTPUT_ABS=$(realpath "$OUTPUT_FILE")

echo -e "${YELLOW}开始转换FBX文件...${NC}"
echo "输入文件: $INPUT_ABS"
echo "输出文件: $OUTPUT_ABS"
echo ""

# 执行转换
echo -e "${YELLOW}正在转换，请稍候...${NC}"
if blender --background --python "$CONVERTER_SCRIPT" -- "$INPUT_ABS" "$OUTPUT_ABS"; then
    echo ""
    echo -e "${GREEN}✅ 转换成功完成!${NC}"
    
    # 显示文件信息
    if [ -f "$OUTPUT_ABS" ]; then
        FILE_SIZE=$(stat -f%z "$OUTPUT_ABS" 2>/dev/null || stat -c%s "$OUTPUT_ABS" 2>/dev/null)
        FILE_SIZE_MB=$(echo "scale=2; $FILE_SIZE / 1024 / 1024" | bc -l)
        echo "输出文件大小: ${FILE_SIZE_MB}MB"
        echo "GLB文件路径: $OUTPUT_ABS"
    fi
else
    echo ""
    echo -e "${RED}❌ 转换失败!${NC}"
    exit 1
fi 