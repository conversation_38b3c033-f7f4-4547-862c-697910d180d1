<!DOCTYPE html><html lang="zh-CN"><head><meta charset="UTF-8"><meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no"><title>PlayableAgent Game</title><style> * { margin: 0; padding: 0; box-sizing: border-box; } body { overflow: hidden; background: #000; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; touch-action: manipulation; } #gameContainer { width: 100vw; height: 100vh; position: relative; display: flex; align-items: center; justify-content: center; } #gameCanvas { display: block; max-width: 100%; max-height: 100%; background: #000000; } #ui { position: absolute; top: 20px; left: 20px; color: white; z-index: 100; font-size: 14px; text-shadow: 0 1px 2px rgba(0,0,0,0.8); } #cta { position: absolute; bottom: 30px; left: 50%; transform: translateX(-50%); background: linear-gradient(135deg, #ff6b35, #f7931e); color: white; border: none; padding: 16px 32px; font-size: 18px; font-weight: bold; border-radius: 30px; cursor: pointer; z-index: 100; box-shadow: 0 4px 20px rgba(255, 107, 53, 0.4); transition: all 0.3s ease; text-transform: uppercase; letter-spacing: 1px; min-width: 200px; } #cta:hover { background: linear-gradient(135deg, #e55a2b, #e8851d); transform: translateX(-50%) translateY(-3px); box-shadow: 0 6px 25px rgba(255, 107, 53, 0.6); } #cta:active { transform: translateX(-50%) translateY(-1px); box-shadow: 0 2px 10px rgba(255, 107, 53, 0.8); } .loading { position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); color: white; font-size: 18px; z-index: 50; } .loading::after { content: ''; display: inline-block; width: 20px; height: 20px; border: 2px solid #ffffff40; border-top: 2px solid #ffffff; border-radius: 50%; animation: spin 1s linear infinite; margin-left: 10px; } @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } } @media (max-width: 768px) { #ui { font-size: 12px; top: 10px; left: 10px; } #cta { bottom: 20px; padding: 14px 28px; font-size: 16px; min-width: 180px; } } </style></head><body><div id="gameContainer"><div class="loading">加载中...</div><canvas id="gameCanvas"></canvas><div id="ui" style="display: none;"><div>🎮 点击拖拽控制视角</div><div>⌨️ WASD键移动角色</div></div><button id="cta" onclick="handleCTA()" style="display: none;"> 🎯 立即下载游戏 </button></div><script> // 场景数据 const SCENE_DATA = { "nodes": { "ground": { "position": { "x": 0, "y": 0, "z": 0 }, "rotation": { "x": -1.5707963267948966, "y": 0, "z": 0 }, "scale": { "x": 1, "y": 1, "z": 1 }, "color": "#808080" }, "player_box": { "position": { "x": 2, "y": 0, "z": 0 }, "rotation": { "x": 0, "y": 0, "z": 0 }, "scale": { "x": 0.015, "y": 0.015, "z": 0.015 }, "color": "#3366ff" }, "directional_light": { "position": { "x": 1, "y": 2, "z": 3 }, "rotation": { "x": 0, "y": 0, "z": 0 }, "scale": { "x": 1, "y": 1, "z": 1 }, "color": "#00ff00" }, "main_camera": { "position": { "x": 0, "y": 15, "z": 10 }, "rotation": { "x": -0.4636476090008063, "y": 0, "z": 0 }, "scale": { "x": 1, "y": 1, "z": 1 }, "color": "#00ff00" }, "box_1753425549287": { "position": { "x": 0, "y": 0, "z": 0 }, "rotation": { "x": 0, "y": 0, "z": 0 }, "scale": { "x": 0.02, "y": 0.02, "z": 0.02 }, "color": "#cee9fe" } }, "scripts": [ { "id": "script_1753603324516", "name": "增加通过wasd可以控制主角上下左右移动的逻辑", "content": "function executeScript(scene, camera, renderer) {\n // 获取主角对象\n const player = scene.getObjectByName('box_1753425549287');\n if (!player) {\n console.warn('Player object not found');\n return;\n }\n\n // 键盘状态追踪\n const keys = {\n w: false,\n a: false,\n s: false,\n d: false\n };\n\n // 移动速度（单位/秒）\n const moveSpeed = 5;\n let lastTime = performance.now();\n let animationId;\n\n // 键盘事件处理\n function onKeyDown(event) {\n event.preventDefault();\n const key = event.key.toLowerCase();\n if (keys.hasOwnProperty(key)) {\n keys[key] = true;\n }\n }\n\n function onKeyUp(event) {\n event.preventDefault();\n const key = event.key.toLowerCase();\n if (keys.hasOwnProperty(key)) {\n keys[key] = false;\n }\n }\n\n // 更新主角位置\n function updatePlayer(deltaTime) {\n const moveDistance = moveSpeed * deltaTime;\n \n if (keys.w) player.position.z -= moveDistance; // 向前\n if (keys.s) player.position.z += moveDistance; // 向后\n if (keys.a) player.position.x -= moveDistance; // 向左\n if (keys.d) player.position.x += moveDistance; // 向右\n }\n\n // 动画循环\n function animate() {\n const currentTime = performance.now();\n const deltaTime = (currentTime - lastTime) / 1000;\n lastTime = currentTime;\n\n updatePlayer(deltaTime);\n \n animationId = requestAnimationFrame(animate);\n }\n\n // 添加事件监听器\n document.addEventListener('keydown', onKeyDown);\n document.addEventListener('keyup', onKeyUp);\n\n // 开始动画\n animate();\n\n return function cleanup() {\n // 清理事件监听器\n document.removeEventListener('keydown', onKeyDown);\n document.removeEventListener('keyup', onKeyUp);\n \n // 停止动画循环\n if (animationId) {\n cancelAnimationFrame(animationId);\n }\n };\n}", "isActive": true } ], "camera": { "position": [ 0.015778277833036973, 14.968517316102922, 10.047051328796734 ], "target": [ 0, 0, 0 ] }, "lights": [ { "type": "AmbientLight", "color": 4210752, "intensity": 0.4, "position": [ 0, 0, 0 ] }, { "type": "DirectionalLight", "color": 16777215, "intensity": 1, "position": [ 10, 10, 5 ] }, { "type": "AmbientLight", "color": 16777215, "intensity": 0.4, "position": [ 0, 0, 0 ] }, { "type": "DirectionalLight", "color": 16777215, "intensity": 1, "position": [ 1, 2, 3 ] } ], "background": "#000000" }; // Three.js库 // Three.js 备用实现（简化版本） console.log('使用Three.js备用实现'); window.THREE = { REVISION: '178', // 场景类 Scene: function() { this.children = []; this.background = null; this.fog = null; this.add = function(object) { this.children.push(object); object.parent = this; }; this.remove = function(object) { const index = this.children.indexOf(object); if (index > -1) { this.children.splice(index, 1); object.parent = null; } }; this.traverse = function(callback) { callback(this); for (let i = 0; i < this.children.length; i++) { if (this.children[i].traverse) { this.children[i].traverse(callback); } } }; }, // 透视相机 PerspectiveCamera: function(fov, aspect, near, far) { this.fov = fov || 75; this.aspect = aspect || 1; this.near = near || 0.1; this.far = far || 1000; this.position = { x: 0, y: 0, z: 5 }; this.rotation = { x: 0, y: 0, z: 0 }; this.position.set = function(x, y, z) { this.x = x; this.y = y; this.z = z; }; this.updateProjectionMatrix = function() { // 简化的投影矩阵更新 }; }, // WebGL渲染器 WebGLRenderer: function(options) { options = options || {}; this.domElement = options.canvas || document.createElement('canvas'); this.shadowMap = { enabled: false, type: null }; this.setSize = function(width, height) { this.domElement.width = width; this.domElement.height = height; this.domElement.style.width = width + 'px'; this.domElement.style.height = height + 'px'; }; this.render = function(scene, camera) { // 简化的渲染实现 const ctx = this.domElement.getContext('2d'); if (ctx) { // 清除画布 ctx.fillStyle = scene.background || '#000000'; ctx.fillRect(0, 0, this.domElement.width, this.domElement.height); // 简单的3D效果模拟 ctx.fillStyle = '#00ff00'; ctx.fillRect( this.domElement.width / 2 - 25, this.domElement.height / 2 - 25, 50, 50 ); // 添加一些动态效果 const time = Date.now() * 0.001; ctx.fillStyle = '#ff6b35'; ctx.beginPath(); ctx.arc( this.domElement.width / 2 + Math.sin(time) * 100, this.domElement.height / 2 + Math.cos(time) * 50, 20, 0, Math.PI * 2 ); ctx.fill(); } }; }, // 几何体 BoxGeometry: function(width, height, depth) { this.width = width || 1; this.height = height || 1; this.depth = depth || 1; this.type = 'BoxGeometry'; }, PlaneGeometry: function(width, height) { this.width = width || 1; this.height = height || 1; this.type = 'PlaneGeometry'; }, // 材质 MeshPhongMaterial: function(parameters) { parameters = parameters || {}; this.color = parameters.color || 0x00ff00; this.type = 'MeshPhongMaterial'; }, MeshBasicMaterial: function(parameters) { parameters = parameters || {}; this.color = parameters.color || 0x00ff00; this.type = 'MeshBasicMaterial'; }, // 网格 Mesh: function(geometry, material) { this.geometry = geometry; this.material = material; this.position = { x: 0, y: 0, z: 0 }; this.rotation = { x: 0, y: 0, z: 0 }; this.scale = { x: 1, y: 1, z: 1 }; this.castShadow = false; this.receiveShadow = false; this.parent = null; this.position.set = function(x, y, z) { this.x = x; this.y = y; this.z = z; }; this.rotation.set = function(x, y, z) { this.x = x; this.y = y; this.z = z; }; this.scale.set = function(x, y, z) { this.x = x; this.y = y; this.z = z; }; }, // 光照 AmbientLight: function(color, intensity) { this.color = { getHex: function() { return color || 0xffffff; } }; this.intensity = intensity || 1; this.type = 'AmbientLight'; this.isLight = true; }, DirectionalLight: function(color, intensity) { this.color = { getHex: function() { return color || 0xffffff; } }; this.intensity = intensity || 1; this.type = 'DirectionalLight'; this.position = { x: 0, y: 1, z: 0 }; this.target = { position: { x: 0, y: 0, z: 0 } }; this.castShadow = false; this.isLight = true; this.position.set = function(x, y, z) { this.x = x; this.y = y; this.z = z; }; }, // 颜色 Color: function(color) { this.r = 1; this.g = 1; this.b = 1; if (typeof color === 'string') { this.setHex(color); } else if (typeof color === 'number') { this.setHex(color); } this.setHex = function(hex) { if (typeof hex === 'string') { hex = parseInt(hex.replace('#', ''), 16); } this.r = ((hex >> 16) & 255) / 255; this.g = ((hex >> 8) & 255) / 255; this.b = (hex & 255) / 255; }; this.getHexString = function() { const r = Math.round(this.r * 255); const g = Math.round(this.g * 255); const b = Math.round(this.b * 255); return ((r << 16) | (g << 8) | b).toString(16).padStart(6, '0'); }; }, // 常量 PCFSoftShadowMap: 'PCFSoftShadowMap' }; console.log('Three.js 备用实现已加载'); // 场景初始化 // 全局变量 let scene, camera, renderer, gameObjects = {}; let animationId; // 场景初始化函数 function startGame() { console.log('开始初始化游戏场景'); try { // 创建场景 scene = new THREE.Scene(); scene.background = new THREE.Color('#000000'); // 创建相机 camera = new THREE.PerspectiveCamera( 75, window.innerWidth / window.innerHeight, 0.1, 1000 ); camera.position.set(0.015778277833036973, 14.968517316102922, 10.047051328796734); // 创建渲染器 const canvas = document.getElementById('gameCanvas'); renderer = new THREE.WebGLRenderer({ canvas: canvas, antialias: true, alpha: true }); renderer.setSize(window.innerWidth, window.innerHeight); renderer.shadowMap.enabled = true; renderer.shadowMap.type = THREE.PCFSoftShadowMap; // 创建光照 const ambientLight_0 = new THREE.AmbientLight(0x404040, 0.4); scene.add(ambientLight_0); const directionalLight_1 = new THREE.DirectionalLight(0xffffff, 1); directionalLight_1.position.set(10, 10, 5); directionalLight_1.castShadow = true; scene.add(directionalLight_1); const ambientLight_2 = new THREE.AmbientLight(0xffffff, 0.4); scene.add(ambientLight_2); const directionalLight_3 = new THREE.DirectionalLight(0xffffff, 1); directionalLight_3.position.set(1, 2, 3); directionalLight_3.castShadow = true; scene.add(directionalLight_3); // 创建场景对象 // 创建节点: ground const geometry_ground = new THREE.BoxGeometry(1, 1, 1); const material_ground = new THREE.MeshPhongMaterial({ color: 0x808080 }); const mesh_ground = new THREE.Mesh(geometry_ground, material_ground); mesh_ground.position.set(0, 0, 0); mesh_ground.rotation.set(-1.5707963267948966, 0, 0); mesh_ground.castShadow = true; mesh_ground.receiveShadow = true; scene.add(mesh_ground); gameObjects['ground'] = mesh_ground; // 创建节点: player_box const geometry_player_box = new THREE.BoxGeometry(0.015, 0.015, 0.015); const material_player_box = new THREE.MeshPhongMaterial({ color: 0x3366ff }); const mesh_player_box = new THREE.Mesh(geometry_player_box, material_player_box); mesh_player_box.position.set(2, 0, 0); mesh_player_box.rotation.set(0, 0, 0); mesh_player_box.castShadow = true; mesh_player_box.receiveShadow = true; scene.add(mesh_player_box); gameObjects['player_box'] = mesh_player_box; // 创建节点: directional_light const geometry_directional_light = new THREE.BoxGeometry(1, 1, 1); const material_directional_light = new THREE.MeshPhongMaterial({ color: 0x00ff00 }); const mesh_directional_light = new THREE.Mesh(geometry_directional_light, material_directional_light); mesh_directional_light.position.set(1, 2, 3); mesh_directional_light.rotation.set(0, 0, 0); mesh_directional_light.castShadow = true; mesh_directional_light.receiveShadow = true; scene.add(mesh_directional_light); gameObjects['directional_light'] = mesh_directional_light; // 创建节点: main_camera const geometry_main_camera = new THREE.BoxGeometry(1, 1, 1); const material_main_camera = new THREE.MeshPhongMaterial({ color: 0x00ff00 }); const mesh_main_camera = new THREE.Mesh(geometry_main_camera, material_main_camera); mesh_main_camera.position.set(0, 15, 10); mesh_main_camera.rotation.set(-0.4636476090008063, 0, 0); mesh_main_camera.castShadow = true; mesh_main_camera.receiveShadow = true; scene.add(mesh_main_camera); gameObjects['main_camera'] = mesh_main_camera; // 创建节点: box_1753425549287 const geometry_box_1753425549287 = new THREE.BoxGeometry(0.02, 0.02, 0.02); const material_box_1753425549287 = new THREE.MeshPhongMaterial({ color: 0xcee9fe }); const mesh_box_1753425549287 = new THREE.Mesh(geometry_box_1753425549287, material_box_1753425549287); mesh_box_1753425549287.position.set(0, 0, 0); mesh_box_1753425549287.rotation.set(0, 0, 0); mesh_box_1753425549287.castShadow = true; mesh_box_1753425549287.receiveShadow = true; scene.add(mesh_box_1753425549287); gameObjects['box_1753425549287'] = mesh_box_1753425549287; // 执行脚本 // 执行脚本: 增加通过wasd可以控制主角上下左右移动的逻辑 try { (function() { // 提供脚本执行环境 const threeScene = scene; const threeCamera = camera; const threeRenderer = renderer; const objects = gameObjects; // 执行脚本内容 function executeScript(scene, camera, renderer) { // 获取主角对象 const player = scene.getObjectByName('box_1753425549287'); if (!player) { console.warn('Player object not found'); return; } // 键盘状态追踪 const keys = { w: false, a: false, s: false, d: false }; // 移动速度（单位/秒） const moveSpeed = 5; let lastTime = performance.now(); let animationId; // 键盘事件处理 function onKeyDown(event) { event.preventDefault(); const key = event.key.toLowerCase(); if (keys.hasOwnProperty(key)) { keys[key] = true; } } function onKeyUp(event) { event.preventDefault(); const key = event.key.toLowerCase(); if (keys.hasOwnProperty(key)) { keys[key] = false; } } // 更新主角位置 function updatePlayer(deltaTime) { const moveDistance = moveSpeed * deltaTime; if (keys.w) player.position.z -= moveDistance; // 向前 if (keys.s) player.position.z += moveDistance; // 向后 if (keys.a) player.position.x -= moveDistance; // 向左 if (keys.d) player.position.x += moveDistance; // 向右 } // 动画循环 function animate() { const currentTime = performance.now(); const deltaTime = (currentTime - lastTime) / 1000; lastTime = currentTime; updatePlayer(deltaTime); animationId = requestAnimationFrame(animate); } // 添加事件监听器 document.addEventListener('keydown', onKeyDown); document.addEventListener('keyup', onKeyUp); // 开始动画 animate(); return function cleanup() { // 清理事件监听器 document.removeEventListener('keydown', onKeyDown); document.removeEventListener('keyup', onKeyUp); // 停止动画循环 if (animationId) { cancelAnimationFrame(animationId); } }; } })(); } catch (error) { console.error('脚本执行错误 (增加通过wasd可以控制主角上下左右移动的逻辑):', error); } // 开始渲染循环 animate(); // 处理窗口大小变化 window.addEventListener('resize', onWindowResize); console.log('游戏场景初始化完成'); } catch (error) { console.error('场景初始化失败:', error); } } // 渲染循环 function animate() { animationId = requestAnimationFrame(animate); // 简单的动画效果 const time = Date.now() * 0.001; // 旋转场景中的对象 Object.values(gameObjects).forEach((obj, index) => { if (obj && obj.rotation) { obj.rotation.y = time + index * 0.5; } }); // 渲染场景 if (renderer && scene && camera) { renderer.render(scene, camera); } } // 窗口大小变化处理 function onWindowResize() { if (camera && renderer) { camera.aspect = window.innerWidth / window.innerHeight; camera.updateProjectionMatrix(); renderer.setSize(window.innerWidth, window.innerHeight); } } // 清理函数 function cleanup() { if (animationId) { cancelAnimationFrame(animationId); } } // 页面卸载时清理 window.addEventListener('beforeunload', cleanup); // 交互逻辑 // 交互系统初始化 console.log('交互系统已初始化'); // 键盘控制状态 const keys = {}; let mouseDown = false; let mousePos = { x: 0, y: 0 }; let lastMousePos = { x: 0, y: 0 }; // 键盘事件监听 document.addEventListener('keydown', (event) => { keys[event.code] = true; // WASD控制示例 if (gameObjects.box) { const speed = 0.1; switch(event.code) { case 'KeyW': gameObjects.box.position.z -= speed; break; case 'KeyS': gameObjects.box.position.z += speed; break; case 'KeyA': gameObjects.box.position.x -= speed; break; case 'KeyD': gameObjects.box.position.x += speed; break; } } event.preventDefault(); }); document.addEventListener('keyup', (event) => { keys[event.code] = false; }); // 鼠标控制 document.addEventListener('mousedown', (event) => { mouseDown = true; lastMousePos.x = event.clientX; lastMousePos.y = event.clientY; }); document.addEventListener('mousemove', (event) => { mousePos.x = event.clientX; mousePos.y = event.clientY; if (mouseDown && camera) { const deltaX = event.clientX - lastMousePos.x; const deltaY = event.clientY - lastMousePos.y; // 简单的相机旋转控制 camera.position.x += deltaX * 0.01; camera.position.y -= deltaY * 0.01; lastMousePos.x = event.clientX; lastMousePos.y = event.clientY; } }); document.addEventListener('mouseup', () => { mouseDown = false; }); // 触摸控制（移动设备） let touchStart = { x: 0, y: 0 }; let touchDown = false; document.addEventListener('touchstart', (event) => { event.preventDefault(); touchDown = true; touchStart.x = event.touches[0].clientX; touchStart.y = event.touches[0].clientY; }, { passive: false }); document.addEventListener('touchmove', (event) => { event.preventDefault(); if (touchDown && event.touches.length > 0) { const touchX = event.touches[0].clientX; const touchY = event.touches[0].clientY; const deltaX = touchX - touchStart.x; const deltaY = touchY - touchStart.y; // 触摸控制逻辑 if (Math.abs(deltaX) > 30 || Math.abs(deltaY) > 30) { if (gameObjects.box) { const speed = 0.05; gameObjects.box.position.x += deltaX * speed * 0.01; gameObjects.box.position.z += deltaY * speed * 0.01; } touchStart.x = touchX; touchStart.y = touchY; } } }, { passive: false }); document.addEventListener('touchend', (event) => { event.preventDefault(); touchDown = false; }, { passive: false }); // 点击/触摸CTA按钮的特殊效果 const ctaButton = document.getElementById('cta'); if (ctaButton) { ctaButton.addEventListener('click', () => { // 添加点击效果 if (gameObjects.box) { gameObjects.box.scale.set(1.2, 1.2, 1.2); setTimeout(() => { if (gameObjects.box) { gameObjects.box.scale.set(1, 1, 1); } }, 200); } }); } // 防止页面滚动和缩放 document.addEventListener('touchmove', (event) => { if (event.target === document.body || event.target === document.documentElement) { event.preventDefault(); } }, { passive: false }); document.addEventListener('gesturestart', (event) => { event.preventDefault(); }); document.addEventListener('gesturechange', (event) => { event.preventDefault(); }); document.addEventListener('gestureend', (event) => { event.preventDefault(); }); // CTA处理函数 function handleCTA() { // 广告平台集成 try { // MRAID 2.0 支持 if (typeof mraid !== 'undefined' && mraid.open) { mraid.open('https://play.google.com/store/apps/details?id=your.app.id'); return; } // Google Ads ExitAPI if (typeof ExitApi !== 'undefined' && ExitApi.exit) { ExitApi.exit(); return; } // Facebook Playable Ads if (typeof FbPlayableAd !== 'undefined' && FbPlayableAd.onCTAClick) { FbPlayableAd.onCTAClick(); return; } // Unity Ads if (typeof gameReady !== 'undefined') { gameReady(); return; } // 备用方案 window.op