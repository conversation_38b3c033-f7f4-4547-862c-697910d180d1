{"name": "my-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "test:playwright": "playwright test", "test:playwright:ui": "playwright test --ui", "test:playwright:headed": "playwright test --headed"}, "dependencies": {"three": "^0.178.0", "@types/three": "^0.178.1", "@langchain/anthropic": "^0.3.24", "@langchain/core": "^0.3.62", "@langchain/langgraph": "^0.3.6", "@langchain/langgraph-sdk": "^0.0.89", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@types/cannon": "^0.1.13", "@types/react-syntax-highlighter": "^15.5.13", "@types/ws": "^8.18.1", "cannon": "^0.6.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.525.0", "next": "15.2.4", "openai": "^5.8.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-markdown": "^10.1.0", "react-syntax-highlighter": "^15.6.1", "tailwind-merge": "^3.3.1", "ws": "^8.18.3", "zod": "^3.25.74"}, "devDependencies": {"@eslint/eslintrc": "^3", "@stagewise-plugins/react": "^0.6.2", "@stagewise/toolbar-next": "^0.6.2", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.2.4", "tailwindcss": "^4", "ts-node": "^10.9.2", "tw-animate-css": "^1.3.5", "typescript": "^5"}}