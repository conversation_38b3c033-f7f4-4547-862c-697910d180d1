---
globs: *.tsx,*.ts
alwaysApply: false
---
# TypeScript 最佳编程规范 - Cursor Rules

## 核心原则

在生成或修改 TypeScript 相关内容时，严格遵循以下编程规范和最佳实践：

## 1. 类型安全 - Type Safety First

### 1.1 禁止使用 `any` 类型
- **永远不要使用 `any` 类型**，这违背了 TypeScript 的核心价值
- 使用 `unknown` 替代 `any` 进行更安全的类型处理
- 为所有变量、函数参数和返回值提供明确的类型注解

```typescript
// ❌ 错误的做法
function processData(data: any): any {
  return data;
}

// ✅ 正确的做法
function processData(data: unknown): string {
  if (typeof data === 'string') {
    return data.toUpperCase();
  }
  throw new Error('Invalid data type');
}
```

### 1.2 明确类型注解
- 显式声明函数参数、返回类型和复杂变量类型
- 利用 TypeScript 的类型推断，但在复杂情况下要明确注解
- 使用 `const` 断言 (`as const`) 确保字面量类型的精确性

```typescript
// ✅ 推荐的做法
const roles = ['admin', 'user', 'guest'] as const;
type Role = typeof roles[number]; // 'admin' | 'user' | 'guest'

function greet(name: string): string {
  return `Hello, ${name}!`;
}
```

### 1.3 启用严格模式
- 在 `tsconfig.json` 中启用 `strict: true`
- 启用 `strictNullChecks`、`strictPropertyInitialization`、`noImplicitAny`

## 2. 接口和类型定义

### 2.1 接口优于类型别名
- 对于对象形状使用 `interface`
- 对于联合类型、交叉类型和复杂类型使用 `type`
- 接口名称不使用 `I` 前缀

```typescript
// ✅ 正确的做法
interface User {
  id: number;
  name: string;
  email: string;
}

type ID = number | string;
type UserRole = 'admin' | 'user' | 'guest';
```

### 2.2 利用工具类型
- 使用内置工具类型：`Partial`、`Readonly`、`Pick`、`Omit`、`Record`
- 创建自定义工具类型来处理特定的数据转换

```typescript
// ✅ 使用工具类型
type UpdateUser = Partial<User>;
type UserProfile = Pick<User, 'name' | 'email'>;
type UserPermissions = Record<string, boolean>;
```

## 3. 命名规范

### 3.1 命名约定
- **类型名称**：PascalCase (User, UserProfile)
- **接口名称**：PascalCase，不使用 `I` 前缀
- **枚举值**：PascalCase
- **函数名称**：camelCase
- **属性名称**：camelCase
- **局部变量**：camelCase
- **常量**：UPPER_SNAKE_CASE 或 camelCase (使用 const)
- **私有属性**：不使用 `_` 前缀

```typescript
// ✅ 正确的命名
interface User {
  userId: number;
  userName: string;
}

enum UserRole {
  Admin = 'admin',
  User = 'user',
  Guest = 'guest'
}

const MAX_RETRY_COUNT = 3;
const userService = new UserService();
```

## 4. 函数设计

### 4.1 函数最佳实践
- 保持函数小而专注，单一职责
- 使用纯函数，避免副作用
- 明确的参数类型和返回类型
- 优先使用 `async/await` 而非 Promise 链

```typescript
// ✅ 推荐的函数设计
async function fetchUser(userId: number): Promise<User> {
  try {
    const response = await fetch(`/api/users/${userId}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch user: ${response.status}`);
    }
    return await response.json();
  } catch (error) {
    console.error('Failed to fetch user:', error);
    throw error;
  }
}
```

### 4.2 错误处理
- 创建自定义错误类型
- 使用类型安全的错误处理
- 避免使用 `any` 类型处理错误

```typescript
// ✅ 类型安全的错误处理
class ValidationError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'ValidationError';
  }
}

function validateEmail(email: string): void {
  if (!email.includes('@')) {
    throw new ValidationError('Invalid email format');
  }
}
```

## 5. 现代 TypeScript 特性

### 5.1 模板字面量类型
- 使用模板字面量类型创建动态字符串类型

```typescript
// ✅ 模板字面量类型
type Endpoint = `api/${'users' | 'posts' | 'comments'}`;
type ColorCode = `#${string}`;
```

### 5.2 可选链和空值合并
- 使用可选链 (`?.`) 和空值合并 (`??`) 操作符

```typescript
// ✅ 现代语法
const cityName = user?.address?.city ?? 'Unknown City';
```

### 5.3 satisfies 操作符
- 使用 `satisfies` 进行类型约束而保持推断

```typescript
// ✅ 使用 satisfies
const config = {
  apiUrl: 'https://api.example.com',
  timeout: 5000,
} satisfies { apiUrl: string; timeout: number };
```

## 6. 代码组织

### 6.1 模块化
- 使用 ES 模块进行代码组织
- 每个文件代表一个逻辑组件
- 使用 barrel 文件简化导入

```typescript
// ✅ 模块化示例
// user.ts
export interface User {
  id: number;
  name: string;
}

// userService.ts
import type { User } from './user';
export class UserService {
  async getUser(id: number): Promise<User> {
    // 实现
  }
}

// index.ts (barrel file)
export * from './user';
export * from './userService';
```

### 6.2 文件结构
- 类型定义在文件顶部
- 单个文件不超过 500 行代码
- 按功能模块拆分文件

## 7. 枚举和联合类型

### 7.1 字符串枚举优于数字枚举
- 使用字符串枚举提高可读性和调试性
- 对于简单状态，考虑使用联合类型

```typescript
// ✅ 字符串枚举
enum Status {
  Pending = 'pending',
  Approved = 'approved',
  Rejected = 'rejected'
}

// ✅ 或使用联合类型
type Status = 'pending' | 'approved' | 'rejected';
```

## 8. 代码风格

### 8.1 格式化规则
- 使用 2 或 4 个空格缩进（保持一致）
- 行长度限制在 100-120 字符
- 使用双引号表示字符串
- 语句末尾使用分号
- 大括号采用 1TBS 风格

### 8.2 箭头函数
- 优先使用箭头函数而非匿名函数表达式
- 必要时才使用括号包围参数

```typescript
// ✅ 箭头函数最佳实践
const add = (a: number, b: number): number => a + b;
const users = data.map(item => processUser(item));
const validator = <T>(value: T, predicate: (v: T) => boolean): boolean => predicate(value);
```

## 9. 注释和文档

### 9.1 JSDoc 注释
- 使用 JSDoc 为公共 API 编写文档
- 解释"为什么"而不是"什么"
- 保持注释简洁有用

```typescript
// ✅ 良好的 JSDoc 注释
/**
 * 计算用户的年龄根据生日
 * @param birthDate - 用户生日的 ISO 字符串
 * @returns 用户当前年龄
 */
function calculateAge(birthDate: string): number {
  const birth = new Date(birthDate);
  const today = new Date();
  let age = today.getFullYear() - birth.getFullYear();
  
  if (today.getMonth() < birth.getMonth() || 
      (today.getMonth() === birth.getMonth() && today.getDate() < birth.getDate())) {
    age--;
  }
  
  return age;
}
```

## 10. ESLint 配置

### 10.1 推荐的 ESLint 规则
确保项目使用以下 TypeScript ESLint 配置：

```javascript
// eslint.config.mjs
import tseslint from 'typescript-eslint';

export default tseslint.config(
  tseslint.configs.strictTypeChecked,
  tseslint.configs.stylisticTypeChecked,
  {
    rules: {
      '@typescript-eslint/no-explicit-any': 'error',
      '@typescript-eslint/no-unused-vars': 'error',
      '@typescript-eslint/prefer-nullish-coalescing': 'error',
      '@typescript-eslint/prefer-optional-chain': 'error',
      '@typescript-eslint/strict-boolean-expressions': 'error',
      '@typescript-eslint/no-non-null-assertion': 'warn',
    }
  }
);
```

## 11. 性能优化

### 11.1 类型优化
- 使用稳定的对象形状，避免运行时动态属性更改
- 避免过度复杂的泛型，保持类型简洁
- 使用工具类型进行类型转换而非手动定义

### 11.2 代码分割
- 使用动态导入进行代码分割
- 避免循环依赖

```typescript
// ✅ 动态导入
const loadModule = async () => {
  const { heavy ModuleFunction } = await import('./heavyModule');
  return heavyModuleFunction();
};
```

## 12. 测试相关

### 12.1 类型安全测试
- 为测试代码同样应用类型安全规则
- 使用类型化的测试工具和断言

```typescript
// ✅ 类型安全的测试
interface TestUser extends User {
  password: string;
}

const createTestUser = (overrides: Partial<TestUser> = {}): TestUser => ({
  id: 1,
  name: 'Test User',
  email: '<EMAIL>',
  password: 'password123',
  ...overrides,
});
```

## 总结

这些规则旨在确保 TypeScript 代码的类型安全、可读性、可维护性和性能。特别强调：

1. **绝对禁止使用 `any` 类型**
2. **始终使用明确的类型注解**
3. **优先考虑类型安全而非便利性**
4. **遵循一致的命名和代码风格**
5. **利用 TypeScript 的现代特性**
6. **保持代码模块化和可测试性**

在生成或修改任何 TypeScript 代码时，请严格遵循这些规范。 